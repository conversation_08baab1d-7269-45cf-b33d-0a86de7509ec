# Countryside Vue

基于 Vue 3 + TypeScript + Vite + Element Plus 的现代化前端项目。

## 技术栈

- **核心框架：** Vue 3.4.x
- **开发语言：** TypeScript 5.5.x
- **构建工具：** Vite 5.4.x
- **UI 框架：** Element Plus 2.8.x
- **状态管理：** Pinia 2.2.x
- **路由管理：** Vue Router 4.4.x
- **HTTP 客户端：** Axios 1.7.x
- **工具库：**
  - @vueuse/core - Vue Composition API 工具集
  - dayjs - 日期处理
  - echarts - 图表库
  - file-saver - 文件下载
  - js-cookie - Cookie 处理
  - mitt - 事件总线
  - qs - 查询字符串解析
  - sortablejs - 拖拽排序

## 项目特性

- 🚀 基于 Vue 3 + TypeScript + Vite 开发
- 📦 支持 WebRTC 实时音视频通讯
- 🎨 集成 Element Plus 组件库
- 📊 集成 ECharts 图表
- 🔒 完善的权限管理系统
- 🌐 多环境配置支持
- 📱 响应式设计
- 🛠 丰富的开发工具和规范配置

## 目录结构

```bash
├── build                   # 构建相关配置
├── mock                    # 数据mock
├── public                  # 静态资源
├── src                     # 源代码
│   ├── api                # API 接口
│   ├── assets             # 主题 字体等静态资源
│   ├── components         # 全局公用组件
│   ├── config             # 全局配置
│   ├── directives         # 全局指令
│   ├── hooks              # 全局 hooks
│   ├── layout             # 全局布局
│   ├── plugins            # 插件
│   ├── router            # 路由
│   ├── store             # 全局状态管理
│   ├── style             # 全局样式
│   ├── types             # 类型声明
│   ├── utils             # 全局工具方法
│   ├── views             # 所有页面
│   ├── App.vue          # 入口页面
│   └── main.ts          # 入口文件
├── types                  # 全局类型声明
├── .env                   # 环境变量
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── .env.staging          # 预发布环境变量
├── vite.config.ts        # Vite 配置
├── tsconfig.json         # TypeScript 配置
└── package.json          # 项目依赖
```

## 页面说明

项目包含以下主要页面模块：

### 总览 (`views/overview/`)

- 路由: `/overview`
- 系统数据总览和统计页面
- 排序等级: 0

### 数据大屏 (`views/large-screen/`)

- 路由: `/large-screen`
- 数据可视化大屏展示页面
- 排序等级: 1

### 系统设置 (`views/system/`)

- 路由: `/system`
- 系统配置和管理相关页面
- 排序等级: 5
- 子页面:
  - 用户管理 (`/system/user`)
  - 角色管理 (`/system/system-role`)
  - 设备管理 (`/system/system-device`)
  - 系统日志 (`/system/system-log`)
  - 系统配置 (`/system/system-setting`)

### 劝导员管理 (`views/counselor/`)

- 路由: `/counselor`
- 劝导员工作管理相关页面
- 排序等级: 5
- 子页面:
  - 违法处理 (`/illegal/log`)
  - 违法分派 (`/illegal/dispatch`)
  - 考勤列表 (`/attendance-management`)
  - 排班列表 (`/schedule-management`)
  - 人脸审核 (`/face-review`)
  - 请假审核 (`/examine-leave`)

### 其他页面

- 登录页面 (`views/login/`)

  - 路由: `/login`
  - 用户登录和认证页面
  - 排序等级: 101

- 错误页面 (`views/error/`)
  - 403 页面 (`/403`)
  - 404 页面 (`/404`)
  - 500 页面 (`/500`)

### 特殊路由

- 重定向页面 (`/redirect/:path(.*)`)
  - 用于页面重定向处理
  - 排序等级: 102

## 开发规范

- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 使用 Stylelint 进行样式检查
- 使用 commitlint 进行提交信息规范化
- 使用 husky 进行 Git hooks 管理

## 环境要求

- Node.js: ^18.18.0 || ^20.9.0 || >=21.1.0
- 包管理器: pnpm >= 9

## 开发命令

```bash

# 安装依赖

pnpm install

# 启动开发服务器

pnpm dev

# 构建生产版本

pnpm build

# 预览生产构建

pnpm preview

# 代码检查

pnpm lint

# 类型检查

pnpm typecheck

# 清理缓存

pnpm clean:cache
```

## WebRTC 组件

项目包含一个基于 WebRTC 的实时音频通讯组件，支持以下功能：

- 实时语音通话
- 自动重连机制
- 心跳检测
- 麦克风设备检测
- 静音控制
- ICE 穿透支持（STUN/TURN）

## 环境配置

项目支持多环境配置：

- 开发环境（development）
- 生产环境（production）
- 预发布环境（staging）

通过不同的 .env 文件进行配置管理。

## 代码提交规范

使用 commitlint 进行提交信息规范化，支持以下类型：

- feat: 新功能
- fix: 修复
- docs: 文档变更
- style: 代码格式
- refactor: 重构
- perf: 性能优化
- test: 增加测试
- chore: 构建过程或辅助工具的变动

## 浏览器支持

- 现代浏览器
- Chrome >= 87
- Firefox >= 78
- Safari >= 13
- Edge >= 88

## 许可证

[MIT](LICENSE)
