import { createVNode, render } from "vue";
import { ElImageViewer } from "element-plus";

export function usePreviewImg() {
  let container: HTMLElement | null = null;

  const onPreviewImg = (urlList: string[], index = 0) => {
    // 清理之前的实例
    if (container) {
      document.body.removeChild(container);
    }

    // 创建新容器
    container = document.createElement("div");
    document.body.appendChild(container);

    const vnode = createVNode(ElImageViewer, {
      urlList,
      initialIndex: index,
      onClose: () => {
        if (container) {
          render(null, container);
          document.body.removeChild(container);
          container = null;
        }
      }
    });

    render(vnode, container);
  };

  return {
    onPreviewImg
  };
}
