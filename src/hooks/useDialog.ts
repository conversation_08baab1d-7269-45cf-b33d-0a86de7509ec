import { h, ref } from "vue";
import { addDialog } from "@/components/ReDialog";
import type { DialogOptions } from "@/components/ReDialog";

export function useDialog() {
  const dialogVisible = ref(false);

  const openDialog = (component: any, options: Partial<DialogOptions> = {}) => {
    const {
      title = "",
      props = {},
      width = "500px",
      draggable = true,
      closeOnClickModal = false,
      ...restOptions
    } = options;

    return addDialog({
      title,
      width,
      draggable,
      closeOnClickModal,
      contentRenderer: () =>
        h(component, {
          ...props,
          visible: dialogVisible,
          "onUpdate:visible": (val: boolean) => {
            dialogVisible.value = val;
          }
        }),
      ...restOptions
    });
  };

  return {
    dialogVisible,
    openDialog
  };
}
