import { ref } from "vue";
import { http } from "@/utils/http";
import { ElMessage } from "element-plus";

export enum TreeNodeType {
  /** 城市 */
  CITY = 0,
  /** 县级 */
  COUNTY = 1,
  /** 乡镇 */
  TOWN_SHIP = 2,
  /** 村 */
  HAMLET = 3,
  /** 点位 */
  SITE = 4
}

export interface TreeNode {
  /** 主键id */
  id: number;
  /** 节点名称 */
  label: string;
  /** 节点类型 */
  level: TreeNodeType;
  /** 父节点id */
  parentId: number;
  /** 子集节点 */
  childList?: TreeNode[];
  /** 更新时间 */
  updateTime: string | null;
  /** 创建时间 */
  createTime: string | null;
}

interface NodeTreeResponse {
  code: number;
  data: TreeNode[];
  message: string;
}

export const useNodeTree = () => {
  const nodeTree = ref<TreeNode[]>([]);
  const selectedNodes = ref({
    city: "",
    county: "",
    township: "",
    hamlet: "",
    site: ""
  });

  // 获取节点树数据
  const getNodeTree = async () => {
    try {
      const res = await http.get<NodeTreeResponse, any>(
        "/treeNode/selectTreeNode"
      );
      if (res.code === 200 && Array.isArray(res.data)) {
        nodeTree.value = res.data;
      } else {
        ElMessage.error("获取节点树数据格式错误");
        console.error("节点树数据格式错误:", res);
      }
    } catch (error) {
      ElMessage.error("获取节点树失败");
      console.error("获取节点树失败:", error);
    }
  };

  // 处理节点选择
  const handleNodeSelect = (node: TreeNode) => {
    // 根据节点层级设置选中的节点
    switch (node.level) {
      case TreeNodeType.CITY:
        selectedNodes.value = {
          city: node.label,
          county: "",
          township: "",
          hamlet: "",
          site: ""
        };
        break;
      case TreeNodeType.COUNTY:
        selectedNodes.value = {
          ...selectedNodes.value,
          county: node.label,
          township: "",
          hamlet: "",
          site: ""
        };
        break;
      case TreeNodeType.TOWN_SHIP:
        selectedNodes.value = {
          ...selectedNodes.value,
          township: node.label,
          hamlet: "",
          site: ""
        };
        break;
      case TreeNodeType.HAMLET:
        selectedNodes.value = {
          ...selectedNodes.value,
          hamlet: node.label,
          site: ""
        };
        break;
      case TreeNodeType.SITE:
        selectedNodes.value = {
          ...selectedNodes.value,
          site: node.label
        };
        break;
    }
  };

  // 初始化时获取节点树数据
  getNodeTree();

  return {
    nodeTree,
    selectedNodes,
    handleNodeSelect,
    refreshNodeTree: getNodeTree
  };
};
