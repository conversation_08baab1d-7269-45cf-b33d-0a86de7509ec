import type { AddressLevel } from "@/types/business";
import type { TreeNode } from "@/views/system/system-user/utils/types";
import type { RouteRecordName } from "vue-router";

export type cacheType = {
  mode: string;
  name?: RouteRecordName;
};

export type positionType = {
  startIndex?: number;
  length?: number;
};

export type appType = {
  sidebar: {
    opened: boolean;
    withoutAnimation: boolean;
    // 判断是否手动点击Collapse
    isClickCollapse: boolean;
  };
  layout: string;
  device: string;
  viewportSize: { width: number; height: number };
  largeScreenArea: AddressLevel;
  largeScreenPathId: number[];
};

export type multiType = {
  path: string;
  name: string;
  meta: any;
  query?: object;
  params?: object;
};

export type setType = {
  title: string;
  fixedHeader: boolean;
  hiddenSideBar: boolean;
};

export type dataType = {
  options: {
    // 车牌状态
    brand: Option[];
    // 处理状态
    handle: Option[];
    // 违法类型
    traffic: Option[];
    // 是否超员 -> 车辆类型
    overcrowding: Option[]; // 车辆类型
    // 是否进行劝导
    persuasion: Option[];
    // 是否需要手动指派
    assign: Option[];
    // 车牌颜色
    plateColor: Option[];
  };
};

export type Option = {
  code: number;
  desc: string;
  color?: string;
};

export type userType = {
  roleList: string[];
  userInfo: UserInfo | null;
  tokenInfo: TokenInfo;
  username?: string;
  nickname?: string;
  roles?: Array<string>;
  permissions?: Array<string>;
  userRegionTree: TreeNode[];
};

export type UserInfo = {
  userId: number;
  userName: string;
  password: string;
  name: string;
  sex: string;
  deptName: string;
  phone: string;
  city: string;
  county: string;
  township: string;
  site: string;
  state: number;
  createTime: string;
};

export type TokenInfo = {
  tokenName: string;
  tokenValue: string;
  isLogin: string;
  loginId: string;
  loginType: number;
  tokenTimeout: number;
  sessionTimeout: number;
  tokenSessionTimeout: number;
  tokenActiveTimeout: number;
  loginDevice: string;
  tag: string | null;
};
