import { useUserStoreHook } from "@/store/modules/user";
import type { AddressLevel } from "@/types/business";
import { defineStore } from "pinia";
import {
  type appType,
  deviceDetection,
  getConfig,
  responsiveStorageNameSpace,
  storageLocal,
  store
} from "../utils";

/** 系统布局配置的 Store */
export const useAppStore = defineStore({
  id: "pure-app",
  state: (): appType => ({
    /** 侧边栏状态 */
    sidebar: {
      // 是否展开
      opened:
        storageLocal().getItem<StorageConfigs>(
          `${responsiveStorageNameSpace()}layout`
        )?.sidebarStatus ?? getConfig().SidebarStatus,
      // 是否有动画
      withoutAnimation: false,
      // 是否是点击 Collapse 触发
      isClickCollapse: false
    },
    /** 布局模式 */
    layout:
      storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}layout`
      )?.layout ?? getConfig().Layout,
    /** 设备类型 */
    device: deviceDetection() ? "mobile" : "desktop",
    /** 视口大小 */
    viewportSize: {
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight
    },
    /** 大屏公共地区选择值 */
    largeScreenArea: null as AddressLevel | null,
    /** 大屏公共地区选择路径 */
    largeScreenPathId: []
  }),
  getters: {
    /** 获取侧边栏状态 */
    getSidebarStatus(state) {
      return state.sidebar.opened;
    },
    /** 获取设备类型 */
    getDevice(state) {
      return state.device;
    },
    /** 获取视口宽度 */
    getViewportWidth(state) {
      return state.viewportSize.width;
    },
    /** 获取视口高度 */
    getViewportHeight(state) {
      return state.viewportSize.height;
    },
    /** 获取大屏公共选址地址 */
    getLargeScreenArea(state) {
      return state.largeScreenArea || {};
    },
    /** 获取大屏公共选址地址id路径 */
    getLargeScreenPathId(state) {
      return state.largeScreenPathId;
    }
  },
  actions: {
    /** 切换侧边栏 */
    TOGGLE_SIDEBAR(opened?: boolean, resize?: string) {
      const layout = storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}layout`
      );
      if (opened && resize) {
        this.sidebar.withoutAnimation = true;
        this.sidebar.opened = true;
        layout.sidebarStatus = true;
      } else if (!opened && resize) {
        this.sidebar.withoutAnimation = true;
        this.sidebar.opened = false;
        layout.sidebarStatus = false;
      } else if (!opened && !resize) {
        this.sidebar.withoutAnimation = false;
        this.sidebar.opened = !this.sidebar.opened;
        this.sidebar.isClickCollapse = !this.sidebar.opened;
        layout.sidebarStatus = this.sidebar.opened;
      }
      storageLocal().setItem(`${responsiveStorageNameSpace()}layout`, layout);
    },
    /** 异步切换侧边栏 */
    async toggleSideBar(opened?: boolean, resize?: string) {
      await this.TOGGLE_SIDEBAR(opened, resize);
    },
    /** 设置设备类型 */
    toggleDevice(device: string) {
      this.device = device;
    },
    /** 设置布局模式 */
    setLayout(layout) {
      this.layout = layout;
    },
    /** 设置视口大小 */
    setViewportSize(size) {
      this.viewportSize = size;
    },
    setSortSwap(val) {
      this.sortSwap = val;
    },
    /** 设置大屏公共地址 */
    setLargeScreenArea(area: AddressLevel, pathId: number[]) {
      if (!area) return;
      this.largeScreenPathId = pathId;
      this.largeScreenArea = area;
    },
    /** 初始化大屏地址 */
    initLargeScreenArea() {
      const userStore = useUserStoreHook();
      this.largeScreenArea = userStore.getUserLocationPath;
    }
  }
});

export function useAppStoreHook() {
  return useAppStore(store);
}
