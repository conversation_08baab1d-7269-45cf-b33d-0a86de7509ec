import { getAreaTree } from "@/api/system";
import { type UserInfoResult, getLogin, getUserInfoByToken } from "@/api/user";
import { initRoutes } from "@/router";
import { LocationPointEnum } from "@/types/business";
import { removeToken, setToken } from "@/utils/auth";
import { defineStore } from "pinia";
import type { TokenInfo, UserInfo } from "../types";
import {
  type userType,
  resetRouter,
  router,
  routerArrays,
  store
} from "../utils";
import { useMultiTagsStoreHook } from "./multiTags";
export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    // 角色列表
    roleList: [],
    // 用户信息
    userInfo: null,
    // token信息
    tokenInfo: null,
    // 权限列表
    permissions: undefined,
    // 用户管辖地区区域树列表
    userRegionTree: []
  }),
  getters: {
    /**
     * 根据权限列表和用户信息获取当前位置路径集合
     * @returns {Object} 当前路径集合（key: value 格式，所有字段都存在）
     */
    getUserLocationPath(state) {
      const levels: LocationPointEnum[] = Object.values(LocationPointEnum);
      const path = {};

      // 初始化所有字段为空字符串
      levels.forEach(level => {
        path[level] = "";
      });

      // 找到权限列表中对应的最低层级的索引
      const startIndex = levels.findIndex(level =>
        state.roleList.includes(level)
      );
      if (startIndex === -1) {
        // 如果权限列表中没有匹配的层级，直接返回空字段的对象
        return path;
      }

      // 从最低层级开始，依次填充路径对象
      for (let i = 0; i <= startIndex; i++) {
        const level = levels[i];
        if (state.userInfo[level]) {
          path[level] = state.userInfo[level];
        }
      }
      Object.keys(path).forEach(field => {
        if (path[field] === "") {
          path[field] = undefined;
        }
      });
      return path;
    },
    /**
     * 获取当前级别最高的角色 code
     * @returns {string} 当前最高级别的角色 code（如 "city", "county"...），如果未匹配，返回空字符串
     */
    getHighestRoleCode(state): LocationPointEnum | "" {
      const levels: LocationPointEnum[] = Object.values(LocationPointEnum);

      // 从高到低依次检查是否存在于 roleList
      for (const level of levels) {
        if (state.roleList.includes(level)) {
          return level; // 返回匹配的最高级别
        }
      }

      // 如果没有匹配，返回空字符串
      return "";
    },
    getUserRegionTree(state) {
      return state.userRegionTree;
    }
  },
  actions: {
    /** 设置权限信息 */
    SET_ROLE_LIST(roleList: string[]) {
      this.roleList = roleList;
    },
    /** 设置用户信息 */
    SET_USERINFO(userInfo: UserInfo) {
      this.userInfo = userInfo;
    },
    /** 设置token信息 */
    SET_TOKEN_INFO(tokenInfo: TokenInfo) {
      this.tokenInfo = tokenInfo;
    },
    /** 设置权限列表 */
    SET_PERMISSIONS(permissions: string[]) {
      this.permissions = permissions;
    },
    /** 获取用户信息和权限列表 */
    async getPermissions() {
      try {
        if (this.permissions) return this.permissions;
        const res = await getUserInfoByToken();
        if (res.code === 200) {
          const { permissionList = [], user, tokenInfo, roleList } = res.data;
          this.SET_ROLE_LIST(roleList);
          this.SET_TOKEN_INFO(tokenInfo);
          this.SET_USERINFO(user);
          this.SET_PERMISSIONS(permissionList);
          return permissionList;
        }
        return [];
      } catch (error) {
        return [];
      }
    },
    /** 初始化用户区域树 */
    initUserRegionTree() {
      getAreaTree().then(res => {
        if (res.code === 200) {
          // 找到所有市级数据
          const cities = res.data.reduce((acc: any[], item: any) => {
            if (item.level === 0) {
              return [...acc, ...item.childList];
            }
            return acc;
          }, []);
          // 如果没有市级权限，直接返回
          if (cities.length === 0) {
            this.userRegionTree = res.data;
            return;
          }
          // 如果只有一个市，直接显示该市的区县列表
          if (cities.length === 1) {
            this.userRegionTree = cities[0].childList;
          } else {
            // 如果有多个市，显示市级列表
            this.userRegionTree = cities;
          }
        }
      });
    },
    /** 登入 */
    async loginByPhone(data) {
      return new Promise<UserInfoResult>((resolve, reject) => {
        resetRouter();
        getLogin(data)
          .then(async data => {
            if (data.code === 200) {
              const { roleList, user, tokenInfo, permissionList } = data.data;
              setToken(tokenInfo);
              this.roleList = roleList;
              this.userInfo = user;
              this.tokenInfo = tokenInfo;
              this.SET_PERMISSIONS(permissionList);

              // 登录成功后重新初始化路由
              await initRoutes();
            }
            resolve(data);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      this.username = "";
      this.roles = [];
      this.permissions = [];
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
