import { defineStore } from "pinia";
import type { dataType, Option } from "../types";
import { store } from "../utils";
import { getSelectOptions } from "@/api/resource";

/** 业务数据的 Store */
export const useDataStore = defineStore({
  id: "business-data",
  state: (): dataType => ({
    /** 选项配置数据 */
    options: {
      brand: [], // 车牌状态
      handle: [], // 处理状态
      traffic: [], // 违法类型
      overcrowding: [], // 车辆类型
      persuasion: [], // 是否进行劝导
      assign: [], // 是否需要手动指派
      plateColor: [] // 车牌颜色
    }
  }),
  getters: {
    /** 获取指定字段的选项配置 */
    getOption: state => {
      return (filed: keyof dataType["options"]): Option[] => {
        return state.options[filed];
      };
    }
  },
  actions: {
    /** 初始化选项配置数据 */
    initOptions() {
      getSelectOptions().then(res => {
        if (res.code === 200) {
          for (const key in res.data.enums) {
            this.options[key] = res.data.enums[key];
          }
        }
      });
    }
  }
});

export function useDataStoreHook() {
  return useDataStore(store);
}
