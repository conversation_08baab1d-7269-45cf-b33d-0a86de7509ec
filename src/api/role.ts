import { http } from "@/utils/http";
import type { HttpResponse, PagedHttpResponse } from "@/utils/http/types";
import type { Role } from "@/views/system/system-role/utils/types";

/** 查询角色可选项 */
export const getRoleOptions = () => {
  return http.request<HttpResponse<Role[]>>("get", "/role/selectRoleList");
};

/** 查询角色列表 */
export const getRoleList = (data: Object) => {
  return http.request<PagedHttpResponse<Role>>("get", "/role/selectRoles", {
    params: data
  });
};

/** 增加角色 */
export const addRole = (data: Object) => {
  return http.request<HttpResponse<Role>>("post", "/role/addRole", {
    data
  });
};

/** 删除角色 */
export const deleteRole = (roleId: number) => {
  return http.request<HttpResponse<Role>>("delete", "/role/deleteRole", {
    params: { id: roleId } // 使用 params 传递查询参数
  });
};

/** 修改角色权限 */
export const updateRole = (data: Object) => {
  return http.request<HttpResponse<Role>>("put", "/role/updateRole", {
    data
  });
};
