import { http } from "@/utils/http";
import type { HttpResponse } from "@/utils/http/types";

// 后端返回的数据结构
export interface SystemConfigResponse {
  id: number;
  overload: number;
  logSaveTime: number;
  offDutyTime: number;
  equipmentOfflineTime: number;
  phone: boolean;
  effectivePersuasion: number;
}

// 前端使用的数据结构
export interface SystemConfig {
  id?: number; // 配置ID
  overloadThreshold: number; // 超载人数阈值
  logCleanupDays: number; // 日志清理天数
  absentThresholdMinutes: number; // 劝导员脱岗时间(分钟)
  deviceOfflineMinutes: number; // 设备离线判定时间(分钟)
  enableAutoCall: boolean; // 是否启用自动打电话
  validVideoSeconds: number; // 有效劝导视频时长(秒)
}

// 数据转换函数
export function convertToSystemConfig(
  response: SystemConfigResponse
): SystemConfig {
  return {
    id: response.id,
    overloadThreshold: response.overload,
    logCleanupDays: response.logSaveTime,
    absentThresholdMinutes: response.offDutyTime,
    deviceOfflineMinutes: response.equipmentOfflineTime,
    enableAutoCall: response.phone,
    validVideoSeconds: response.effectivePersuasion
  };
}

export function convertToSystemConfigResponse(
  config: SystemConfig
): SystemConfigResponse {
  return {
    id: config.id || 1, // 如果没有 ID，默认使用 1
    overload: config.overloadThreshold,
    logSaveTime: config.logCleanupDays,
    offDutyTime: config.absentThresholdMinutes,
    equipmentOfflineTime: config.deviceOfflineMinutes,
    phone: config.enableAutoCall,
    effectivePersuasion: config.validVideoSeconds
  };
}

// 获取系统配置
export function fetchSystemConfig() {
  return http.request<HttpResponse<SystemConfigResponse>>(
    "get",
    "/RelatedConfigurations/getRelatedConfigurations"
  );
}

// 保存系统配置
export function saveSystemConfig(data: SystemConfig) {
  const requestData = convertToSystemConfigResponse(data);
  return http.request<HttpResponse>(
    "post",
    "/RelatedConfigurations/updateRelatedConfigurations",
    {
      data: requestData
    }
  );
}
