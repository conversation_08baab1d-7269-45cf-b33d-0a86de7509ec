import { http } from "@/utils/http";

export type Result = {
  code: number;
  data: any;
  message: string;
};

/** 获取所有员工 */
export function getAllEmployees(params) {
  return http.request<Result>("get", "/schedule/employees", {
    params
  });
}

/** 获取所有员工的考勤记录 */
export function getAllEmployeeAttendance(
  userName: string,
  startDate: string,
  endDate: string
) {
  return http.request<Result>("get", "/attendance/schedule/all", {
    params: { userName, startDate, endDate }
  });
}

/** 获取员工考勤记录 */
export function getEmployeeAttendance(
  userId: number,
  startDate: string,
  endDate: string
) {
  return http.request<Result>("get", `/attendance/employee/${userId}`, {
    params: { startDate, endDate }
  });
}

/** 获取考勤统计 */
export function getAttendanceStats(
  userId: number,
  startDate: string,
  endDate: string
) {
  return http.request<Result>("get", `/attendance/stats/${userId}`, {
    params: { startDate, endDate }
  });
}

/** 删除考勤记录 */
export function deleteAttendance(id: number) {
  return http.request("delete", `/attendance/${id}`);
}

/** 添加考勤记录 */
export function addAttendance(data) {
  return http.request("post", "/attendance", {
    data
  });
}

/** 更新考勤记录 */
export function updateAttendance(data) {
  return http.request("put", `/attendance/${data.id}`, {
    data
  });
}
