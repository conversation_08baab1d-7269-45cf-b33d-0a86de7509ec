import { http } from "@/utils/http";
import type { AddressLevel } from "@/types/business";
import type { HttpResponse } from "@/utils/http/types";
import type { ManualAlarm } from "@/views/large-screen/types";
export type Result = {
  code: number;
  data: any;
  message: string;
};
/** 查询手动报警列表 */
export const getManualAlarmList = (address: AddressLevel) => {
  return http.request<HttpResponse<ManualAlarm[]>>(
    "get",
    "/alarm/getManualOperation",
    {
      params: address
    }
  );
};

/** 确认报警 */
export const confirmAlarm = (id: number) => {
  return http.request<HttpResponse<any>>("get", "/alarm/confirmAlarm", {
    params: { id }
  });
};

/** 获取路口视频地址 */
export const getIntersection = (id: number) => {
  return http.request<HttpResponse<Result>>("get", "/alarm/getIntersection", {
    params: { id }
  });
};

/**
 * 获取报警提示
 */
export function getAlarmPrompt(params?: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}) {
  return http.request<HttpResponse<any>>("get", "/alarm/getAlarmPrompt", {
    params
  });
}

/**
 * 获取报警详情
 */
export function getAlarmDetail(uuid: string) {
  return http.request<HttpResponse<any>>(
    "get",
    "/IllegalRecords/selectDetails",
    {
      params: { uuid }
    }
  );
}
