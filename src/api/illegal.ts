import { http } from "@/utils/http";
import type { HttpResponse, PagedHttpResponse } from "@/utils/http/types";
import type { IllegalRecord } from "@/views/illegal/illegal-list/utils/type";
import type { User } from "@/views/system/system-user/utils/types";
export type Result = {
  code: number;
  data: any;
  message: string;
};
/** 查询违法列表 */
export const getIllegalRecords = (params: Object) => {
  return http.request<PagedHttpResponse<IllegalRecord>>(
    "get",
    "/IllegalRecords/selectIllegalRecords",
    {
      params
    }
  );
};

/** 查询可指派的劝导员 */
export const getDispatchUserList = (params: Object) => {
  return http.request<HttpResponse<User[]>>(
    "get",
    "/IllegalRecords/selectProselytizer",
    { params }
  );
};

/** 劝导员指派违法 */
export const dispatchIllega = (data: Object) => {
  return http.request<HttpResponse>("post", "/IllegalRecords/update", {
    data
  });
};

/** 查询违法处理结果详情 */

export const fecthIllegalHandleResult = (uuid: string) => {
  return http.request<HttpResponse>("get", "/IllegalRecords/selectDetails", {
    params: {
      uuid
    }
  });
};

/** 查询精准劝导列表 */
export const selectAccuratePersuasionList = (params?: object) => {
  return http.request<Result>(
    "get",
    "/accuratePersuasion/selectAccuratePersuasionList",
    {
      params
    }
  );
};

/** 删除精准劝导 */
export const deleteAccuratePersuasion = (uuid: string) => {
  return http.request<Result>(
    "delete",
    "/accuratePersuasion/deleteAccuratePersuasion",
    {
      params: { uuid }
    }
  );
};

/** 批量删除精准劝导 */
export const deleteAccuratePersuasions = (uuids: string[]) => {
  return http.request<Result>(
    "post",
    "/accuratePersuasion/deleteAccuratePersuasions",
    {
      headers: {
        "Content-Type": "application/json"
      },
      data: JSON.stringify(uuids)
    }
  );
};

/** 更新违法记录审核信息 */
export function updateIllegalAudit(params: {
  uuid: string;
  actualVehicleType?: number;
  actualPlateColor?: number;
  actualPlateNumber?: string;
  actualIllegalType?: number;
  actualNumberOfPassengers?: number;
  actualPersuasiveBehavior?: number;
}) {
  return http.request<Result>("post", "/IllegalRecords/addConfirm", { params });
}

/** 下派违法 */
export function dispatchIllegal(data: FormData) {
  return http.request<HttpResponse>(
    "post",
    "/accuratePersuasion/dispatchAccuratePersuasion",
    {
      data,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
}

/** 删除违法记录 */
export const deleteIllegalRecord = (uuid: string) => {
  return http.request<HttpResponse>("delete", "/IllegalRecords/delete", {
    params: { uuid }
  });
};
