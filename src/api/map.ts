import { http } from "@/utils/http";
// 获取坐标接口
export interface CoordinateResponse {
  code: number;
  data: {
    latitude: string;
    longitude: string;
  };
  msg: string;
}

/**
 * 获取地理坐标
 * @param city - 城市名称
 * @param county - 县区名称（可选）
 */
export const getCoordinate = (city: string, county?: string) => {
  return http.request<CoordinateResponse>("get", "/map/getCoordinate", {
    params: { city, county }
  });
};
