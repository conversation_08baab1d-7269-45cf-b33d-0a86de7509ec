import { http } from "@/utils/http";
import type { HttpResponse, PagedHttpResponse } from "@/utils/http/types";
import type { User } from "@/views/system/system-user/utils/types";

/** 获取用户列表 */
export const getUserList = (data?: object) => {
  return http.request<PagedHttpResponse<User>>("post", "/user/selectUsers", {
    data
  });
};

/** 新增用户 */
export const addUser = (data?: object) => {
  return http.request<HttpResponse>("post", "/user/addUser", {
    data
  });
};

/** 编辑用户 */
export const editUser = (data: Object) => {
  return http.request<HttpResponse>("post", "/user/updateUsername", {
    data
  });
};

/** 管理员重置密码 */
export const resetPassword = (userId: string, password: string) => {
  return http.request<HttpResponse>("put", "/user/resetPassword", {
    data: {
      userId: userId,
      newPassword: password
    }
  });
};

/** 删除用户 */
export const deleteUser = (userId: string) => {
  return http.request<HttpResponse>("delete", "/user/deleteUser", {
    params: {
      id: userId
    }
  });
};
/** 批量删除用户 */
export const deleteRoleList = (userIds: number[]) => {
  return http.request<HttpResponse>("delete", "/user/deleteUserList", {
    data: userIds
  });
};

/** 冻结用户 */
export const frozenUser = (userId: string, state: number) => {
  return http.request<HttpResponse>("put", "/user/frozenUser", {
    data: {
      id: userId,
      state: state
    }
  });
};

/** 查询节点树 */
export const getAreaTree = () => {
  return http.request<any>("get", "/treeNode/selectTreeNode");
};

/** 删除节点 */
export const deleteTreeNode = (id: number) => {
  return http.request<any>("get", "/treeNode/deleteTreeNode", {
    params: {
      id: id
    }
  });
};

/** 新增节点 */
export const addTreeNode = (data: Object) => {
  return http.request<any>("post", "/treeNode/addTreeNode", {
    data
  });
};

/** 编辑节点 */
export const updateTreeNode = (data: Object) => {
  return http.request<any>("post", "/treeNode/updateTreeNode", {
    data
  });
};

/** 分配用户角色 */
export const assignRole = (userId: string, roleIds: number[]) => {
  return http.request<HttpResponse>("post", "/role/assignRole", {
    data: {
      userId: userId,
      roleIds: roleIds
    }
  });
};
