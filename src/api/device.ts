import { http } from "@/utils/http";

export type Result = {
  code: number;
  data: any;
  message: string;
};

export interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  location: string;
  state: number;
  ip: string;
  equipmentNumber: string;
  createTime: string;
  updateTime: string;
  province?: string;
  city?: string;
  county?: string;
  township?: string;
  site?: string;
  longitude?: string;
  latitude?: string;
}

// 定义设备分组数据类型
export interface GroupedDevice {
  deviceType: string;
  streamKey: string | null;
  latitude: string;
  id: number;
  state: number;
  deviceName: string;
  equipmentNumber: string;
  longitude: string;
  offlineDuration?: string;
  offlineTime?: string;
  maintainerPhone: string;
}

export interface LocationGroup {
  site: string;
  city: string;
  hamlet: string;
  county: string;
  township: string;
  devices: GroupedDevice[];
}

// 视频回放相关类型定义
export interface DeviceTreeNode {
  label: string;
  type: "location" | "device";
  level: "city" | "county" | "township" | "hamlet" | "site" | null;
  children: DeviceTreeNode[];
  deviceInfo?: {
    deviceId: number;
    deviceName: string;
    equipmentNumber: string;
    state: number;
    ip: string;
    streamKey: string;
  } | null;
}

export interface PlaybackQueryParams {
  deviceId: number;
  date: string;
  startTime?: string;
  endTime?: string;
}

export interface PlaybackResult {
  deviceId: number;
  deviceName: string;
  date: string;
  videos: {
    startTime: string;
    endTime: string;
    duration: number;
    url: string;
  }[];
}

// 录像查询相关类型定义
export interface RecordingQueryParams {
  streamName: string;
  startTime?: string;
  endTime?: string;
}

export interface RecordingInfo {
  id: number;
  streamName: string;
  deviceName: string | null;
  fileName: string;
  filePath: string;
  fileSize: number;
  duration: number;
  startTime: string;
  endTime: string;
  status: number;
  mp4Converted: number;
  createdTime: string;
  updatedTime: string;
}

export interface RecordingsResult {
  recordings: RecordingInfo[];
  total: number;
}

/** 获取设备列表（分页） */
export function getDeviceList(params: {
  curPage: number;
  pageSize: number;
  location?: any;
}) {
  return http.request<Result>("get", "/device/getDevice", {
    params
  });
}

/** 获取设备状态列表 */
export const getDeviceStatusList = (state: 0 | 1) => {
  return http.request<Result>("get", "/device/getDevicesByLocation", {
    params: {
      state: state
    }
  });
};

/** 添加设备 */
export function addDevice(deviceDTO) {
  delete deviceDTO.id;
  delete deviceDTO.status;
  delete deviceDTO.updateTime;
  delete deviceDTO.createTime;
  return http.request<Result>("post", "/device/addDevice", {
    data: deviceDTO
  });
}

/** 更新设备信息 */
export function updateDevice(deviceDTO) {
  delete deviceDTO.status;
  delete deviceDTO.updateTime;
  return http.request<Result>("put", "/device/updateDevice", {
    data: deviceDTO
  });
}

/** 删除单个设备 */
export function deleteDevice(id: number) {
  return http.request<Result>("delete", "/device/deleteDevice", {
    params: {
      id: id
    }
  });
}

/** 批量删除设备 */
export function deleteDevices(ids: number[]) {
  return http.request<Result>("post", "/device/deleteDevices", {
    data: ids
  });
}

/** 获取设备摄像头url */
export function getCameraUrl(equipmentNumber: string) {
  return http.request<Result>("get", "/device/getCameraUrl", {
    params: {
      equipmentNumber: equipmentNumber
    }
  });
}

// === 视频回放相关API ===

/** 获取设备节点树 */
export function getCameraTree() {
  return http.request<Result>("get", "/video/camera-tree");
}

/** 查询设备回放视频 */
export function queryPlaybackVideos(params: PlaybackQueryParams) {
  return http.request<Result>("get", "/video/playback", {
    params
  });
}

// === 录像查询相关API ===

/** 生成streamName（去除设备编号和IP中的冒号和点） */
export function generateStreamName(
  equipmentNumber: string,
  ip: string
): string {
  const cleanEquipmentNumber = equipmentNumber.replace(/[:.]/g, "");
  const cleanIp = ip.replace(/[:.]/g, "");
  return `${cleanEquipmentNumber}${cleanIp}`;
}

/** 查询录制文件列表 */
export function getRecordings(params: RecordingQueryParams) {
  return http.request<Result>("get", "/video/recordings", {
    params
  });
}

/** 获取MP4视频播放URL */
export function getVideoPlayUrl(filePath: string): string {
  const baseUrl = import.meta.env.VITE_API_URL;
  const finalUrl = `${baseUrl}/video/play-direct?filePath=${encodeURIComponent(filePath)}`;
  return finalUrl;
}

/** 下载MP4视频文件 */
export function downloadVideoFile(filePath: string, fileName?: string): void {
  const baseUrl = import.meta.env.VITE_API_URL;
  const downloadUrl = `${baseUrl}/video/download?filePath=${encodeURIComponent(filePath)}`;

  console.log("开始下载视频文件:", filePath);
  console.log("下载URL:", downloadUrl);

  // 创建隐藏的下载链接
  const link = document.createElement("a");
  link.href = downloadUrl;
  link.style.display = "none";

  // 如果提供了文件名，设置下载文件名
  if (fileName) {
    link.download = fileName;
  }

  // 添加到DOM，触发下载，然后移除
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  console.log("下载请求已发送");
}
