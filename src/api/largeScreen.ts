import type { AddressLevel } from "@/types/business";
import { http } from "@/utils/http";
import type { HttpResponse } from "@/utils/http/types";

/** 获取实时报警数据 */
export const getRealTimeAlarms = (address: AddressLevel) => {
  return http.request<HttpResponse<any>>("get", "/statistics/latestNews", {
    params: address
  });
};

/** 获取违法概览数据（日） */
export const getIllegalOverview = (address: AddressLevel, date?: string) => {
  return http.request<HttpResponse<any>>("get", "/largeScreen/DayStatistics", {
    params: {
      ...address,
      ...(date && { date })
    }
  });
};

/** 获取违法概览数据（月） */
export const getMonthIllegalOverview = (
  address: AddressLevel,
  date?: string
) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/MonthStatistics",
    {
      params: {
        ...address,
        ...(date && { date })
      }
    }
  );
};

/** 获取设备状态数据 */
export const getDeviceOverview = (address: AddressLevel) => {
  return http.request<HttpResponse<any>>("get", "/largeScreen/deviceOverview", {
    params: address
  });
};

/** 获取高发地段排名数据 （日）*/
export const getHotspotRank = (address: AddressLevel, date?: string) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/topLocationsByDay",
    {
      params: {
        ...address,
        ...(date && { date })
      }
    }
  );
};

/** 获取高发地段排名数据（月） */
export const getMonthHotspotRank = (address: AddressLevel, date?: string) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/topLocationsByMonth",
    {
      params: {
        ...address,
        ...(date && { date })
      }
    }
  );
};

// /** 获取数据统计数据 */
// export const getStatisticsData = () => {
//   return http.request<HttpResponse<any>>("get", "/largeScreen/illegalData");
// };

/**
 * 获取精准劝导概览（年度数据）
 */
export const getDisposalStatistics = (
  params: AddressLevel & { year?: string }
) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getDisposalStatistics",
    {
      params
    }
  );
};

/**
 * 获取精准劝导概览（月度数据）
 */
export const getDisposalStatisticsByMonth = (
  params: AddressLevel & {
    startTime: string;
    endTime: string;
  }
) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getDisposalStatisticsByMonth",
    {
      params
    }
  );
};

/**
 * @deprecated 使用 getDisposalStatistics 替代
 * 获取周精准劝导概览
 */
export const getHandleData = (address: AddressLevel) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getDisposalStatistics",
    {
      params: address
    }
  );
};

/**
 * @deprecated 使用 getDisposalStatisticsByMonth 替代
 * 获取月精准劝导概览
 */
export const getHandleDataMonth = (address: AddressLevel) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getDisposalStatisticsByMonth",
    {
      params: address
    }
  );
};

/** 获取点位详情数据（日） */
export const getPointDetailDataByDay = (params: {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
  year: number;
  month: number;
  day: number;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getPointDetailDataByDay",
    { params }
  );
};

/** 获取点位详情数据（月） */
export const getPointDetailDataByMonth = (params: {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
  year: number;
  month: number;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getPointDetailDataByMonth",
    { params }
  );
};

/** 获取点位详情数据（年） */
export const getPointDetailDataByYear = (params: {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
  year: number;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getPointDetailDataByYear",
    { params }
  );
};

/** 违法劝导率统计 */
export const getPersuasionStatsData = (params?: object) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getPersuasionStats",
    {
      params
    }
  );
};

/** 获取点位违法趋势统计(按年统计) */
export const getLocationTrendsByYear = (params: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  year: number;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getLocationTrendsByYear",
    {
      params
    }
  );
};

/** 获取当天出勤情况 */
export const getAttendance = (params: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  date: string;
}) => {
  return http.request<
    HttpResponse<{
      date: string;
      sites: Array<{
        siteId: string;
        location: {
          city: string;
          county: string;
          township: string;
          hamlet: string;
          site: string;
        };
        shifts: Array<{
          shiftName: string;
          startTime: string;
          endTime: string;
          staffCount: number;
        }>;
        staff: Array<{
          userId: number;
          userName: string;
          phone: string;
          deptName: string;
          status: string;
          shifts: Array<{
            shiftName: string;
            startTime: string;
            endTime: string;
            status: string;
            abnormalRecords?: Array<{
              type: string;
              startTime: string;
              endTime: string;
              duration: string;
              description: string;
            }>;
          }>;
        }>;
        deviceFaultRecords?: Array<{
          equipmentNumber: string;
          ip: string;
          startTime: string;
          endTime: string;
          duration: string;
          description: string;
        }>;
        status: string;
      }>;
      statistics: {
        totalSites: number;
        statusCount: {
          onDuty: number;
          offDuty: number;
          rest: number;
          fault: number;
        };
      };
    }>
  >("get", "/largeScreen/getAttendance", { params });
};

/** 获取地图点位详情数据 */
export const getMapPointDetail = (params: {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getMapPointDetail",
    {
      params
    }
  );
};

/** 获取点位违法统计数据 */
export const getPointViolationStats = (params: {
  city: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  startTime: string;
  endTime: string;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getViolationTypeStatisticsByMap",
    {
      params
    }
  );
};

/** 获取区域数据 */
export const getRegionData = (address: AddressLevel) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getBasicDataByMap",
    {
      params: address
    }
  );
};

/** 获取当天出勤勤务安排，在岗时长，在岗率（日） */
export const getAttendanceAndWorkTimeByday = (params: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  date: string;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getAttendanceAndWorkTimeByday",
    { params }
  );
};

/** 获取当天出勤勤务安排，在岗时长，在岗率（月） */
export const getAttendanceAndWorkTimeByMonth = (params: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  date: string;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getAttendanceAndWorkTimeByMonth",
    { params }
  );
};

/** 获取当天出勤勤务安排，在岗时长，在岗率（年） */
export const getAttendanceAndWorkTimeByYears = (params: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  date: string;
}) => {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getAttendanceAndWorkTimeByYears",
    { params }
  );
};
