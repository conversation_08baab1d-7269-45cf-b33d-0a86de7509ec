import { http } from "@/utils/http";
import type { HttpResponse, PagedHttpResponse } from "@/utils/http/types";
import type { LeaveRecord } from "@/views/illegal/examine-leave/types";

/** 获取请假记录列表 */
export const getLeaveRecords = (params: {
  userName?: string;
  status?: number;
  startDate?: string;
  endDate?: string;
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  page: number;
  size: number;
}) => {
  return http.request<PagedHttpResponse<LeaveRecord>>(
    "get",
    "/attendance/leave/list",
    {
      params
    }
  );
};

/** 审批请假 */
export const approveLeave = (params: {
  leaveId: number;
  approvalStatus: number; // 1-同意，2-拒绝
  approvalComment?: string;
}) => {
  return http.request<HttpResponse<any>>("post", "/attendance/leave/approve", {
    params
  });
};

/** 批量审批请假 */
export const batchApproveLeave = (params: {
  ids: number[];
  status: number;
  remark?: string;
}) => {
  return http.request<HttpResponse<any>>(
    "post",
    "/attendance/leave/batchApprove",
    {
      data: params
    }
  );
};

/** 获取请假详情 */
export const getLeaveDetail = (leaveId: number) => {
  return http.request<HttpResponse<LeaveRecord>>(
    "get",
    `/attendance/leave/${leaveId}`
  );
};
