import { http } from "@/utils/http";
import type { HttpResponse } from "@/utils/http/types";

export type Result = {
  code: number;
  data: any;
  message: string;
};

/** 获取班组列表 */
export function getGroupList(page: number, size: number) {
  return http.request<Result>("get", "/shift/group/list", {
    params: { page, size }
  });
}

/** 删除班组 */
export function deleteGroup(id) {
  return http.request<Result>("delete", `/shift/group/${id}`);
}

/** 获取月度排班表 */
export const getScheduleForMonth = (
  userId: number,
  startDate: string,
  endDate: string
) => {
  return http.request<Result>("get", `/schedule/employee/${userId}`, {
    params: {
      startDate,
      endDate
    }
  });
};

export function updateGroup(group) {
  return http.request<Result>("put", "/shift/group/update", {
    data: group
  });
}

export function createGroup(group) {
  return http.request<Result>("post", "/shift/group/add", {
    data: group
  });
}

export function getShiftList() {
  return http.request<Result>("get", "/shift/list");
}

export function createGroupWithShifts(requestData) {
  return http.request<Result>("post", "/shift/group/createWithShifts", {
    data: requestData
  });
}

export function addShift(shiftData) {
  return http.request<Result>("post", "/shift/add", {
    data: shiftData
  });
}

export function updateShift(shiftData) {
  return http.request<Result>("put", "/shift/update", {
    data: shiftData
  });
}

export function assignSchedule(schedule) {
  return http.request<Result>("post", "/user/shift-group/assign", {
    data: schedule
  });
}

export function batchAssignSchedule(schedules) {
  return http.request<Result>("post", "/schedule/batchAssign", {
    data: schedules
  });
}

export function getEmployeeSchedule(userId, startDate, endDate) {
  return http.request<Result>("get", `/schedule/employee/${userId}`, {
    params: { startDate, endDate }
  });
}

export function getScheduleByDate(date) {
  return http.request<Result>("get", "/schedule/date", { params: { date } });
}

export function updateSchedule(schedule) {
  return http.request<Result>("put", "/schedule/update", { data: schedule });
}

export function cancelSchedule(id: number) {
  return http.request<Result>("delete", `/schedule/${id}`);
}

export function generateSchedule(request) {
  return http.request<Result>("post", "/schedule/generate", { data: request });
}

export function temporarySchedule(schedules) {
  return http.request<Result>("post", "/schedule/temporary", {
    data: schedules
  });
}

export function getPersuaders(params: {
  curPage: number;
  pageSize: number;
  location?: any;
}) {
  return http.request<Result>("get", "/schedule/employees", {
    params
  });
}

export function deleteShift(id) {
  return http.request<Result>("delete", `/shift/${id}`);
}

export function getAreaTree() {
  return http.request<Result>("get", "/area/tree");
}

export function updateShiftGroup(id: number, data: any) {
  return http.request<Result>("put", `/shift-group/${id}`, {
    data
  });
}

export function getShiftListPage(page: number, size: number) {
  return http.request<Result>("get", "/shift/listPage", {
    params: { page, size }
  });
}

/** 获取所有班组列表 */
export const listAllGroups = () => {
  return http.request<HttpResponse<any>>("get", "/shift/group/listAll");
};
