import type { AddressLevel } from "@/types/business";
import { http } from "@/utils/http";
import type { HttpResponse } from "@/utils/http/types";

/** 获取今日违法数量 */
export const getTodayIllegalNum = () => {
  return http.request<
    HttpResponse<{
      todayCount: number;
      oneWeekAgoCount: number;
      increaseRate: number;
    }>
  >("get", "/statistics/todayStatistics");
};

/** 获取设备总数 */
export const getDeviceCount = () => {
  return http.request<HttpResponse<any>>("get", "/statistics/deviceCount");
};

/** 获取设备在线率 */
export const getDeviceOnlineNum = () => {
  return http.request<HttpResponse<any>>("get", "/statistics/deviceOnline");
};

/** 获取处理率 */
export const getProcessingRate = () => {
  return http.request<HttpResponse<any>>("get", "/statistics/processingRate");
};

/** 获取年内违法点位最多的前七个 */
export const getTopIllegalSiteByYear = (year: number) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/statistics/topLocationsByYear?year=${year}`
  );
};

/** 获取月内违法点位最多的前七个 */
export const getTopIllegalSiteByMonth = (year: number, month: number) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/statistics/topLocationsByMonth?year=${year}&month=${month}`
  );
};

/** 获取日内违法点位最多的前七个 */
export const getTopIllegalSiteByDay = (
  year: number,
  month: number,
  day: number
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/statistics/topLocationsByDay?year=${year}&month=${month}&day=${day}`
  );
};

/** 获取过去七天内违法点位最多的前七个 */
export const getTopIllegalSiteByLastSeventDay = () => {
  return http.request<HttpResponse<any>>(
    "get",
    "/statistics/topLocationsByLastSevenDays"
  );
};

/** 获取按年统计的违法类型分布趋势 */
export const getIllegalTrendByYear = (
  year: number,
  addRess: AddressLevel = {}
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/violationTrendsByYear`,
    {
      params: {
        year,
        ...addRess
      }
    }
  );
};

/** 获取按月统计的违法类型分布趋势 */
export const getIllegalTrendByMonth = (
  year: number,
  month: number,
  addRess: AddressLevel = {}
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/violationTrendsByMonth`,
    {
      params: {
        year,
        month,
        ...addRess
      }
    }
  );
};

/** 获取按周统计的违法类型分布趋势 */
export const getIllegalTrendByWeek = (
  startTime: string,
  endTime: string,
  addRess: AddressLevel = {}
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/violationTrendsByWeek`,
    {
      params: {
        startTime,
        endTime,
        ...addRess
      }
    }
  );
};

/** 获取按日统计的违法类型分布趋势 */
export const getIllegalTrendByDay = (
  date: string,
  addRess: AddressLevel = {}
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/violationTrendsByDay`,
    {
      params: {
        date,
        ...addRess
      }
    }
  );
};

/** 获取按天统计的违法类型次数 */
export const getViolationCountsByDay = (
  date: string,
  addRess: AddressLevel = {}
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/statistics/violationCountsByDay`,
    {
      params: {
        date,
        ...addRess
      }
    }
  );
};

/** 获取指定时间段内的违法类型次数 */
export const getViolationCountsByTimeRange = (
  startTime: string,
  endTime: string,
  addRess: AddressLevel = {}
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/statistics/violationCountsByTimeRange`,
    {
      params: {
        startTime,
        endTime,
        ...addRess
      }
    }
  );
};

/** 获取最新动态 */
export const getLatestNews = () => {
  return http.request<HttpResponse<any>>("get", "/statistics/latestNews");
};

/** 获取点位某天段违法趋势统计 */
export const getPersuasionTrendsByDay = (params: Object) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/getLocationTrends`,
    {
      params
    }
  );
};

/** 获取点位某时间范围违法趋势统计 */
export const getPersuasionTrendsByDateRange = (params: Object) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/getPersuasionTrendsByDateRange`,
    {
      params
    }
  );
};

/** 获取违法类型分析 */
export function getViolationTypeStatistics(params?: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  date?: string;
}) {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/getViolationTypeStatistics",
    {
      params
    }
  );
}

/** 获取按周统计的违法类型分析 */
export function getViolationTypeAnalysisByWeek(params?: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}) {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/violationTypeAnalysisByWeek",
    {
      params
    }
  );
}

/** 获取按月统计的违法类型分析 */
export function getViolationTypeAnalysisByMonth(params?: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  date?: string;
}) {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/violationTypeAnalysisByMonth",
    {
      params
    }
  );
}

/** 获取按年统计的违法类型分析 */
export function getViolationTypeAnalysisByYear(params?: {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}) {
  return http.request<HttpResponse<any>>(
    "get",
    "/largeScreen/violationTypeAnalysisByYear",
    {
      params
    }
  );
}

// 获取精准劝导处置效率分析（按地点返回）（日）
export const getDisposalEfficiencyAnalysisByDay = (
  date: string,
  params: {
    city?: string;
    county?: string;
    township?: string;
    hamlet?: string;
    site?: string;
  }
) => {
  const [year, month, day] = date.split("-").map(Number);
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/getDisposalEfficiencyAnalysisByDay`,
    {
      params: {
        ...params,
        year,
        month,
        day
      }
    }
  );
};

// 获取精准劝导处置效率分析（按地点返回）（月）
export const getDisposalEfficiencyAnalysisByMonth = (
  year: number,
  month: number,
  params: {
    city?: string;
    county?: string;
    township?: string;
    hamlet?: string;
    site?: string;
  }
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/getDisposalEfficiencyAnalysisByMonth`,
    {
      params: {
        ...params,
        year,
        month
      }
    }
  );
};

// 获取精准劝导处置效率分析（按地点返回）（年）
export const getDisposalEfficiencyAnalysisByYear = (
  year: number,
  params: {
    city?: string;
    county?: string;
    township?: string;
    hamlet?: string;
    site?: string;
  }
) => {
  return http.request<HttpResponse<any>>(
    "get",
    `/largeScreen/getDisposalEfficiencyAnalysisByYear`,
    {
      params: {
        ...params,
        year
      }
    }
  );
};
