import { http } from "@/utils/http";

export interface Result<T = any> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 获取人脸审核列表
 */
export const getFaceReviewList = (params: {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
  examine: string;
  pageSize: number;
  curPage: number;
}) => {
  return http.request<Result>("get", "/examineFace/selectExamineFace", {
    params
  });
};

/**
 * 获取用户列表
 */
export const getUsers = (params: {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
}) => {
  return http.request<Result>("get", "/examineFace/getUsers", {
    params
  });
};

/**
 * 审核人脸
 */
export const reviewFace = (params: {
  id: number;
  userId?: number | null;
  userName?: string | null;
  isExamine: boolean;
  reviewer: string;
  reviewerId: number;
}) => {
  return http.request<Result>("post", "/examineFace/examineFace", {
    data: params
  });
};
