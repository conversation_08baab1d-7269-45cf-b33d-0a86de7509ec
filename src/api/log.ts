import { http } from "@/utils/http";

export type Result = {
  code: number;
  data: any;
  message: string;
};

/** 获取日志列表 */
export const getLogList = (params?: object) => {
  return http.request<Result>("get", "/log/selectLog", { params });
};

/** 删除单个日志 */
export const deleteLog = (id: number) => {
  return http.request<Result>("delete", "/log/deleteLog", {
    params: { id }
  });
};

/** 批量删除日志 */
export const deleteLogs = (ids: number[]) => {
  return http.request<Result>("delete", "/log/deleteLogs", {
    params: { ids: ids.join(",") }
  });
};
