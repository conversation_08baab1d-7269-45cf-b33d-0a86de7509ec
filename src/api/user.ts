import type { TokenInfo, UserInfo } from "@/store/types";
import { http } from "@/utils/http";
export type Result = {
  code: number;
  data: any;
  message: string;
};
export type UserResult = {
  code: boolean;
  data: {
    /** 头像 */
    avatar: string;
    /** 用户名 */
    username: string;
    /** 昵称 */
    nickname: string;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** 按钮级别权限 */
    permissions: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export interface UserInfoResult {
  code: number;
  data: {
    roleList: string[];
    user: UserInfo | null;
    tokenInfo: TokenInfo;
    permissionList: string[];
  };
}

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserInfoResult>("post", "/user/login", { data });
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};

/** 根据token获取用户信息 */
export const getUserInfoByToken = () => {
  return http.request<UserInfoResult>("get", "/user/tokenInfo");
};

/** 修改密码 */
export const updatePassword = (data: {
  userId: number;
  oldPassword: string;
  newPassword: string;
}) => {
  return http.request<Result>("post", "/user/updatePassword", { data });
};
