@import "./transition";
@import "./element-plus";
@import "./sidebar";
@import "./dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;

  /* 常用border-color 需要时可取用 */
  --pure-border-color: rgb(5 5 5 / 6%);

  /* switch关闭状态下的color 需要时可取用 */
  --pure-switch-off-color: #a6a6a6;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

/* 大屏VideoDialog白色边框移除和居中 - 全局强制覆盖 */
.large-screen-video-dialog,
.el-dialog.large-screen-video-dialog,
.el-overlay .large-screen-video-dialog,
.el-dialog__wrapper .large-screen-video-dialog {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  width: 1840px !important;
  height: 1074px !important;
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* 更强的Element Plus对话框覆盖 */
.el-overlay .large-screen-video-dialog.el-dialog,
.el-dialog__wrapper .large-screen-video-dialog.el-dialog {
  width: 1840px !important;
  height: 1074px !important;
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.large-screen-video-dialog .el-dialog {
  border: none !important;
  outline: none !important;
  width: 1840px !important;
  height: 1074px !important;
  box-shadow:
    0 0 40px rgba(64, 158, 255, 0.3),
    0 0 80px rgba(0, 234, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}
