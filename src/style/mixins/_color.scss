@use "sass:color" as color;
@use "sass:string" as string;

@function get-color-channel($color, $channel) {
  @return color.channel($color, $channel, $space: rgb);
}

@function rgb2hex($color) {
  @return string.unquote("#" + #{string.slice(color.ie-hex-str($color), 4)});
}

@function mix-overlay-color($upper, $lower, $opacity) {
  $red: get-color-channel($upper, "red") * $opacity +
    get-color-channel($lower, "red") * (1 - $opacity);
  $green: get-color-channel($upper, "green") * $opacity +
    get-color-channel($lower, "green") * (1 - $opacity);
  $blue: get-color-channel($upper, "blue") * $opacity +
    get-color-channel($lower, "blue") * (1 - $opacity);

  @return rgb($red, $green, $blue);
}
