@use "sass:color" as color;
@use "sass:string" as string;

// 覆盖element-plus的颜色函数
@function rgb2hex($color) {
  @return string.unquote("#" + #{string.slice(color.ie-hex-str($color), 4)});
}

@function mix-overlay-color($upper, $lower, $opacity) {
  $red: color.channel($upper, "red", $space: rgb) * $opacity +
    color.channel($lower, "red", $space: rgb) * (1 - $opacity);
  $green: color.channel($upper, "green", $space: rgb) * $opacity +
    color.channel($lower, "green", $space: rgb) * (1 - $opacity);
  $blue: color.channel($upper, "blue", $space: rgb) * $opacity +
    color.channel($lower, "blue", $space: rgb) * (1 - $opacity);

  @return rgb($red, $green, $blue);
}

// 覆盖element-plus的全局变量
$namespace: "el" !default;
$common-separator: "-" !default;
$element-separator: "__" !default;
$modifier-separator: "--" !default;
$state-prefix: "is-" !default;
$B: null !default;

// 覆盖element-plus的BEM命名函数
@function b($block) {
  $B: $namespace + $common-separator + $block !global;

  @return $B;
}
