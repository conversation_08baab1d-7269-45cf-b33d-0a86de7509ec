// @ts-nocheck
/* eslint-disable */
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useUserStoreHook } from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import NProgress from "@/utils/progress";
import { buildHierarchyTree } from "@/utils/tree";
import {
  createRouter,
  type RouteComponent,
  type Router,
  type RouteRecordRaw
} from "vue-router";
import remainingRouter from "./modules/remaining";
import {
  ascending,
  filterRoutesByPermissions,
  formatFlatteningRoutes,
  formatTwoStageRoutes,
  getHistoryMode,
  handleAliveRoute
} from "./utils";

/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true
  }
);

/** 原始静态路由（未做任何处理） */
const routes = [];

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default);
});

// 初始化权限列表
let permissionList: string[] = [];

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export let constantRoutes: Array<RouteRecordRaw> = [];

/** 用于渲染菜单，保持原始层级 */
export let constantMenus: Array<RouteComponent> = [];

// 获取权限列表
async function getPermissionList() {
  const userStore = useUserStoreHook();
  permissionList = await userStore.getPermissions();
  return permissionList;
}

function sortRoutesByRank(routes) {
  // 先对当前路由列表进行排序
  routes.sort((a, b) => {
    // 获取 rank，若没有 rank 则默认给一个很大的值
    const rankA = a.meta?.rank ?? Number.MAX_VALUE;
    const rankB = b.meta?.rank ?? Number.MAX_VALUE;
    return rankA - rankB; // 升序排序
  });

  // 对每个子路由进行递归排序
  routes.forEach(route => {
    if (route.children && Array.isArray(route.children)) {
      route.children = sortRoutesByRank(route.children); // 递归排序子路由
    }
  });

  return routes;
}

// 添加递归函数来处理嵌套路由
function addNestedRoutes(routes: RouteRecordRaw[]) {
  routes.forEach(route => {
    // 如果是根路由，直接添加
    if (route.path === "/") {
      if (!router.hasRoute(route.name)) {
        router.addRoute(route);
      }
      // 递归添加子路由
      if (route.children) {
        route.children.forEach(childRoute => {
          if (!router.hasRoute(childRoute.name)) {
            router.addRoute(route.name, childRoute);
          }
        });
      }
    } else {
      // 非根路由直接添加
      if (!router.hasRoute(route.name)) {
        router.addRoute(route);
      }
      // 递归添加子路由
      if (route.children) {
        route.children.forEach(childRoute => {
          if (!router.hasRoute(childRoute.name)) {
            router.addRoute(route.name, childRoute);
          }
        });
      }
    }
  });
}

// 修改 initRoutes 函数
export async function initRoutes() {
  // 获取权限列表
  await getPermissionList();

  // 更新constantRoutes
  constantRoutes = formatTwoStageRoutes(
    formatFlatteningRoutes(
      buildHierarchyTree(
        ascending(
          filterRoutesByPermissions(routes.flat(Infinity), permissionList)
        )
      )
    )
  );

  // 更新constantMenus
  constantMenus = ascending(
    filterRoutesByPermissions(routes.flat(Infinity), permissionList)
  ).concat(...remainingRouter);

  // 重置路由
  resetRouter();

  // 使用新函数添加路由
  addNestedRoutes(constantRoutes);

  // 添加remainingRouter
  remainingRouter.forEach((route: RouteRecordRaw) => {
    if (!router.hasRoute(route.name)) {
      router.addRoute(route);
    }
  });

  // 添加404路由
  router.addRoute({
    path: "/:pathMatch(.*)*",
    redirect: "/overview",
    meta: {
      hidden: true
    }
  });

  // 更新菜单
  usePermissionStoreHook().handleWholeMenus(
    filterRoutesByPermissions(
      sortRoutesByRank(routes.flat(Infinity)),
      permissionList
    )
  );
}

/** 不参与菜单的路由 */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path;
});

/** 创建路由实例 */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  // 初始化时添加基础路由
  routes: [
    {
      path: "/login",
      name: "Login",
      component: () => import("@/views/login/index.vue"),
      meta: {
        title: "登录",
        showLink: false,
        rank: 101
      }
    }
  ],
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

/** 重置路由 */
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes.flat(Infinity)))
        )
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}

/** 路由白名单 */
const whiteList = ["/login"];

const { VITE_HIDE_HOME } = import.meta.env;

router.beforeEach(async (to: ToRouteType, _from, next) => {
  const userToken = getToken();

  // 未登录状态
  if (!userToken) {
    if (whiteList.includes(to.path)) {
      next();
    } else {
      next("/login");
    }
    NProgress.start();
    return;
  }

  // 已登录状态
  if (userToken) {
    if (to.path === "/login") {
      next("/");
      return;
    }

    // 如果路由表为空，重新初始化路由
    if (router.getRoutes().length === 1) {
      // 只有登录路由时初始化
      await initRoutes();
      next({ ...to, replace: true });
      return;
    }

    if (to.meta?.keepAlive) {
      handleAliveRoute(to, "add");
      if (_from.name === undefined || _from.name === "Redirect") {
        handleAliveRoute(to);
      }
    }
    next();
  }

  NProgress.start();
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
