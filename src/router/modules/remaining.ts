const Layout = () => import("@/layout/index.vue");
const Error403 = () => import("@/views/error/403.vue");
const Error404 = () => import("@/views/error/404.vue");
const Error500 = () => import("@/views/error/500.vue");
const LargeScreen = () => import("@/views/large-screen/index.vue");

export default [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  {
    path: "/403",
    component: Error403,
    meta: {
      title: "403",
      showLink: false
    }
  },
  {
    path: "/404",
    component: Error404,
    meta: {
      title: "404",
      showLink: false
    }
  },
  {
    path: "/500",
    component: Error500,
    meta: {
      title: "500",
      showLink: false
    }
  },
  {
    path: "/large-screen",
    component: LargeScreen,
    meta: {
      title: "大屏总览",
      showLink: false
    }
  }
] satisfies Array<RouteConfigsTable>;
