const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/overview",
  meta: {
    icon: "ep:home-filled",
    title: "总览",
    rank: 0
  },
  children: [
    {
      path: "/overview",
      name: "Overview",
      component: () => import("@/views/overview/index.vue"),
      meta: {
        title: "总览",
        showLink: VITE_HIDE_HOME === "true" ? false : true
      }
    }
    // {
    //   path: "/large-screen",
    //   name: "LargeScreen",
    //   component: () => import("@/views/large-screen/index.vue"),
    //   meta: {
    //     title: "总览",
    //     showLink: VITE_HIDE_HOME === "true" ? false : true
    //   }
    // }
  ]
} satisfies RouteConfigsTable;
