export default {
  path: "/system",
  redirect: "/system/user",
  meta: {
    icon: "icon-park-solid:setting",
    title: "系统设置",
    rank: 5
  },
  children: [
    {
      path: "/system/user",
      name: "SystemUser",
      component: () => import("@/views/system/system-user/index.vue"),
      meta: {
        title: "用户管理"
      }
    },
    {
      path: "/system/system-role",
      name: "SystemRole",
      component: () => import("@/views/system/system-role/index.vue"),
      meta: {
        title: "角色管理"
      }
    },

    {
      path: "/system/system-log",
      name: "SystemLog",
      component: () => import("@/views/system/system-log/index.vue"),
      meta: {
        title: "系统日志"
      }
    },
    {
      path: "/system/system-setting",
      name: "SystemSetting",
      component: () => import("@/views/system/system-setting/index.vue"),
      meta: {
        title: "系统配置"
      }
    }
  ]
} satisfies RouteConfigsTable;
