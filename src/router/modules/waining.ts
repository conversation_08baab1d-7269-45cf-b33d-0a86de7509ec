export default {
  path: "/warning",
  redirect: "/warning/list",
  meta: {
    icon: "fluent:text-bullet-list-square-warning-20-filled",
    // showLink: false,
    title: "报警",
    rank: 4
  },
  children: [
    {
      path: "/warning/list",
      name: "List",
      component: () => import("@/views/warning/list.vue"),
      meta: {
        title: "报警列表"
      }
    }
  ]
} satisfies RouteConfigsTable;
