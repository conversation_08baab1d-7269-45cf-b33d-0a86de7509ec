export default {
  path: "/counselor",
  redirect: "/illegal/log",
  meta: {
    icon: "tdesign:user-list-filled",
    title: "劝导员管理",
    rank: 5
  },
  children: [
    {
      path: "/schedule-management",
      name: "ScheduleManagement",
      component: () => import("@/views/schedule-management/index.vue"),
      meta: {
        title: "排班列表"
      }
    },
    {
      path: "/examine-leave",
      name: "ExamineLeave",
      component: () => import("@/views/illegal/examine-leave/index.vue"),
      meta: {
        title: "请假审核"
      }
    },
    {
      path: "/face-review",
      name: "FaceReview",
      component: () => import("@/views/face-review/index.vue"),
      meta: {
        title: "人脸审核"
      }
    },
    {
      path: "/attendance-management",
      name: "AttendanceManagement",
      component: () => import("@/views/attendance-management/index.vue"),
      meta: {
        title: "考勤列表"
      }
    },
    {
      path: "/illegal/dispatch",
      name: "IllegalDispatch",
      component: () => import("@/views/illegal/illegal-dispatch/index.vue"),
      meta: {
        title: "任务下派"
      }
    },
    {
      path: "/illegal/log",
      name: "IllegalLog",
      component: () => import("@/views/illegal/illegal-persuasion/index.vue"),
      meta: {
        title: "任务列表"
      }
    }
  ]
} satisfies RouteConfigsTable;
