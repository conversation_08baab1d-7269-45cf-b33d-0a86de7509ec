export default {
  path: "/device",
  redirect: "/device/list",
  meta: {
    icon: "icon-park-solid:computer",
    title: "设备管理",
    rank: 3
  },
  children: [
    {
      path: "/device/list",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      component: () => import("@/views/system/system-device/index-device.vue"),
      meta: {
        title: "设备列表"
      }
    },
    {
      path: "/device/playback",
      name: "DevicePlayback",
      component: () => import("@/views/device/playback/index.vue"),
      meta: {
        title: "设备回放"
      }
    }
  ]
} satisfies RouteConfigsTable;
