/**
 * 判断点位大小关系
 * @param {string} base - 基准点位
 * @param {string} target - 目标点位
 * @returns {boolean} 如果目标点位的优先级高于基准点位，返回 true；否则返回 false
 */
export const isPointHigherOrEqual = (base, target) => {
  // 定义层级顺序
  const levels = ["city", "county", "township", "hamlet", "site"];

  // 获取基准和目标点位在层级中的索引
  const baseIndex = levels.indexOf(base);
  const targetIndex = levels.indexOf(target);

  // 如果任一点位不存在于层级列表中，返回 false
  if (baseIndex === -1 || targetIndex === -1) {
    return false;
  }

  // 判断目标点位是否高于基准点位
  return targetIndex > baseIndex;
};
