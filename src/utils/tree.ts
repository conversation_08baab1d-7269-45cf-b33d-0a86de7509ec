import type { UserInfo } from "@/store/types";
import { LocationPointEnum } from "@/types/business";
import type {
  Permission,
  RoleTreeNode
} from "@/views/system/system-role/utils/types";
import { TreeNodeType } from "@/views/system/system-user/utils/enum";
import type { TreeNode, User } from "@/views/system/system-user/utils/types";

/**
 * @description 提取菜单树中的每一项uniqueId
 * @param tree 树
 * @returns 每一项uniqueId组成的数组
 */
export const extractPathList = (tree: any[]): any => {
  if (!Array.isArray(tree)) {
    console.warn("tree must be an array");
    return [];
  }
  if (!tree || tree.length === 0) return [];
  const expandedPaths: Array<number | string> = [];
  for (const node of tree) {
    const hasChildren = node.children && node.children.length > 0;
    if (hasChildren) {
      extractPathList(node.children);
    }
    expandedPaths.push(node.uniqueId);
  }
  return expandedPaths;
};

/**
 * @description 如果父级下children的length为1，删除children并自动组建唯一uniqueId
 * @param tree 树
 * @param pathList 每一项的id组成的数组
 * @returns 组件唯一uniqueId后的树
 */
export const deleteChildren = (tree: any[], pathList = []): any => {
  if (!Array.isArray(tree)) {
    console.warn("menuTree must be an array");
    return [];
  }
  if (!tree || tree.length === 0) return [];
  for (const [key, node] of tree.entries()) {
    if (node.children && node.children.length === 1) delete node.children;
    node.id = key;
    node.parentId = pathList.length ? pathList[pathList.length - 1] : null;
    node.pathList = [...pathList, node.id];
    node.uniqueId =
      node.pathList.length > 1 ? node.pathList.join("-") : node.pathList[0];
    const hasChildren = node.children && node.children.length > 0;
    if (hasChildren) {
      deleteChildren(node.children, node.pathList);
    }
  }
  return tree;
};

/**
 * @description 创建层级关系
 * @param tree 树
 * @param pathList 每一项的id组成的数组
 * @returns 创建层级关系后的树
 */
export const buildHierarchyTree = (tree: any[], pathList = []): any => {
  if (!Array.isArray(tree)) {
    console.warn("tree must be an array");
    return [];
  }
  if (!tree || tree.length === 0) return [];
  for (const [key, node] of tree.entries()) {
    node.id = key;
    node.parentId = pathList.length ? pathList[pathList.length - 1] : null;
    node.pathList = [...pathList, node.id];
    const hasChildren = node.children && node.children.length > 0;
    if (hasChildren) {
      buildHierarchyTree(node.children, node.pathList);
    }
  }
  return tree;
};

/**
 * @description 广度优先遍历，根据唯一uniqueId找当前节点信息
 * @param tree 树
 * @param uniqueId 唯一uniqueId
 * @returns 当前节点信息
 */
export const getNodeByUniqueId = (
  tree: any[],
  uniqueId: number | string
): any => {
  if (!Array.isArray(tree)) {
    console.warn("menuTree must be an array");
    return [];
  }
  if (!tree || tree.length === 0) return [];
  const item = tree.find(node => node.uniqueId === uniqueId);
  if (item) return item;
  const childrenList = tree
    .filter(node => node.children)
    .map(i => i.children)
    .flat(1) as unknown;
  return getNodeByUniqueId(childrenList as any[], uniqueId);
};

/**
 * @description 向当前唯一uniqueId节点中追加字段
 * @param tree 树
 * @param uniqueId 唯一uniqueId
 * @param fields 需要追加的字段
 * @returns 追加字段后的树
 */
export const appendFieldByUniqueId = (
  tree: any[],
  uniqueId: number | string,
  fields: object
): any => {
  if (!Array.isArray(tree)) {
    console.warn("menuTree must be an array");
    return [];
  }
  if (!tree || tree.length === 0) return [];
  for (const node of tree) {
    const hasChildren = node.children && node.children.length > 0;
    if (
      node.uniqueId === uniqueId &&
      Object.prototype.toString.call(fields) === "[object Object]"
    )
      Object.assign(node, fields);
    if (hasChildren) {
      appendFieldByUniqueId(node.children, uniqueId, fields);
    }
  }
  return tree;
};

/**
 * @description 构造树型结构数据
 * @param data 数据源
 * @param id id字段 默认id
 * @param parentId 父节点字段，默认parentId
 * @param children 子节点字段，默认children
 * @returns 追加字段后的树
 */
export const handleTree = (
  data: any[],
  id?: string,
  parentId?: string,
  children?: string
): any => {
  if (!Array.isArray(data)) {
    console.warn("data must be an array");
    return [];
  }
  const config = {
    id: id || "id",
    parentId: parentId || "parentId",
    childrenList: children || "children"
  };

  const childrenListMap: any = {};
  const nodeIds: any = {};
  const tree = [];

  for (const d of data) {
    const parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (const d of data) {
    const parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o: Record<string, any>) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
};

/**
 * @description 节点对应赋值表单
 * @returns 构造赋值后的表单
 */
export const handleFormField = (
  node: TreeNode,
  nodeList: TreeNode[],
  field: "id" | "label" = "id"
) => {
  if (nodeList.length === 0) return {};
  const form: Partial<
    Pick<User, "city" | "county" | "township" | "site" | "hamlet">
  > = {};
  const nodeCollection = [];
  let handleNode = node;
  while (handleNode) {
    nodeCollection.push(handleNode);
    handleNode = getParentNode(handleNode, nodeList);
    if (handleNode && handleNode.level === TreeNodeType.CITY) {
      nodeCollection.push(handleNode);
      handleNode = null;
    }
  }
  nodeCollection.forEach(item => {
    switch (item.level) {
      case TreeNodeType.CITY:
        form.city = item[field];
        break;
      case TreeNodeType.COUNTY:
        form.county = item[field];
        break;
      case TreeNodeType.TOWN_SHIP:
        form.township = item[field];
        break;
      case TreeNodeType.HAMLET:
        form.hamlet = item[field];
        break;
      case TreeNodeType.SITE:
        form.site = item[field];
        break;
    }
  });
  return form;
};

/**
 * @description 获取父节点
 * @returns 父节点或null
 */
export const getParentNode = (
  node: TreeNode,
  nodeList: TreeNode[]
): TreeNode | null => {
  if (node.level === TreeNodeType.CITY) return node;
  return flattenTreeNode(nodeList).find(item => item.id === node.parentId);
};

const flattenTreeNode = (nodeList: TreeNode[]): TreeNode[] => {
  let result: TreeNode[] = [];
  nodeList.forEach(item => {
    // 先把当前项推入结果
    result.push({ ...item });

    // 如果当前项有children属性，就递归展平它们
    if (item.childList && Array.isArray(item.childList)) {
      result = result.concat(flattenTreeNode(item.childList));
    }
  });
  return result;
};

export const handlePermissionTree = (permissionList: Permission[]) => {
  const permissionTree: RoleTreeNode[] = [];

  permissionList.forEach(item => {
    let parentNode = permissionTree.find(
      node => node.permissionName === item.plate
    );

    if (!parentNode) {
      parentNode = {
        permissionCode: item.plate,
        permissionId: item.plate,
        permissionName: item.plate,
        children: []
      };
      permissionTree.push(parentNode);
    }

    parentNode.children.push({
      permissionCode: item.permissionCode,
      permissionId: item.permissionId,
      permissionName: item.remarks,
      children: []
    });
  });

  return permissionTree;
};

/**
 * 补充缺失的路径信息
 * @param {Object} currentPath 当前路径信息，可能缺少父级路径
 * @param {Object} userInfo 用户信息
 * @returns {Object} 补充后的路径信息
 */
export const fillMissingPath = (currentPath: Object, userInfo: UserInfo) => {
  const levels: LocationPointEnum[] = Object.values(LocationPointEnum);

  // 创建一个新对象来保存完整路径，先将当前路径复制过来
  const completePath = { ...currentPath };

  // 获取当前路径中已有的字段
  const currentKeys = Object.keys(currentPath);

  // 遍历层级顺序，从最小层级往上检查是否缺少字段
  let currentIndex = -1;
  for (let i = 0; i < levels.length; i++) {
    // 找到当前路径中存在的最小层级索引
    if (currentKeys.includes(levels[i])) {
      currentIndex = i;
      break;
    }
  }

  // 如果未找到任何层级，直接返回
  if (currentIndex === -1) return completePath;

  // 填充当前路径的父级路径（从当前层级的父级往上填充）
  for (let i = currentIndex - 1; i >= 0; i--) {
    const parentLevel = levels[i];
    // 如果当前路径没有这个父级，并且用户信息中有该层级字段，则填充
    if (!completePath[parentLevel] && userInfo[parentLevel]) {
      completePath[parentLevel] = userInfo[parentLevel];
    }
  }

  return completePath;
};

// 根据ID获取完整的节点路径数据
export const getNodePathById = (
  id: number,
  nodes: AreaTreeNode[]
): AreaTreeNode[] => {
  const path: AreaTreeNode[] = [];

  const findNode = (
    nodes: AreaTreeNode[] | null,
    targetId: number
  ): boolean => {
    if (!nodes) return false;

    for (const node of nodes) {
      if (node.id === targetId) {
        path.push(node);
        return true;
      }

      if (findNode(node.childList, targetId)) {
        path.unshift(node);
        return true;
      }
    }

    return false;
  };

  findNode(nodes, id);
  return path;
};

// 添加一个新的类型来处理区域树节点
export interface AreaTreeNode {
  id: number;
  parentId?: number;
  label: string;
  level: number;
  childList?: AreaTreeNode[];
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  selected?: boolean;
  highlight?: boolean;
}

// 修改 handleTreeNode 函数使用新的类型
export const handleTreeNode = (node: AreaTreeNode) => {
  return {
    city: node.city || undefined,
    county: node.county || undefined,
    township: node.township || undefined,
    hamlet: node.hamlet || undefined,
    site: node.site || undefined
  };
};
