import {
  getDeviceOverview,
  getDisposalStatistics,
  getDisposalStatisticsByMonth,
  getHotspotRank,
  getIllegalOverview,
  getMonthHotspotRank,
  getMonthIllegalOverview,
  getRealTimeAlarms
} from "@/api/largeScreen";
import {
  getViolationTypeStatistics,
  getViolationTypeAnalysisByMonth
} from "@/api/screen";
import { useAppStoreHook } from "@/store/modules/app";
import { useUserStoreHook } from "@/store/modules/user";
import { onMounted, ref, watch, onUnmounted } from "vue";
// 定义违法类型对应的颜色和显示名称
const violationTypeColors = {
  violations: {
    超员: { color: "#FF0000", label: "超员" }, // 红色
    未佩戴头盔: { color: "#FFA500", label: "未戴头盔" }, // 橙色
    加装遮阳伞: { color: "#FFFF00", label: "加装伞" }, // 黄色
    "超员+未佩戴头盔": { color: "#00FF00", label: "超员未戴头盔" }, // 绿色
    "超员+加装遮阳伞": { color: "#00FFFF", label: "超员加装伞" }, // 青色
    "未佩戴头盔+加装遮阳伞": { color: "#0000FF", label: "未戴头盔加装伞" }, // 蓝色
    "超员+未佩戴头盔+加装遮阳伞": { color: "#800080", label: "三项违法" } // 紫色
  }
};
let _pollingTimer: NodeJS.Timeout | null = null;
// 定义定时器变量
let _overviewPollingTimer: NodeJS.Timeout | null = null;
let _typePollingTimer: NodeJS.Timeout | null = null;
let _hotspotPollingTimer: NodeJS.Timeout | null = null;
let _devicePollingTimer: NodeJS.Timeout | null = null;
let _handlePollingTimer: NodeJS.Timeout | null = null;
// 修改 Point 接口
interface Point {
  id: number;
  coordinates: [number, number]; // 使用元组类型表示经纬度
  count?: number;
  status?: "online" | "offline";
}

// 宜宾市中心坐标和缩放级别
const MAP_CENTER: [number, number] = [104.633019, 28.767032];
const MAP_ZOOM = 11;

// 宜宾市区县数据
export const districtData = [
  {
    name: "翠屏区",
    center: [104.62, 28.766],
    color: "rgba(103, 194, 58, 0.2)"
  },
  {
    name: "叙州区",
    center: [104.541, 28.69],
    color: "rgba(64, 158, 255, 0.2)"
  }
  // ... 其他区县数据保持不变
];

// 生成随机经纬度 (宜宾市范围内)
const generateRandomLocation = () => {
  // 宜宾市大致范围 (根据实际边界调整)
  const lat = 28.4 + Math.random() * 0.6; // 纬度范围: 28.4 ~ 29.0
  const lng = 104.3 + Math.random() * 0.9; // 经度范围: 104.3 ~ 105.2
  return { lat, lng };
};

// 生成违法点位数据
const generateViolationPoints = (count = 20) => {
  return Array.from({ length: count }, (_, id) => {
    const { lat, lng } = generateRandomLocation();
    return {
      id,
      lat,
      lng,
      count: Math.floor(Math.random() * 100) + 10 // 10-110之间的随机数
    };
  });
};

// 生成设备点位数据
const generateDevicePoints = (count = 30) => {
  return Array.from({ length: count }, (_, id) => {
    const { lat, lng } = generateRandomLocation();
    return {
      id,
      lat,
      lng,
      type: Math.random() > 0.5 ? "camera" : "sensor",
      status: Math.random() > 0.2 ? ("online" as const) : ("offline" as const)
    };
  });
};

export function useLargeScreen() {
  // 当前时间
  const currentTime = ref(new Date().toLocaleString());

  const appStore = useAppStoreHook();

  // 时间状态管理
  // 违法概览 - 支持日期和月份选择
  const illegalOverviewDate = ref(new Date().toISOString().split("T")[0]); // 当前日期 YYYY-MM-DD
  const illegalOverviewMonth = ref(
    `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}`
  ); // 当前月份 YYYY-MM
  const illegalOverviewMode = ref<"day" | "month">("day"); // 选择模式

  // 违法类型占比 - 支持日期和月份选择
  const typeAnalysisDate = ref(new Date().toISOString().split("T")[0]);
  const typeAnalysisMonth = ref(
    `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}`
  );
  const typeAnalysisMode = ref<"day" | "month">("day");

  // 违法排名 - 支持日期和月份选择
  const hotspotDate = ref(new Date().toISOString().split("T")[0]);
  const hotspotMonth = ref(
    `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}`
  );
  const hotspotMode = ref<"day" | "month">("day");

  // 实时报警数据
  const alarmData = ref([]);

  // 日违法概览数据
  const overviewData = ref({
    total: 0,
    handled: 0,
    rate: 0,
    trend: []
  });

  // 月违法概览数据
  const monthOverviewData = ref({
    total: 0,
    handled: 0,
    rate: 0,
    trend: []
  });

  // 违法类型分布
  const typeAnalysisData = ref({
    types: [] // 直接存储类型数据数组
  });

  // 违法类型分布（月）
  const monthTypeAnalysisData = ref({
    types: []
  });

  // 设备概览数据
  const deviceData = ref({});

  // 地图点位数据
  const violationPoints = ref<Point[]>([]);

  const devicePoints = ref<Point[]>([]);

  // 高发地段排名
  const hotspotData = ref([]);

  // 预警处置统计
  const currentHandleRange = ref<"year" | "month">("month");

  // 构建默认的HandleStatistics参数
  const buildDefaultHandleParams = (
    range: string = currentHandleRange.value
  ) => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    if (range === "year") {
      return {
        range: "year",
        year: currentYear.toString()
      };
    } else {
      // 月模式：获取上个月的开始和结束日期
      const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
      const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear;

      const startOfMonth = `${lastMonthYear}-${lastMonth.toString().padStart(2, "0")}-01`;
      const endOfMonth = new Date(lastMonthYear, lastMonth, 0).getDate();
      const endOfMonthStr = `${lastMonthYear}-${lastMonth.toString().padStart(2, "0")}-${endOfMonth.toString().padStart(2, "0")}`;

      return {
        range: "month",
        startTime: startOfMonth,
        endTime: endOfMonthStr
      };
    }
  };
  const handleData = ref({
    total: 0,
    handled: 0,
    pending: 0,
    rate: 0,
    onTimeProcessingRate: 0,
    trend: {
      warning: [],
      handled: [],
      dates: []
    }
  });

  // 出勤率统计
  const attendanceData = ref({
    weekly: {
      expected: [100, 100, 100, 100, 100, 100, 100],
      actual: [96, 95, 97, 94, 96, 95, 97],
      rate: [96, 95, 97, 94, 96, 95, 97],
      trend: {
        dates: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
      }
    },
    monthly: {
      expected: Array(30).fill(100),
      actual: Array(30)
        .fill(0)
        .map(() => Math.floor(Math.random() * 10 + 90)),
      rate: Array(30).fill(0),
      trend: {
        dates: Array(30)
          .fill(0)
          .map((_, i) => `${i + 1}日`)
      }
    }
  });
  // 地图配置
  const mapConfig = ref({
    center: MAP_CENTER,
    zoom: MAP_ZOOM,
    districts: districtData // 使用原有的区县数据
  });

  // 添加数据类型状态
  const currentDataType = ref<"day" | "month">("day");

  // 高发地段排名专用数据类型
  const hotspotDataType = ref<"day" | "month">("day");

  /** 获取实时报警数据 */
  const fetchRealTimeAlarms = async () => {
    try {
      const response = await getRealTimeAlarms(appStore.largeScreenArea);
      alarmData.value = response.data.map((item: any) => ({
        id: item.id,
        wfUuid: item.wfUuid,
        type: item.alarmType,
        location: item.county + item.township + item.hamlet,
        time: item.eventTime,
        level: item.alarmLevel
      }));
    } catch (error) {
      console.error("Failed to fetch real-time alarms:", error);
    }
  };
  /** 违法类型分布数据 */
  const fetchTypeAnalysisData = async (type?: string) => {
    try {
      const currentType = type || currentDataType.value;

      // 获取地址参数
      const getAddressParams = (largeScreenArea: any) => {
        const params: any = {};
        if (largeScreenArea?.county) params.county = largeScreenArea.county;
        if (largeScreenArea?.township)
          params.township = largeScreenArea.township;
        if (largeScreenArea?.hamlet) params.hamlet = largeScreenArea.hamlet;
        if (largeScreenArea?.site) params.site = largeScreenArea.site;
        return params;
      };

      // 获取当前日期参数
      const now = new Date();
      const getCurrentDateParams = () => {
        if (currentType === "day") {
          return {
            date: now.toISOString().split("T")[0] // YYYY-MM-DD
          };
        } else {
          return {
            date: `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, "0")}` // YYYY-MM
          };
        }
      };

      const addressParams = getAddressParams(appStore.largeScreenArea);
      const dateParams = getCurrentDateParams();

      const response = await (currentType === "day"
        ? getViolationTypeStatistics({ ...addressParams, ...dateParams })
        : getViolationTypeAnalysisByMonth({ ...addressParams, ...dateParams }));

      if (response.code === 200) {
        const targetData =
          currentType === "day"
            ? typeAnalysisData.value
            : monthTypeAnalysisData.value;

        // 创建所有预定义类型的数据数组
        const allTypes = Object.entries(violationTypeColors.violations).map(
          ([name, config]) => ({
            name,
            displayName: config.label,
            value: 0,
            itemStyle: {
              color: config.color
            }
          })
        );

        // 用实际数据更新对应类型的值
        response.data.forEach((item: any) => {
          const existingType = allTypes.find(type => type.name === item.name);
          if (existingType) {
            existingType.value = item.value;
          }
        });

        targetData.types = allTypes;
      }
    } catch (error) {
      console.error("Failed to fetch type distribution data:", error);
    }
  };
  /** 获取违法概览数据（当日） */
  const fetchDayIllegalOverview = async () => {
    try {
      const response = await getIllegalOverview(appStore.largeScreenArea);
      if (response.code === 200) {
        overviewData.value = response.data;
      }
    } catch (error) {
      console.error("Failed to fetch day illegal overview:", error);
    }
  };
  /** 获取违法概览数据（当月） */
  const fetchMonthIllegalOverview = async () => {
    try {
      const response = await getMonthIllegalOverview(appStore.largeScreenArea);
      if (response.code === 200) {
        monthOverviewData.value = response.data;
      }
    } catch (error) {
      console.error("Failed to fetch month illegal overview:", error);
    }
  };

  // 新增：根据具体日期获取违法概览数据
  const fetchIllegalOverviewByDate = async (_date: string) => {
    try {
      const response = await getIllegalOverview(
        appStore.largeScreenArea,
        _date
      );
      if (response.code === 200) {
        overviewData.value = response.data;
      } else {
        console.error("DEBUG: 违法概览数据（日）获取失败", response);
      }
    } catch (error) {
      console.error("Failed to fetch illegal overview by date:", error);
    }
  };

  // 新增：根据具体月份获取违法概览数据
  const fetchIllegalOverviewByMonth = async (_month: string) => {
    try {
      const response = await getMonthIllegalOverview(
        appStore.largeScreenArea,
        _month
      );
      if (response.code === 200) {
        monthOverviewData.value = response.data;
      } else {
        console.error("DEBUG: 违法概览数据（月）获取失败", response);
      }
    } catch (error) {
      console.error("Failed to fetch illegal overview by month:", error);
    }
  };

  // 新增：根据具体日期获取违法类型分析数据
  const fetchTypeAnalysisByDate = async (_date: string) => {
    try {
      const response = await getViolationTypeStatistics({
        ...appStore.largeScreenArea,
        date: _date
      });
      if (response.code === 200) {
        const allTypes = Object.entries(violationTypeColors.violations).map(
          ([name, config]) => ({
            name,
            displayName: config.label,
            value: 0,
            itemStyle: {
              color: config.color
            }
          })
        );
        response.data.forEach((item: any) => {
          const existingType = allTypes.find(type => type.name === item.name);
          if (existingType) {
            existingType.value = item.value;
          }
        });
        typeAnalysisData.value.types = allTypes;
      }
    } catch (error) {
      console.error("Failed to fetch type analysis by date:", error);
    }
  };

  // 新增：根据具体月份获取违法类型分析数据
  const fetchTypeAnalysisByMonth = async (_month: string) => {
    try {
      const response = await getViolationTypeAnalysisByMonth({
        ...appStore.largeScreenArea,
        date: _month
      });
      if (response.code === 200) {
        const allTypes = Object.entries(violationTypeColors.violations).map(
          ([name, config]) => ({
            name,
            displayName: config.label,
            value: 0,
            itemStyle: {
              color: config.color
            }
          })
        );
        response.data.forEach((item: any) => {
          const existingType = allTypes.find(type => type.name === item.name);
          if (existingType) {
            existingType.value = item.value;
          }
        });
        monthTypeAnalysisData.value.types = allTypes;
      }
    } catch (error) {
      console.error("Failed to fetch type analysis by month:", error);
    }
  };

  // 新增：根据具体日期获取履职情况数据 (暂时保留原有逻辑)
  const fetchPersuasionByDate = async (_date: string) => {
    try {
      // 履职情况数据获取逻辑，暂时保留原有的处理方式
    } catch (error) {
      console.error("Failed to fetch persuasion by date:", error);
    }
  };

  // 新增：根据具体月份获取履职情况数据 (暂时保留原有逻辑)
  const fetchPersuasionByMonth = async (_month: string) => {
    try {
      // 履职情况数据获取逻辑，暂时保留原有的处理方式
    } catch (error) {
      console.error("Failed to fetch persuasion by month:", error);
    }
  };

  // 新增：根据具体日期获取违法排名数据
  const fetchHotspotByDate = async (_date: string) => {
    try {
      const response = await getHotspotRank(appStore.largeScreenArea, _date);
      if (response.code === 200) {
        hotspotData.value = response.data.map((item: any) => ({
          site: item.site,
          city: item.city,
          hamlet: item.hamlet,
          count: item.count,
          county: item.county,
          location: item.location,
          township: item.township
        }));
      }
    } catch (error) {
      console.error("Failed to fetch hotspot by date:", error);
    }
  };

  // 新增：根据具体月份获取违法排名数据
  const fetchHotspotByMonth = async (_month: string) => {
    try {
      const response = await getMonthHotspotRank(
        appStore.largeScreenArea,
        _month
      );
      if (response.code === 200) {
        hotspotData.value = response.data.map((item: any) => ({
          site: item.site,
          city: item.city,
          hamlet: item.hamlet,
          count: item.count,
          county: item.county,
          location: item.location,
          township: item.township
        }));
      }
    } catch (error) {
      console.error("Failed to fetch hotspot by month:", error);
    }
  };

  // 时间变化处理方法
  const handleIllegalOverviewDateChange = (date: string) => {
    illegalOverviewDate.value = date;
    fetchIllegalOverviewByDate(date);
  };

  const handleIllegalOverviewMonthChange = (month: string) => {
    illegalOverviewMonth.value = month;
    fetchIllegalOverviewByMonth(month);
  };

  const handleTypeAnalysisDateChange = (date: string) => {
    typeAnalysisDate.value = date;
    fetchTypeAnalysisByDate(date);
  };

  const handleTypeAnalysisMonthChange = (month: string) => {
    typeAnalysisMonth.value = month;
    fetchTypeAnalysisByMonth(month);
  };

  const handleHotspotDateChange = (date: string) => {
    hotspotDate.value = date;
    fetchHotspotByDate(date);
  };

  const handleHotspotMonthChange = (month: string) => {
    hotspotMonth.value = month;
    fetchHotspotByMonth(month);
  };
  /** 获取设备概览数据 */
  const fetchDeviceOverview = async () => {
    try {
      const response = await getDeviceOverview(appStore.largeScreenArea);
      deviceData.value = response.data;
    } catch (error) {
      console.error("Failed to fetch device overview:", error);
    }
  };
  // 高发地段排名（日、月）
  const fetchHotspotRank = async (type?: string) => {
    // 只有在type为undefined时才使用hotspotDataType.value，避免空字符串等情况
    const timeRange = type === undefined ? hotspotDataType.value : type;

    // 更新高发地段排名的数据类型
    if (type !== undefined) {
      hotspotDataType.value = type as "day" | "month";
    }

    if (timeRange === "day") {
      const response = await getHotspotRank(appStore.largeScreenArea);
      if (response.code === 200) {
        hotspotData.value = response.data.map((item: any) => ({
          site: item.site,
          city: item.city,
          hamlet: item.hamlet,
          count: item.count,
          county: item.county,
          location: item.location,
          township: item.township
        }));
      }
    } else {
      const response = await getMonthHotspotRank(appStore.largeScreenArea);
      if (response.code === 200) {
        hotspotData.value = response.data.map((item: any) => ({
          site: item.site,
          city: item.city,
          hamlet: item.hamlet,
          count: item.count,
          county: item.county,
          location: item.location,
          township: item.township
        }));
      }
    }
  };
  // 预警处置统计
  const fetchHandleData = async (
    params:
      | {
          range: string;
          year?: string;
          startTime?: string;
          endTime?: string;
        }
      | string
  ) => {
    try {
      // 兼容旧的string参数格式
      const rangeParams =
        typeof params === "string" ? { range: params } : params;

      currentHandleRange.value = rangeParams.range as "year" | "month";

      let response;
      if (rangeParams.range === "year") {
        // 使用年度接口
        response = await getDisposalStatistics({
          ...appStore.largeScreenArea,
          year: rangeParams.year
        });
      } else {
        // 使用月度接口
        response = await getDisposalStatisticsByMonth({
          ...appStore.largeScreenArea,
          startTime: rangeParams.startTime!,
          endTime: rangeParams.endTime!
        });
      }

      if (response.code === 200) {
        const data = response.data;

        // 适配新的数据结构
        const trendData = Array.isArray(data) ? data : [];

        // 计算汇总数据
        const totalTasks = trendData.reduce(
          (sum: number, item: any) => sum + (item.totalTasks || 0),
          0
        );
        const completedTasks = trendData.reduce(
          (sum: number, item: any) => sum + (item.completedTasks || 0),
          0
        );

        // 过滤未来的日期/月份
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1; // getMonth() 返回 0-11
        const currentDay = currentDate.getDate();

        const filteredData = trendData.filter((item: any) => {
          if (rangeParams.range === "year") {
            // 年模式：过滤未来的月份
            const month = item.month || "";
            if (month.includes("-")) {
              const [year, monthNum] = month.split("-");
              const itemYear = parseInt(year);
              const itemMonth = parseInt(monthNum);

              // 只显示当前年份之前的数据，或当前年份的当前月份之前的月份（不包括本月）
              return (
                itemYear < currentYear ||
                (itemYear === currentYear && itemMonth < currentMonth)
              );
            }
            return true;
          } else {
            // 月模式：过滤未来的日期
            const date = item.date || "";
            if (date.includes("-")) {
              const [year, month, day] = date.split("-");
              const itemYear = parseInt(year);
              const itemMonth = parseInt(month);
              const itemDay = parseInt(day);

              // 只显示当前日期及之前的数据
              const itemDate = new Date(itemYear, itemMonth - 1, itemDay);
              const today = new Date(currentYear, currentMonth - 1, currentDay);

              return itemDate <= today;
            }
            return true;
          }
        });

        // 构建trend数据
        const totalTasksTrend = filteredData.map(
          (item: any) => item.totalTasks || 0
        );
        const completedTasksTrend = filteredData.map(
          (item: any) => item.completedTasks || 0
        );
        const datesTrend = filteredData.map((item: any) => {
          if (rangeParams.range === "year") {
            // 年模式：从 "2025-01" 提取月份 "01月"
            const month = item.month || "";
            if (month.includes("-")) {
              const monthNum = month.split("-")[1];
              return `${monthNum}月`;
            }
            return month;
          } else {
            // 月模式：从 "2025-06-01" 提取日期 "01日"
            const date = item.date || "";
            if (date.includes("-")) {
              const parts = date.split("-");
              if (parts.length >= 3) {
                return `${parts[2]}日`;
              }
            }
            return date;
          }
        });

        handleData.value = {
          total: totalTasks,
          handled: completedTasks,
          pending: Math.max(0, totalTasks - completedTasks),
          rate:
            totalTasks > 0
              ? Math.round((completedTasks / totalTasks) * 100)
              : 0,
          onTimeProcessingRate: 0, // 暂时设为0，后续根据需要计算
          trend: {
            warning: totalTasksTrend,
            handled: completedTasksTrend,
            dates: datesTrend
          }
        };
      }
    } catch (error) {
      console.error("获取处置数据失败：", error);
    }
  };

  // 更新时间
  const updateTime = () => {
    setInterval(() => {
      currentTime.value = new Date().toLocaleString();
    }, 1000);
  };

  // 初始化统计数据
  const initStatisticsData = () => {
    // 更新出勤率数据
    const generateAttendanceData = (days: number) => {
      const expected = Array(days).fill(100);
      const actual = Array(days)
        .fill(0)
        .map(() => Math.floor(Math.random() * 10 + 90));
      const rate = actual.map(val => Math.round((val / 100) * 100));
      return { expected, actual, rate };
    };

    // 更新周数据
    const weeklyAttendance = generateAttendanceData(7);

    // 更新月数据
    const monthlyAttendance = generateAttendanceData(30);

    attendanceData.value = {
      weekly: {
        ...weeklyAttendance,
        trend: {
          dates: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        }
      },
      monthly: {
        ...monthlyAttendance,
        trend: {
          dates: Array(30)
            .fill(0)
            .map((_, i) => `${i + 1}日`)
        }
      }
    };

    // 初始化违法概览数据
    if (appStore.largeScreenArea) {
      if (illegalOverviewMode.value === "day") {
        fetchIllegalOverviewByDate(illegalOverviewDate.value);
      } else {
        fetchIllegalOverviewByMonth(illegalOverviewMonth.value);
      }
    }
  };

  // 初始化地图点位数据
  const initMapPoints = () => {
    // 生成更多的点位，使其分布更合理
    violationPoints.value = generateViolationPoints(50).map(item => ({
      id: item.id,
      coordinates: [item.lng, item.lat],
      count: item.count
    }));

    devicePoints.value = generateDevicePoints(80).map(item => ({
      id: item.id,
      coordinates: [item.lng, item.lat],
      status: item.status
    }));
  };

  // 数据范围改变事件
  const rangeChange = (params: {
    range: string;
    componentName: string;
    year?: string;
    startTime?: string;
    endTime?: string;
  }) => {
    switch (params.componentName) {
      case "HandleStatistics":
        fetchHandleData(params);
        break;
      case "HotspotRank":
        fetchHotspotRank(params.range);
        break;
    }
  };

  onMounted(() => {
    useUserStoreHook().initUserRegionTree();
    updateTime();
    initStatisticsData();
    initMapPoints();

    // 轮询获取违法概览数据
    _overviewPollingTimer = setInterval(() => {
      if (illegalOverviewMode.value === "day") {
        fetchIllegalOverviewByDate(illegalOverviewDate.value); //违法概览(当日)
      } else {
        fetchIllegalOverviewByMonth(illegalOverviewMonth.value); //违法概览(当月)
      }
    }, 60000);

    // 轮询获取违法类型占比
    _typePollingTimer = setInterval(() => {
      if (typeAnalysisMode.value === "day") {
        fetchTypeAnalysisByDate(typeAnalysisDate.value);
      } else {
        fetchTypeAnalysisByMonth(typeAnalysisMonth.value);
      }
    }, 60000);

    // 轮询获取高发地段排名
    _hotspotPollingTimer = setInterval(() => {
      if (hotspotMode.value === "day") {
        fetchHotspotByDate(hotspotDate.value);
      } else {
        fetchHotspotByMonth(hotspotMonth.value);
      }
    }, 60000);

    // 轮询获取设备状态
    _devicePollingTimer = setInterval(() => {
      fetchDeviceOverview();
    }, 60000);

    // 轮询获取预警处置统计
    _handlePollingTimer = setInterval(() => {
      fetchHandleData(buildDefaultHandleParams());
    }, 60000);
  });

  // 添加清理定时器的函数
  const clearPollingTimers = () => {
    if (_overviewPollingTimer) {
      clearInterval(_overviewPollingTimer);
      _overviewPollingTimer = null;
    }
    if (_typePollingTimer) {
      clearInterval(_typePollingTimer);
      _typePollingTimer = null;
    }
    if (_hotspotPollingTimer) {
      clearInterval(_hotspotPollingTimer);
      _hotspotPollingTimer = null;
    }
    if (_devicePollingTimer) {
      clearInterval(_devicePollingTimer);
      _devicePollingTimer = null;
    }
    if (_handlePollingTimer) {
      clearInterval(_handlePollingTimer);
      _handlePollingTimer = null;
    }
  };

  onUnmounted(() => {
    clearPollingTimers();
  });

  /**
   * 统一监听大屏地址选择改变事件，重新获取需要更新的数据
   * 1、在需要增加大屏地址改变后重新需要调用的接口中添加对应的参数
   * 2、将onMounted中的请求方法移动到此watch中
   * 3、如除了此hooks中的方法外，还有其他需要监听大屏地址改变的方法，同理在对应大屏组件中添加watch监听largeScreenArea并重新携带地址参数调用对应的接口
   */
  watch(
    () => appStore.largeScreenArea,
    () => {
      if (!appStore.largeScreenArea) return;
      fetchRealTimeAlarms(); //实时报警

      // 违法概览 - 根据当前选择的时间模式获取数据

      if (illegalOverviewMode.value === "day") {
        fetchIllegalOverviewByDate(illegalOverviewDate.value); //违法概览(当日)
      } else {
        fetchIllegalOverviewByMonth(illegalOverviewMonth.value); //违法概览(当月)
      }

      // 违法类型占比 - 根据当前选择的时间模式获取数据
      if (typeAnalysisMode.value === "day") {
        fetchTypeAnalysisByDate(typeAnalysisDate.value);
      } else {
        fetchTypeAnalysisByMonth(typeAnalysisMonth.value);
      }

      // 违法排名 - 根据当前选择的时间模式获取数据
      if (hotspotMode.value === "day") {
        fetchHotspotByDate(hotspotDate.value);
      } else {
        fetchHotspotByMonth(hotspotMonth.value);
      }

      fetchHandleData(buildDefaultHandleParams()); // 履职情况
      fetchDeviceOverview(); //设备状态
    },
    {
      // 组件挂载时默认执行一次，无需再onMounted中调用
      immediate: true
    }
  );

  // 添加切换数据类型的方法
  const switchDataType = (type: "day" | "month") => {
    currentDataType.value = type;
    fetchTypeAnalysisData();
  };

  return {
    currentTime,
    alarmData,
    typeAnalysisData,
    monthTypeAnalysisData,
    overviewData,
    monthOverviewData,
    deviceData,
    violationPoints,
    devicePoints,
    hotspotData,
    handleData,
    attendanceData,
    rangeChange,
    mapConfig,
    fetchDayIllegalOverview,
    fetchMonthIllegalOverview,
    fetchTypeAnalysisData,
    // 新增的日期/月份数据获取函数
    fetchIllegalOverviewByDate,
    fetchIllegalOverviewByMonth,
    fetchTypeAnalysisByDate,
    fetchTypeAnalysisByMonth,
    fetchPersuasionByDate,
    fetchPersuasionByMonth,
    fetchHotspotByDate,
    fetchHotspotByMonth,
    currentDataType,
    hotspotDataType,
    switchDataType,
    currentHandleRange,
    // 时间状态
    illegalOverviewDate,
    illegalOverviewMonth,
    illegalOverviewMode,
    typeAnalysisDate,
    typeAnalysisMonth,
    typeAnalysisMode,
    hotspotDate,
    hotspotMonth,
    hotspotMode,
    // 时间变化处理方法
    handleIllegalOverviewDateChange,
    handleIllegalOverviewMonthChange,
    handleTypeAnalysisDateChange,
    handleTypeAnalysisMonthChange,
    handleHotspotDateChange,
    handleHotspotMonthChange
  };
}

// 修改数据转换函数
export const useMapData = () => {
  const violationPoints = ref<Point[]>([]);
  const devicePoints = ref<Point[]>([]);

  const updateViolationPoints = (data: any[]) => {
    violationPoints.value = data.map(item => ({
      id: item.id,
      coordinates: [item.lng, item.lat],
      count: item.count
    }));
  };

  const updateDevicePoints = (data: any[]) => {
    devicePoints.value = data.map(item => ({
      id: item.id,
      coordinates: [item.lng, item.lat],
      status: item.status as "online" | "offline"
    }));
  };

  return {
    violationPoints,
    devicePoints,
    updateViolationPoints,
    updateDevicePoints
  };
};
