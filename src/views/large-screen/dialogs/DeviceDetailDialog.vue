<template>
  <!-- 设备状态弹窗 -->
  <div v-if="dialogVisible" class="dialog-container" @click.self="handleClose">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <div class="dialog-header">
          <div class="title">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <span class="title-text">{{ title }}</span>
          </div>
          <div class="large-screen-close-btn" @click="handleClose">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <div class="dialog-body">
          <div class="device-list">
            <div
              v-for="group in deviceGroups"
              :key="group.site"
              class="location-group"
            >
              <div class="group-header">
                <div class="location-info">
                  <el-icon><Location /></el-icon>
                  <span>{{ formatLocation(group) }}</span>
                </div>
                <div class="device-count">
                  设备数量: {{ group.devices.length }}
                </div>
              </div>

              <el-table
                :data="group.devices"
                stripe
                :max-height="calculateTableHeight(group.devices.length)"
                style="width: 100%"
              >
                <el-table-column
                  prop="deviceName"
                  label="设备名称"
                  min-width="200"
                  align="center"
                >
                  <template #default="{ row }">
                    <div class="device-name">
                      <el-icon><Monitor /></el-icon>
                      <span>{{ row.deviceName }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="maintainerPhone"
                  label="维保电话"
                  width="140"
                  align="center"
                >
                  <template #default="{ row }">
                    <div class="maintainer-phone">
                      <el-icon><Phone /></el-icon>
                      <span>{{ row.maintainerPhone || "-" }}</span>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  prop="deviceType"
                  label="设备类型"
                  width="140"
                  align="center"
                /> -->
                <!-- <el-table-column
                  prop="equipmentNumber"
                  label="设备编号"
                  width="200"
                  align="center"
                /> -->
                <el-table-column label="状态" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag :type="row.state === 0 ? 'success' : 'danger'">
                      {{ row.state === 0 ? "在线" : "离线" }}
                      <template v-if="row.state === 1">
                        <el-tooltip
                          :content="`离线时间：${row.offlineTime}\n离线时长：${row.offlineDuration}`"
                          placement="top"
                        >
                          <el-icon class="ml-1"><Warning /></el-icon>
                        </el-tooltip>
                      </template>
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template #default="{ row }">
                    <el-button type="primary" link @click="handleDetail(row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </dv-border-box-12>
    </div>

    <!-- 设备详情弹窗 -->
    <div v-if="detailVisible" class="detail-dialog">
      <div class="detail-content">
        <dv-border-box-12 class="dialog-border">
          <div class="dialog-header">
            <div class="title">
              <dv-decoration-5 style="width: 60px; height: 30px" />
              <span class="title-text">设备详情</span>
            </div>
            <div class="large-screen-close-btn" @click="closeDetail">
              <el-icon><Close /></el-icon>
            </div>
          </div>

          <div class="dialog-body detail-info">
            <div class="info-section">
              <div class="section-header">
                <div class="section-title">基本信息</div>
              </div>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">设备名称</div>
                  <div class="info-value">
                    <el-icon><Monitor /></el-icon>
                    {{ currentDevice?.deviceName }}
                  </div>
                </div>
                <!-- <div class="info-item">
                  <div class="info-label">设备类型</div>
                  <div class="info-value">
                    <el-icon><Cpu /></el-icon>
                    {{ currentDevice?.deviceType }}
                  </div>
                </div> -->
                <div class="info-item">
                  <div class="info-label">维保人员电话</div>
                  <div class="info-value">
                    <el-icon><Timer /></el-icon>
                    {{ currentDevice?.maintainerPhone }}
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">设备状态</div>
                  <div class="info-value">
                    <el-icon><Connection /></el-icon>
                    <span
                      :class="
                        currentDevice?.state === 0
                          ? 'text-success'
                          : 'text-warning'
                      "
                    >
                      {{ currentDevice?.state === 0 ? "在线" : "离线" }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="info-section">
              <div class="section-header">
                <div class="section-title">位置信息</div>
              </div>
              <div class="info-grid">
                <div class="info-item full-width">
                  <div class="info-label">经纬度</div>
                  <div class="info-value">
                    <el-icon><MapLocation /></el-icon>
                    {{ formatCoordinates(currentDevice) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </dv-border-box-12>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { getDeviceStatusList } from "@/api/device";
import type { GroupedDevice, LocationGroup } from "@/api/device";
import {
  Monitor,
  Location,
  Timer,
  InfoFilled,
  MapLocation,
  Connection,
  Cpu,
  Close,
  Warning,
  Phone
} from "@element-plus/icons-vue";

const props = defineProps<{
  visible: boolean;
  deviceState: 1 | 0; // 0-离线，1-正常
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const dialogVisible = ref(props.visible);
const deviceGroups = ref<LocationGroup[]>([]);
const detailVisible = ref(false);
const currentDevice = ref<GroupedDevice | null>(null);

const title = computed(
  () => `${props.deviceState === 0 ? "正常" : "离线"}点位列表`
);

// 获取设备列表数据
const fetchDeviceList = async () => {
  try {
    const response = await getDeviceStatusList(props.deviceState);
    if (response.code === 200) {
      deviceGroups.value = response.data;
    }
  } catch (error) {
    console.error("获取设备列表失败:", error);
  }
};

// 格式化位置信息
const formatLocation = (group: LocationGroup) => {
  return [group.city, group.county, group.township, group.hamlet, group.site]
    .filter(Boolean)
    .join(" - ");
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
  emit("update:visible", false);
};

// 处理详情关闭
const closeDetail = () => {
  detailVisible.value = false;
};

// 处理设备详情
const handleDetail = (device: GroupedDevice) => {
  currentDevice.value = device;
  detailVisible.value = true;
};

// 格式化坐标
const formatCoordinates = (device: GroupedDevice | null) => {
  if (!device) return "";
  return `${device.longitude}, ${device.latitude}`;
};

// 计算表格高度 - 显示3行数据，其余滚动
const calculateTableHeight = (dataLength: number) => {
  const headerHeight = 50; // 表头高度
  const rowHeight = 50; // 每行高度
  const maxVisibleRows = 3; // 最多显示3行
  const visibleRows = Math.min(dataLength, maxVisibleRows);

  // 如果数据少于等于3行，不需要滚动，精确计算高度
  if (dataLength <= maxVisibleRows) {
    return headerHeight + visibleRows * rowHeight;
  }

  // 如果数据超过3行，固定显示3行的高度，启用滚动
  return headerHeight + maxVisibleRows * rowHeight;
};

// 监听状态变化重新获取数据
watch(
  () => props.deviceState,
  newState => {
    fetchDeviceList();
  }
);

watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    if (val) {
      fetchDeviceList();
    }
  }
);
</script>

<style lang="scss" scoped>
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgb(103 194 58 / 40%);
  }

  70% {
    box-shadow: 0 0 0 10px rgb(103 194 58 / 0%);
  }

  100% {
    box-shadow: 0 0 0 0 rgb(103 194 58 / 0%);
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  padding: 20px;
}

// 弹窗内容
.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%); // 居中定位，不进行缩放
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 20px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .title {
    display: flex;
    gap: 12px;
    align-items: center;

    .title-text {
      font-size: 20px;
      font-weight: 500;
      color: #fff;
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.dialog-body {
  // 精确计算：弹窗总高度 - 头部高度 - 内边距
  // 1024px(弹窗高度) - 20px(上边距) - 20px(下边距) - 72px(头部+间距) = 892px
  height: 892px;
  padding-right: 10px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(64 158 255 / 30%);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.device-list {
  height: 100%; // 充分利用父容器高度
  display: flex;
  flex-direction: column;

  .location-group {
    display: flex;
    flex-direction: column;
    // 移除flex: 1，让卡片根据内容自适应高度
    min-height: 250px; // 设置最小高度，确保至少能显示3行数据
    margin-bottom: 24px;
    overflow: visible; // 改为visible，让表格内部处理滚动
    background: rgb(2 12 39 / 90%);
    border-radius: 4px;

    &:last-child {
      margin-bottom: 0; // 最后一个分组不需要下边距
    }

    .group-header {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      color: #fff;
      background: rgba(30, 40, 60, 0.8); // 深色主题分组头部
      border-bottom: 2px solid rgba(64, 158, 255, 0.3); // 蓝色底部边框

      .location-info {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 16px;

        .el-icon {
          font-size: 18px;
          color: #409eff;
        }
      }

      .device-count {
        font-size: 16px;
        color: rgb(255 255 255 / 70%);
      }
    }

    :deep(.el-table) {
      flex: 1;
      height: 100%;
      overflow: auto;
      background: transparent;
      color: rgba(255, 255, 255, 0.8);

      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgb(64 158 255 / 30%);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .el-table__inner-wrapper {
        &::before {
          background: none;
        }
      }

      .el-table__body-wrapper {
        background: transparent;
        overflow-y: auto;

        // 美化滚动条
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(64, 158, 255, 0.4);
          border-radius: 3px;

          &:hover {
            background: rgba(64, 158, 255, 0.6);
          }
        }

        &::-webkit-scrollbar-track {
          background: rgba(0, 24, 75, 0.2);
          border-radius: 3px;
        }
      }

      // 表头样式 - 完全参考HandleDetailDialog
      .el-table__header-wrapper {
        th {
          background: rgba(0, 24, 75, 0.6) !important;
          color: rgba(255, 255, 255, 0.9) !important;
          border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
          font-weight: 600 !important;
          font-size: 16px;
          height: 50px;
          padding: 12px 0;
        }
      }

      // 表格行样式 - 完全参考HandleDetailDialog
      .el-table__row {
        background: transparent;

        td {
          background: rgba(0, 24, 75, 0.3) !important;
          color: rgba(255, 255, 255, 0.8) !important;
          border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
          font-size: 16px;
          height: 50px;
          padding: 12px 0;
        }

        // 悬浮效果 - 完全参考HandleDetailDialog
        &:hover td {
          background: rgba(64, 158, 255, 0.2) !important;
        }

        // 斑马纹样式 - 完全参考HandleDetailDialog
        &.el-table__row--striped td {
          background: rgba(0, 24, 75, 0.2) !important;
        }

        // 斑马纹悬浮效果
        &.el-table__row--striped:hover td {
          background: rgba(64, 158, 255, 0.2) !important;
        }
      }

      .el-table__empty-block {
        background: rgba(0, 24, 75, 0.3);
        color: rgba(255, 255, 255, 0.6);
      }

      &::before {
        display: none;
      }

      .el-checkbox__inner {
        background-color: transparent;
        border-color: rgba(64, 158, 255, 0.5);
      }

      // 表格边框 - 参考HandleDetailDialog，去掉右边框
      td,
      th {
        border-right: none !important;
      }

      // 排序相关样式
      th.is-sortable:hover {
        background-color: rgba(64, 158, 255, 0.25) !important;
        color: #ffffff !important;
        transition: all 0.3s ease;
      }

      th.ascending,
      th.descending {
        background-color: rgba(64, 158, 255, 0.3) !important;
        color: #ffffff !important;

        .sort-caret.ascending {
          border-bottom-color: #79bbff;
        }

        .sort-caret.descending {
          border-top-color: #79bbff;
        }
      }
    }
  }
}

.device-name {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 16px;
  color: rgb(255 255 255 / 90%);
  transition: all 0.3s ease;

  .el-icon {
    font-size: 18px;
    color: #409eff;
    transition: all 0.3s ease;
  }

  // 悬浮时图标发光效果
  tr:hover & {
    .el-icon {
      color: #79bbff;
      filter: drop-shadow(0 0 4px rgb(64 158 255 / 50%));
    }
  }
}

.detail-dialog {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2100;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgb(0 0 0 / 70%);
  backdrop-filter: blur(10px);
}

.detail-content {
  width: 60%;
  height: 60%;
  animation: zoomIn 0.3s ease-out;
}

.detail-info {
  padding: 0 20px;

  .info-section {
    margin-bottom: 20px;
    background: rgb(2 12 39 / 90%);
    border-radius: 4px;

    .section-header {
      padding: 12px 16px;
      background: rgb(71 84 125 / 50%);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;

      .section-title {
        position: relative;
        padding-left: 12px;
        font-size: 14px;
        color: #fff;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 3px;
          height: 14px;
          content: "";
          background: #409eff;
          border-radius: 2px;
          transform: translateY(-50%);
        }
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0;

      .info-item {
        display: flex;
        padding: 12px 16px;
        border-right: 1px solid rgb(71 84 125 / 30%);
        border-bottom: 1px solid rgb(71 84 125 / 30%);

        &:nth-child(2n) {
          border-right: none;
        }

        &:last-child,
        &:nth-last-child(2):not(:nth-child(2n)) {
          border-bottom: none;
        }

        &.full-width {
          grid-column: 1 / -1;
          border-right: none;
        }

        .info-label {
          width: 80px;
          font-size: 14px;
          color: rgb(255 255 255 / 70%);
        }

        .info-value {
          display: flex;
          flex: 1;
          gap: 8px;
          align-items: center;
          color: rgb(255 255 255 / 90%);

          .el-icon {
            color: #409eff;
          }
        }
      }
    }
  }
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #f56c6c;
}

.status-dot {
  position: relative;
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.online {
    background: #67c23a;
    box-shadow: 0 0 8px rgb(103 194 58 / 50%);
  }

  &.offline {
    background: #f56c6c;
    box-shadow: 0 0 8px rgb(245 108 108 / 50%);
  }

  .pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
  }
}

:deep(.el-tag) {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 2px 8px;
  background: transparent;
  border: none;

  &.el-tag--success {
    color: #67c23a;
  }

  &.el-tag--danger {
    color: #f56c6c;
  }

  .el-icon {
    font-size: 14px;
  }
}

:deep(.el-button--primary.el-button--link) {
  color: #409eff;

  &:hover {
    color: #79bbff;
  }
}

.text-muted {
  font-size: 12px;
  color: rgb(255 255 255 / 50%);
}

.ml-1 {
  margin-left: 4px;
}

// 修改分页器样式
:deep(.el-pagination) {
  .el-pagination__total,
  .el-pagination__jump {
    color: rgb(255 255 255 / 70%);
  }

  .btn-prev,
  .btn-next {
    color: rgb(255 255 255 / 70%);
    background-color: transparent;

    &:disabled {
      color: rgb(255 255 255 / 30%);
    }
  }

  .el-pager li {
    color: rgb(255 255 255 / 70%);
    background-color: transparent;

    &:hover {
      color: #409eff;
    }

    &.active {
      color: #fff;
      background-color: #409eff;
    }
  }
}

.maintainer-phone {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: rgb(255 255 255 / 90%);
  transition: all 0.3s ease;

  .el-icon {
    font-size: 18px;
    color: #409eff;
    transition: all 0.3s ease;
  }

  // 悬浮时图标发光效果
  tr:hover & {
    .el-icon {
      color: #79bbff;
      filter: drop-shadow(0 0 4px rgb(64 158 255 / 50%));
    }
  }
}
</style>
