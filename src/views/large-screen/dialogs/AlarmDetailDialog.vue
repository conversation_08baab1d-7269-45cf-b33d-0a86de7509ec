<template>
  <!-- 严重违法预警弹窗 -->
  <teleport to="body">
    <div v-if="visible" class="dialog-container">
      <div :key="currentWfUuid" class="dialog-content">
        <div class="dialog-border">
          <div class="dialog-header">
            <div class="title">
              <el-icon class="title-icon"><Document /></el-icon>
              <span class="title-text">报警详情</span>
            </div>
            <div class="header-controls">
              <el-button
                v-if="currentIndex > 0"
                type="primary"
                plain
                @click="prevAlarm"
                >上一条</el-button
              >
              <el-button
                v-if="currentIndex < totalAlarms - 1"
                type="primary"
                plain
                @click="nextAlarm"
                >下一条</el-button
              >
              <span class="header-index"
                >{{ currentIndex + 1 }} / {{ totalAlarms }}</span
              >
              <div class="large-screen-close-btn" @click="handleClose">
                <el-icon><Close /></el-icon>
              </div>
            </div>
          </div>
          <div class="dialog-body">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
              <el-icon class="is-loading loading-icon"><Loading /></el-icon>
              <span class="loading-text">正在加载数据...</span>
            </div>

            <!-- 数据内容 -->
            <template v-else-if="detailData && detailData.illegalRecords">
              <div class="info-card">
                <div class="card-title">基本信息</div>
                <div class="info-grid">
                  <div class="info-col">
                    <div class="info-row">
                      <span class="label">抓拍时间：</span
                      ><span class="value">{{
                        detailData.illegalRecords?.captureTime ||
                        detailData.eventTime ||
                        "-"
                      }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">违法类型：</span
                      ><span class="value">{{
                        getIllegalType(detailData.illegalRecords)
                      }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">车辆类型：</span
                      ><span class="value">{{
                        getVehicleType(detailData.illegalRecords)
                      }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">现场劝导：</span
                      ><span class="value">{{
                        getPersuasiveType(detailData.illegalRecords)
                      }}</span>
                    </div>
                  </div>
                  <div class="info-col">
                    <div class="info-row">
                      <span class="label">报警地点：</span
                      ><span class="value">{{
                        getFullAddress(detailData.illegalRecords)
                      }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">车牌号：</span
                      ><span class="value">{{
                        detailData.illegalRecords?.plateNumber || "-"
                      }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">实载人数：</span
                      ><span class="value">{{
                        detailData.illegalRecords?.numberOfPassengers || "-"
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="image-card">
                <div class="card-title">
                  现场图片
                  <span class="img-count"
                    >（共{{ pictureUrls.length }}张）</span
                  >
                </div>
                <div class="image-list">
                  <template v-if="pictureUrls && pictureUrls.length > 0">
                    <div
                      v-for="(url, index) in pictureUrls"
                      :key="index"
                      class="image-item"
                      @click="onPreviewImg(pictureUrls, index)"
                    >
                      <el-image
                        :src="url"
                        fit="cover"
                        class="img"
                        loading="lazy"
                      >
                        <template #error>
                          <div class="image-error">
                            <div class="error-icon-wrapper">
                              <el-icon class="error-icon"><Picture /></el-icon>
                            </div>
                            <span class="error-text">加载失败</span>
                            <div class="error-retry">点击重试</div>
                          </div>
                        </template>
                        <template #placeholder>
                          <div class="image-loading">
                            <div class="loading-skeleton">
                              <div class="skeleton-shimmer" />
                            </div>
                            <div class="loading-content">
                              <div class="loading-spinner">
                                <div class="spinner-ring" />
                                <div class="spinner-ring" />
                                <div class="spinner-ring" />
                              </div>
                              <span class="loading-text">加载中...</span>
                            </div>
                          </div>
                        </template>
                      </el-image>
                    </div>
                  </template>
                  <div v-else class="image-placeholder">
                    <el-icon class="placeholder-icon"><Picture /></el-icon>
                    <span class="placeholder-text">暂无图片</span>
                  </div>
                </div>
              </div>
            </template>

            <!-- 错误状态 -->
            <el-empty v-else description="加载数据失败">
              <template #image>
                <el-icon :size="48" class="text-gray-400"><Warning /></el-icon>
              </template>
              <template #description>
                <p class="text-warning mb-4">数据加载失败，请稍后重试</p>
                <el-button type="primary" @click="fetchDetail(currentWfUuid)"
                  >重新加载</el-button
                >
              </template>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import {
  Close,
  Picture,
  Warning,
  Document,
  Loading
} from "@element-plus/icons-vue";
import type { AlarmItem } from "../types";
import { ref, watch, computed } from "vue";
import { getAlarmDetail } from "@/api/alarm";
import { ElMessage } from "element-plus";
import { usePreviewImg } from "@/hooks/usePreviewImg";

// 定义组件属性
const props = defineProps<{
  visible: boolean;
  alarmData: AlarmItem | null;
  allAlarms?: AlarmItem[]; // 添加所有报警项列表
}>();

// 定义事件
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "refresh"): void;
}>();

const { onPreviewImg } = usePreviewImg();
const loading = ref(false);
const detailData = ref<any>(null);
const currentWfUuid = ref<string>("");
const pictureUrls = ref<string[]>([]);

// 添加重试计数器
const retryCount = ref(0);
const maxRetries = 3;

// 验证数据完整性
const validateDetailData = (data: any): boolean => {
  if (!data) {
    console.warn("数据为空");
    return false;
  }

  if (!data.illegalRecords) {
    console.warn("缺少 illegalRecords 数据");
    return false;
  }

  // 检查关键字段（改为警告而不是阻止显示）
  const requiredFields = ["captureTime", "plateNumber"];
  const missingFields = requiredFields.filter(
    field => !data.illegalRecords[field] && data.illegalRecords[field] !== ""
  );

  if (missingFields.length > 0) {
    console.warn("缺少关键字段:", missingFields, "但继续显示数据");
  }

  return true;
};

// 当前报警在列表中的索引
const currentIndex = ref(0);
// 报警总数
const totalAlarms = computed(() => props.allAlarms?.length || 0);

// 获取详情数据
const fetchDetail = async (wfUuid: string) => {
  if (!wfUuid) {
    console.warn("wfUuid 为空，无法获取详情");
    return;
  }

  try {
    loading.value = true;
    // 清空之前的数据，避免显示旧数据
    detailData.value = null;
    pictureUrls.value = [];

    // 这里使用 wfUuid 值作为 uuid 参数传给后端
    const res = await getAlarmDetail(wfUuid);

    if (res.code === 200 && res.data) {
      // 验证数据结构和完整性
      if (validateDetailData(res.data)) {
        detailData.value = res.data;
        retryCount.value = 0; // 重置重试计数器

        // 解析图片URL
        try {
          const pictureUrl = detailData.value.illegalRecords.pictureUrl;
          if (pictureUrl) {
            if (typeof pictureUrl === "string") {
              pictureUrls.value = JSON.parse(pictureUrl);
            } else if (Array.isArray(pictureUrl)) {
              pictureUrls.value = pictureUrl;
            } else {
              pictureUrls.value = [];
            }
          } else if (detailData.value.pictureUrl) {
            if (typeof detailData.value.pictureUrl === "string") {
              pictureUrls.value = JSON.parse(detailData.value.pictureUrl);
            } else if (Array.isArray(detailData.value.pictureUrl)) {
              pictureUrls.value = detailData.value.pictureUrl;
            } else {
              pictureUrls.value = [];
            }
          } else {
            pictureUrls.value = [];
          }
        } catch (e) {
          console.error("解析图片URL失败:", e);
          pictureUrls.value = [];
        }
      } else {
        console.error("数据结构验证失败，缺少 illegalRecords:", res.data);

        // 尝试重试
        if (retryCount.value < maxRetries) {
          retryCount.value++;
          setTimeout(() => {
            fetchDetail(wfUuid);
          }, 1000 * retryCount.value); // 递增延迟
          return;
        }

        ElMessage.error("数据结构异常，请联系管理员");
        detailData.value = null;
      }
    } else {
      console.error("获取报警详情失败:", res);

      // 尝试重试
      if (retryCount.value < maxRetries) {
        retryCount.value++;
        setTimeout(() => {
          fetchDetail(wfUuid);
        }, 1000 * retryCount.value); // 递增延迟
        return;
      }

      ElMessage.error(res.message || "获取报警详情失败");
      detailData.value = null;
    }
  } catch (error) {
    console.error("获取报警详情请求异常:", error);

    // 尝试重试
    if (retryCount.value < maxRetries) {
      retryCount.value++;
      setTimeout(() => {
        fetchDetail(wfUuid);
      }, 1000 * retryCount.value); // 递增延迟
      return;
    }

    ElMessage.error("获取报警详情失败，请稍后重试");
    detailData.value = null;
  } finally {
    loading.value = false;
  }
};

// 获取完整地址
const getFullAddress = (record: any) => {
  if (!record) return "-";
  const parts = [
    record.city,
    record.county,
    record.township,
    // record.hamlet,
    record.site
  ].filter(Boolean);
  return parts.length ? parts.join(" - ") : "-";
};

// 获取非法类型
const getIllegalType = (record: any) => {
  if (!record) return "-";
  return record.illegalType.desc || "-";
};

// 获取车辆类型
const getVehicleType = (record: any) => {
  if (!record) return "-";
  return record.vehicleType.desc || "-";
};

// 获取劝导类型
const getPersuasiveType = (record: any) => {
  if (!record) return "-";
  return record.persuasiveBehavior.desc || "-";
};

// 重置组件状态
const resetComponentState = () => {
  detailData.value = null;
  pictureUrls.value = [];
  currentWfUuid.value = "";
  currentIndex.value = 0;
  loading.value = false;
  retryCount.value = 0;
};

// 处理关闭对话框
const handleClose = () => {
  resetComponentState();
  emit("update:visible", false);
};

// 导航到上一条报警
const prevAlarm = () => {
  if (currentIndex.value > 0 && props.allAlarms) {
    currentIndex.value--;
    const prevItem = props.allAlarms[currentIndex.value];
    if (prevItem && prevItem.wfUuid) {
      currentWfUuid.value = prevItem.wfUuid;
      fetchDetail(prevItem.wfUuid);
    }
  }
};

// 导航到下一条报警
const nextAlarm = () => {
  if (currentIndex.value < totalAlarms.value - 1 && props.allAlarms) {
    currentIndex.value++;
    const nextItem = props.allAlarms[currentIndex.value];
    if (nextItem && nextItem.wfUuid) {
      currentWfUuid.value = nextItem.wfUuid;
      fetchDetail(nextItem.wfUuid);
    }
  }
};

// 监听报警数据变化
watch(
  () => props.alarmData,
  (newVal, oldVal) => {
    if (newVal && newVal.wfUuid) {
      // 只有在弹窗显示时才处理数据变化
      if (props.visible) {
        currentWfUuid.value = newVal.wfUuid;

        // 设置当前索引
        if (props.allAlarms && props.allAlarms.length > 0) {
          const index = props.allAlarms.findIndex(
            item => item.wfUuid === newVal.wfUuid
          );
          if (index !== -1) {
            currentIndex.value = index;
          }
        }

        // 重新加载数据
        fetchDetail(newVal.wfUuid);
      } else {
        // 弹窗未显示时，只保存数据，不立即加载
        currentWfUuid.value = newVal.wfUuid;

        if (props.allAlarms && props.allAlarms.length > 0) {
          const index = props.allAlarms.findIndex(
            item => item.wfUuid === newVal.wfUuid
          );
          if (index !== -1) {
            currentIndex.value = index;
          }
        }
      }
    }
  },
  { immediate: false } // 改为 false，避免初始化时的重复调用
);

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal) {
      // 弹窗打开时
      document.body.classList.add("dialog-visible");

      // 确保有数据时才加载
      if (props.alarmData && props.alarmData.wfUuid) {
        currentWfUuid.value = props.alarmData.wfUuid;

        // 设置当前索引
        if (props.allAlarms && props.allAlarms.length > 0) {
          const index = props.allAlarms.findIndex(
            item => item.wfUuid === props.alarmData.wfUuid
          );
          if (index !== -1) {
            currentIndex.value = index;
          }
        }

        // 强制重新加载数据
        fetchDetail(props.alarmData.wfUuid);
      } else if (currentWfUuid.value) {
        fetchDetail(currentWfUuid.value);
      } else {
        console.warn("没有可用的 wfUuid，无法加载数据");
      }
    } else {
      // 弹窗关闭时
      document.body.classList.remove("dialog-visible");
      // 延迟清理状态，避免关闭动画时看到空白
      setTimeout(() => {
        resetComponentState();
      }, 300);
    }
  }
);
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
/* 动画定义 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  padding: 20px;
}

// 弹窗内容
.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%) scale(var(--scale-ratio)); // 居中定位，保持大屏页面相同缩放比例
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
}

// 边框容器
.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
  display: flex;
  flex-direction: column;
  background: rgb(8 24 48 / 98%);
  border: 1.5px solid #00eaff55;
  border-radius: 16px;
  box-shadow: 0 0 32px 0 #00eaff44;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .title {
    display: flex;
    gap: 12px;
    align-items: center;

    .title-icon {
      @extend .fs-dialog-title; /* 对话框标题图标大小 */
      color: #00eaff;
    }

    .title-text {
      @extend .fs-dialog-title; /* 对话框主标题文字大小 */
      font-weight: 600;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }

  .header-controls {
    display: flex;
    gap: 16px;
    align-items: center;

    .header-index {
      margin: 0 8px;
      @extend .fs-dialog-title; /* 报警索引显示文字大小 (如 1/5) */
      color: #e6f7ff;
    }

    // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
  }
}

.dialog-body {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 32px;
  overflow-y: auto;
}

.info-card,
.image-card {
  position: relative;
  padding: 24px 32px 20px;
  margin-bottom: 32px;
  background: rgb(12 32 64 / 95%);
  border: 1px solid #1e4e8c;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 #00eaff22;
}

.info-card {
  min-height: 200px;
}

.image-card {
  margin-bottom: 0;
}

.card-title {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 18px;
  @extend .fs-dialog-title; /* 卡片标题文字大小 (基本信息、现场图片) */
  font-weight: 600;
  color: #00eaff;
  letter-spacing: 1px;
}

.info-grid {
  display: flex;
  gap: 48px;
}

.info-col {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 18px;
}

.info-row {
  display: flex;
  gap: 8px;
  @extend .fs-alarm-dialog-title; /* 基本信息行文字大小 (标签和值) */
  color: #e6f7ff;

  .label {
    min-width: 90px;
    font-weight: 500;
    color: #8ec6ff;
  }

  .value {
    font-weight: 400;
    /* 继承父级颜色 #e6f7ff，无需重复定义 */
  }
}

.image-card {
  .img-count {
    margin-left: 8px;
    @extend .fs-alarm-dialog-title; /* 图片数量显示文字大小 (共X张) */
    font-weight: 400;
    color: #e6f7ff99;
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    margin-top: 8px;

    .image-item {
      width: 400px;
      height: 250px;
      overflow: hidden;
      cursor: pointer;
      background: #111c2a;
      border: 1.5px solid #00eaff33;
      border-radius: 10px;
      box-shadow: 0 2px 8px #00eaff22;
      transition:
        box-shadow 0.2s,
        border 0.2s;

      &:hover {
        border: 1.5px solid #00eaff;
        box-shadow: 0 4px 16px #00eaff55;
      }

      .img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .image-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 400px;
      height: 250px;
      @extend .fs-alarm-dialog-no-data-text; /* 图片占位符文字大小 (暂无图片) */
      color: #8ec6ff;
      background: #111c2a;
      border: 1.5px dashed #00eaff33;
    }
  }
}

/* 图片加载状态样式 */
.image-loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #0a1e3a 0%, #1a2f4a 50%, #0a1e3a 100%);

  .loading-skeleton {
    position: absolute;
    inset: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgb(0 234 255 / 10%) 50%,
      transparent 100%
    );

    .skeleton-shimmer {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgb(0 234 255 / 15%) 25%,
        rgb(0 234 255 / 30%) 50%,
        rgb(0 234 255 / 15%) 75%,
        transparent 100%
      );
      animation: shimmer 2s infinite;
    }
  }

  .loading-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .loading-spinner {
    position: relative;
    width: 40px;
    height: 40px;

    .spinner-ring {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1.5s linear infinite;

      &:nth-child(1) {
        border-top-color: #00eaff;
        animation-delay: 0s;
      }

      &:nth-child(2) {
        top: 10%;
        left: 10%;
        width: 80%;
        height: 80%;
        border-right-color: #00eaff;
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        top: 20%;
        left: 20%;
        width: 60%;
        height: 60%;
        border-bottom-color: #00eaff;
        animation-delay: 0.6s;
      }
    }
  }

  .loading-text {
    @extend .fs-alarm-dialog-no-data-text; /* 图片加载中文字大小 */
    font-weight: 500;
    color: #00eaff;
    text-shadow: 0 0 8px rgb(0 234 255 / 30%);
    letter-spacing: 0.5px;
  }
}

/* 图片错误状态样式 */
.image-error {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background: linear-gradient(135deg, #2a1a1a 0%, #3a2a2a 50%, #2a1a1a 100%);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #3a2a2a 0%, #4a3a3a 50%, #3a2a2a 100%);

    .error-icon-wrapper {
      transform: scale(1.1);
    }
  }

  .error-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgb(255 107 107 / 10%);
    border: 2px solid rgb(255 107 107 / 30%);
    border-radius: 50%;
    transition: transform 0.3s ease;

    .error-icon {
      @extend .fs-alarm-dialog-no-data-text; /* 图片加载错误图标大小 */
      color: #ff6b6b;
    }
  }

  .error-text {
    @extend .fs-alarm-dialog-no-data-text; /* 图片加载错误提示文字大小 */
    font-weight: 500;
    color: #ff6b6b;
  }

  .error-retry {
    @extend .fs-alarm-dialog-no-data-text; /* 图片重试提示文字大小 */
    color: #8ec6ff;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  &:hover .error-retry {
    opacity: 1;
  }
}

/* 主加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  justify-content: center;
  min-height: 300px;

  .loading-icon {
    font-size: 48px; /* 主加载状态图标大小 */
    color: #00eaff;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    @extend .fs-alarm-dialog-no-data-text; /* 主加载状态文字大小 (正在加载数据...) */
    font-weight: 500;
    color: #e6f7ff;
    letter-spacing: 1px;
  }
}
</style>

<style lang="scss">
/* 全局样式，不加 scoped */
body.dialog-visible {
  .map-legends,
  .mapboxgl-ctrl-bottom-right,
  .mapboxgl-ctrl-bottom-left {
    visibility: hidden !important;
  }
}
</style>
