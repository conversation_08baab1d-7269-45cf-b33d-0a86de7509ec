<template>
  <!-- 地图弹窗 -->
  <div v-if="visible" class="dialog-container" @click.self="handleClose">
    <VideoDialog v-model:visible="videoDialogVisible" :devices="videoDevices" />
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <div class="dialog-header">
          <div class="title">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <span class="title-text">{{ title }}</span>
          </div>
          <div class="large-screen-close-btn" @click="handleClose">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <div v-if="pointData" class="dialog-body">
          <div class="point-info">
            <div class="flex-row">
              <div class="section device-info">
                <h3 class="section-title">设备信息</h3>
                <div class="device-list">
                  <el-table
                    :data="pointData?.devices || []"
                    stripe
                    style="width: 100%"
                    height="262px"
                  >
                    <el-table-column
                      prop="name"
                      label="设备名称"
                      min-width="220"
                      align="center"
                    >
                      <template #default="{ row }">
                        <div
                          class="device-name"
                          style="cursor: pointer"
                          @click="handleViewVideo(row)"
                        >
                          <el-icon>
                            <component :is="getDeviceIcon(row.type)" />
                          </el-icon>
                          <span>{{ row.name }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态">
                      <template #default="{ row }">
                        <el-tag
                          :type="
                            getDeviceStatusText(row.status) === '在线'
                              ? 'success'
                              : 'danger'
                          "
                        >
                          {{ getDeviceStatusText(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="maintainerPhone"
                      label="维保电话"
                      align="center"
                      min-width="150"
                    />
                  </el-table>
                </div>
              </div>
              <div class="section staff-info">
                <h3 class="section-title">上岗时间</h3>
                <div class="staff-list">
                  <div class="shift-time-container">
                    <div class="shift-time-section">
                      <div class="shift-time-header">上午班次</div>
                      <div class="shift-time-list">
                        <div
                          v-for="shift in getUniqueShiftTimes('AM')"
                          :key="shift.time"
                          class="time-item"
                        >
                          <span class="shift-name">{{ shift.name }}</span>
                          <span class="shift-time">{{ shift.time }}</span>
                        </div>
                        <div
                          v-if="getUniqueShiftTimes('AM').length === 0"
                          class="no-shifts"
                        >
                          暂无班次
                        </div>
                      </div>
                    </div>

                    <div class="shift-time-section">
                      <div class="shift-time-header">下午班次</div>
                      <div class="shift-time-list">
                        <div
                          v-for="shift in getUniqueShiftTimes('PM')"
                          :key="shift.time"
                          class="time-item"
                        >
                          <span class="shift-name">{{ shift.name }}</span>
                          <span class="shift-time">{{ shift.time }}</span>
                        </div>
                        <div
                          v-if="getUniqueShiftTimes('PM').length === 0"
                          class="no-shifts"
                        >
                          暂无班次
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="section attendance-info">
                <div class="section-title">
                  <span>当前状态</span>
                </div>
                <div class="duty-content">
                  <div class="staff-list">
                    <el-table
                      :data="getAllStaffList() || []"
                      stripe
                      height="262px"
                      style="width: 100%"
                      :header-cell-style="{
                        background: 'rgba(0, 24, 75, 0.6)',
                        color: 'rgba(255, 255, 255, 0.8)',
                        borderBottom: '1px solid rgba(64, 158, 255, 0.2)'
                      }"
                      :cell-style="{
                        background: 'rgba(0, 24, 75, 0.3)',
                        color: 'rgba(255, 255, 255, 0.8)',
                        borderBottom: '1px solid rgba(64, 158, 255, 0.1)'
                      }"
                    >
                      <el-table-column
                        prop="name"
                        align="center"
                        label="姓名"
                        width="120"
                        :resizable="false"
                      />
                      <el-table-column
                        prop="deptName"
                        align="center"
                        label="岗位"
                        width="80"
                        :resizable="false"
                      >
                        <template #default="{ row }">
                          <span
                            :class="{
                              'special-staff': row.deptName?.includes('专职')
                            }"
                          >
                            {{ row.deptName }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="phone"
                        align="center"
                        label="手机号"
                        width="160"
                      />
                      <el-table-column
                        label="上岗状态"
                        align="center"
                        width="120"
                      >
                        <template #default="{ row }">
                          <el-tag
                            v-if="getDisplayStatus(row)"
                            :type="getStatusTagType(getDisplayStatus(row))"
                          >
                            {{ getDisplayStatus(row) }}
                          </el-tag>
                          <span v-else class="empty-status">-</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
            <div class="section violation-info">
              <div class="section-header">
                <h3 class="section-title">违法类型统计</h3>
                <div class="time-filter">
                  <TimeRangeSelector
                    v-model="violationTimeRange"
                    :enabled-ranges="['day', 'month', 'year']"
                    :custom-labels="{
                      day: '今日',
                      month: '本月',
                      year: '全年'
                    }"
                    :disable-future="true"
                    @change="handleViolationTimeRangeChange"
                  />
                </div>
              </div>
              <div
                class="violation-cards"
                :class="{
                  'odd-cards': (violationData?.details?.length || 0) % 4 !== 0
                }"
              >
                <div
                  v-for="item in violationData?.details || []"
                  :key="item.type"
                  class="violation-card"
                >
                  <div class="card-header">
                    <el-icon class="icon"><Warning /></el-icon>
                    <span class="type-name">{{ item.type }}</span>
                  </div>
                  <div class="card-body">
                    <div class="stat-row">
                      <span class="label">违法数</span>
                      <span class="value">{{ item.count }}</span>
                    </div>
                    <div class="stat-row">
                      <span class="label">已下派</span>
                      <span class="value">{{ item.handled }}</span>
                    </div>
                    <div class="stat-row highlight">
                      <span class="label">下派率</span>
                      <span class="value">{{ getHandleRate(item) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="section chart-info">
              <!-- 趋势图 -->
              <div class="trend-chart-info">
                <div class="chart-header">
                  <div class="title">
                    <span class="title-text" style="cursor: pointer">
                      违法趋势概览
                    </span>
                  </div>
                  <TimeRangeSelector
                    v-model="selectedView"
                    :enabled-ranges="['day', 'month', 'year']"
                    :custom-labels="{
                      day: '今日',
                      month: '本月',
                      year: '全年'
                    }"
                    :disable-future="true"
                    @change="handleTimeChange"
                  />
                </div>
                <div ref="trendChartRef" class="trend-chart" />
              </div>
              <!-- 精准劝导概览 -->
              <HandleStatistics
                :data="handleStatisticsData"
                @range-change="handleStatisticsRangeChange"
              />
            </div>
          </div>
        </div>

        <dv-decoration-3
          class="dialog-footer"
          style="width: 100%; height: 4px"
        />
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getDisposalStatistics,
  getDisposalStatisticsByMonth
} from "@/api/largeScreen";
import {
  getIllegalTrendByDay,
  getIllegalTrendByMonth,
  getIllegalTrendByYear
} from "@/api/screen";
import { useAppStoreHook } from "@/store/modules/app";
import VideoDialog from "../components/VideoDialog.vue";
import HandleStatistics from "../components/HandleStatistics.vue";
import TimeRangeSelector from "@/components/TimeRangeSelector.vue";
import {
  AlarmClock,
  Bell,
  Close,
  Cpu,
  Monitor,
  Select,
  Timer,
  TrendCharts,
  VideoCamera,
  Warning
} from "@element-plus/icons-vue";
import dayjs from "dayjs";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import {
  defineEmits,
  defineProps,
  nextTick,
  onMounted,
  ref,
  watch,
  onUnmounted
} from "vue";
import { getPointViolationStats } from "@/api/largeScreen";
import { PointData, Device, Shift, Staff } from "../types/map";

// 更新类型定义
interface LeavePostDetail {
  startTime: string;
  endTime: string;
  duration: number;
  status: string;
}

interface StaffShift {
  scheduleId: number;
  shiftName: string;
  startTime: string;
  endTime: string;
  checkInTime: string;
  checkOutTime: string;
  status: string;
  statusText?: string;
  leavePostDetails: LeavePostDetail[];
}

interface StaffInfo {
  userId: number;
  name: string;
  phone: string;
  shifts: StaffShift[];
  hasAbnormal: boolean;
}

// props 定义
const props = defineProps<{
  visible: boolean;
  title: string;
  pointData?: PointData;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();
const videoDialogVisible = ref(false);
const videoDevices = ref<
  Array<{
    id: number;
    deviceName: string;
    streamKey: string;
    equipmentNumber: string;
  }>
>([]);

const appStore = useAppStoreHook();

// 时间范围相关
const selectedView = ref<"day" | "month" | "year">("day");
const violationTimeRange = ref<"day" | "month" | "year">("day");

// 违法趋势统计数据
const trendData = ref<{
  series: { data: number[]; name: string }[];
  categories: string[];
}>({
  series: [],
  categories: []
});
const trendChartRef = ref<HTMLElement>();
let trendChart: echarts.ECharts | null = null;

// 精准劝导统计 - 适配HandleStatistics组件
const handleStatisticsData = ref({
  total: 0,
  handled: 0,
  pending: 0,
  rate: 0,
  onTimeProcessingRate: 0,
  trend: {
    warning: [],
    handled: [],
    dates: []
  }
});

// 注意：原有的handleData已被handleStatisticsData替代

// 当前选中的人员类型
const currentStaffType = ref<"正常出勤" | "异常出勤" | "待上岗">("正常出勤");

// 违法统计数据
const violationData = ref(null);

// 添加currentTab的定义和初始化
const currentTab = ref(new Date().getHours() < 12 ? "am" : "pm");
const isSiteGreen = ref(false); // 添加站点颜色状态标记

const handleClose = () => {
  emit("update:visible", false);
  videoDialogVisible.value = false;
};

const handleViewVideo = (row: {
  type: string;
  streamKey: string;
  equipmentNumber: string;
  deviceName?: string;
}) => {
  if (row.type !== "摄像机") {
    ElMessage.warning("只有摄像机设备可以查看视频");
    return;
  }
  if (!row.streamKey) {
    ElMessage.warning("该设备未配置流密钥，无法查看视频");
    return;
  }

  // 构造设备数组以兼容大屏VideoDialog
  videoDevices.value = [
    {
      id: 1,
      deviceName: row.deviceName || "监控设备",
      streamKey: row.streamKey,
      equipmentNumber: row.equipmentNumber
    }
  ];

  videoDialogVisible.value = true;
};

const getHandleRate = (item: any) => {
  if (!item.count) return 0;
  return Math.round((item.handled / item.count) * 100);
};

const handleTimeChange = (value: "day" | "month" | "year") => {
  selectedView.value = value;
  fetchTrendData(value);
};

const handleViolationTimeRangeChange = (value: "day" | "month" | "year") => {
  violationTimeRange.value = value;
  fetchViolationStats();
};

// 添加获取所有员工列表的方法
const getAllStaffList = () => {
  if (!props.pointData?.staff) return [];

  // 获取所有员工数据
  const staffList = props.pointData.staff.map(staff => {
    // 查找对应的班次信息
    const schedule = props.pointData.schedules?.find(
      s => s.userId === staff.user_id
    );

    return {
      userId: staff.user_id,
      name: staff.name,
      deptName: staff.deptName,
      phone: staff.phone,
      shifts: schedule?.shifts || [],
      status: staff.status
    };
  });

  // 按岗位排序（专职排在前面）
  staffList.sort((a, b) => {
    const isASpecial = a.deptName?.includes("专职");
    const isBSpecial = b.deptName?.includes("专职");

    if (isASpecial && !isBSpecial) return -1;
    if (!isASpecial && isBSpecial) return 1;

    // 其他按照姓名排序
    return a.name?.localeCompare(b.name) || 0;
  });

  return staffList;
};
// 获取趋势数据
const fetchTrendData = async (view: string = selectedView.value) => {
  try {
    let response;
    const today = dayjs();

    switch (view) {
      case "day":
        response = await getIllegalTrendByDay(
          today.format("YYYY-MM-DD"),
          appStore.largeScreenArea
        );
        break;
      case "month":
        response = await getIllegalTrendByMonth(
          today.year(),
          today.month() + 1
        );
        break;
      case "year":
        const currentYear = today.year();
        response = await getIllegalTrendByYear(
          currentYear,
          appStore.largeScreenArea
        );
        if (response.code === 200 && response.data.categories) {
          response.data.categories = response.data.categories.map(
            (month: string) => {
              const monthNum = parseInt(month);
              return monthNum.toString().padStart(2, "0");
            }
          );
        }
        break;
      default:
        response = await getIllegalTrendByDay(
          today.format("YYYY-MM-DD"),
          appStore.largeScreenArea
        );
    }

    if (response.code === 200) {
      trendData.value = response.data;
      nextTick(() => {
        renderChart();
      });
    }
  } catch (error) {
    console.error("Failed to fetch trend data:", error);
  }
};

const renderChart = () => {
  if (!trendChartRef.value || !trendData.value.categories.length) return;

  if (!trendChart && trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);
  }

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 10, 30, 0.85)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      borderWidth: 1,
      padding: [6, 8], // 减小内边距，让tooltip更紧凑
      // 优化tooltip位置，防止被遮挡
      confine: true, // 限制tooltip在图表容器内
      appendToBody: true, // 将tooltip添加到body，避免被对话框容器裁剪
      textStyle: {
        color: "rgba(255, 255, 255, 0.9)",
        fontSize: 12 // 减小字体大小，让tooltip更小巧
      },
      position: function (point, params, dom, rect, size) {
        // 智能位置计算，防止tooltip超出边界
        const [mouseX, mouseY] = point;
        const { contentSize, viewSize } = size;
        const [tooltipWidth, tooltipHeight] = contentSize;
        const [containerWidth, containerHeight] = viewSize;

        let x = mouseX + 10; // 默认在鼠标右侧
        let y = mouseY - tooltipHeight / 2; // 垂直居中

        // 检查右边界，如果超出则显示在左侧
        if (x + tooltipWidth > containerWidth) {
          x = mouseX - tooltipWidth - 10;
        }

        // 检查上下边界
        if (y < 0) {
          y = 10;
        } else if (y + tooltipHeight > containerHeight) {
          y = containerHeight - tooltipHeight - 10;
        }

        return [x, y];
      }
    },
    legend: {
      data: trendData.value.series.map(item => item.name),
      textStyle: {
        color: "rgba(255, 255, 255, 0.7)"
      },
      top: 0
    },
    grid: {
      top: 50,
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: trendData.value.categories,
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)"
        }
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)"
      }
    },
    yAxis: {
      type: "value",
      name: "违法数量(起)",
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.7)"
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)"
        }
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)"
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      }
    },
    series: trendData.value.series.map(item => ({
      name: item.name,
      type: "line",
      smooth: true,
      symbol: "circle",
      symbolSize: 8,
      data: item.data,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        opacity: 0.1
      }
    }))
  };

  trendChart.setOption(option);
};

// HandleStatistics组件范围变化事件处理
const handleStatisticsRangeChange = (params: {
  range: string;
  componentName: string;
  year: string;
  startTime?: string;
  endTime?: string;
}) => {
  // 根据HandleStatistics组件的时间范围获取数据
  fetchHandleDataForStatistics(params);
};

// 为HandleStatistics组件获取数据
const fetchHandleDataForStatistics = async (params: {
  range: string;
  componentName: string;
  year: string;
  startTime?: string;
  endTime?: string;
}) => {
  try {
    let response;
    if (params.range === "year") {
      // 使用年度接口
      response = await getDisposalStatistics({
        ...appStore.largeScreenArea,
        year: params.year
      });
    } else {
      // 使用月度接口
      response = await getDisposalStatisticsByMonth({
        ...appStore.largeScreenArea,
        startTime: params.startTime!,
        endTime: params.endTime!
      });
    }

    if (response.code === 200) {
      const data = response.data;

      // 适配新的数据结构（与大屏页面hooks保持一致）
      const trendData = Array.isArray(data) ? data : [];

      // 计算汇总数据
      const totalTasks = trendData.reduce(
        (sum: number, item: any) => sum + (item.totalTasks || 0),
        0
      );
      const completedTasks = trendData.reduce(
        (sum: number, item: any) => sum + (item.completedTasks || 0),
        0
      );

      // 构建trend数据
      const totalTasksTrend = trendData.map(
        (item: any) => item.totalTasks || 0
      );
      const completedTasksTrend = trendData.map(
        (item: any) => item.completedTasks || 0
      );
      const datesTrend = trendData.map((item: any) => {
        if (params.range === "year") {
          // 年模式：从 "2025-01" 提取月份 "01月"
          const month = item.month || "";
          if (month.includes("-")) {
            const monthNum = month.split("-")[1];
            return `${monthNum}月`;
          }
          return month;
        } else {
          // 月模式：从 "2025-06-01" 提取日期 "01日"
          const date = item.date || "";
          if (date.includes("-")) {
            const parts = date.split("-");
            if (parts.length >= 3) {
              return `${parts[2]}日`;
            }
          }
          return date;
        }
      });

      handleStatisticsData.value = {
        total: totalTasks,
        handled: completedTasks,
        pending: Math.max(0, totalTasks - completedTasks),
        rate:
          totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        onTimeProcessingRate: 0, // 暂时设为0，后续根据需要计算
        trend: {
          warning: totalTasksTrend,
          handled: completedTasksTrend,
          dates: datesTrend
        }
      };
    }
  } catch (error) {
    console.error("获取处置数据失败：", error);
  }
};

// 初始化数据获取方法
const fetchHandleData = async () => {
  try {
    // 默认获取当前月份的数据
    const now = dayjs();
    const startTime = now.startOf("month").format("YYYY-MM-DD");
    const endTime = now.endOf("month").format("YYYY-MM-DD");

    const response = await getDisposalStatisticsByMonth({
      ...appStore.largeScreenArea,
      startTime,
      endTime
    });

    if (response.code === 200) {
      const data = response.data;
      const trendData = Array.isArray(data) ? data : [];

      // 计算汇总数据
      const totalTasks = trendData.reduce(
        (sum: number, item: any) => sum + (item.totalTasks || 0),
        0
      );
      const completedTasks = trendData.reduce(
        (sum: number, item: any) => sum + (item.completedTasks || 0),
        0
      );

      // 构建trend数据
      const totalTasksTrend = trendData.map(
        (item: any) => item.totalTasks || 0
      );
      const completedTasksTrend = trendData.map(
        (item: any) => item.completedTasks || 0
      );
      const datesTrend = trendData.map((item: any) => {
        const date = item.date || "";
        if (date.includes("-")) {
          const parts = date.split("-");
          if (parts.length >= 3) {
            return `${parts[2]}日`;
          }
        }
        return date;
      });

      handleStatisticsData.value = {
        total: totalTasks,
        handled: completedTasks,
        pending: Math.max(0, totalTasks - completedTasks),
        rate:
          totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        onTimeProcessingRate: 0,
        trend: {
          warning: totalTasksTrend,
          handled: completedTasksTrend,
          dates: datesTrend
        }
      };
    }
  } catch (error) {
    console.error("获取处置数据失败：", error);
  }
};

// 注意：图表渲染现在由HandleStatistics组件内部处理，不再需要这个方法

// 根据设备类型返回对应图标
const getDeviceIcon = (type: string) => {
  switch (type) {
    case "摄像机":
      return VideoCamera;
    case "电脑":
      return Cpu;
    default:
      return Monitor;
  }
};

// 获取违法统计数据
const fetchViolationStats = async () => {
  try {
    // 根据选择的时间范围计算开始和结束时间
    const now = dayjs();
    let startTime, endTime;

    switch (violationTimeRange.value) {
      case "day":
        startTime = now.startOf("day");
        endTime = now.endOf("day");
        break;
      case "month":
        startTime = now.startOf("month");
        endTime = now.endOf("month");
        break;
      case "year":
        startTime = now.startOf("year");
        endTime = now.endOf("year");
        break;
    }

    const params = {
      city: props.pointData?.city,
      county: props.pointData?.county,
      township: props.pointData?.township,
      hamlet: props.pointData?.hamlet,
      site: props.pointData?.site,
      startTime: startTime.format("YYYY-MM-DD HH:mm:ss"),
      endTime: endTime.format("YYYY-MM-DD HH:mm:ss")
    };

    const res = await getPointViolationStats(params);
    if (res.code === 200) {
      // 适配后端返回的数据格式
      violationData.value = {
        total: res.data.totalViolations,
        handled: res.data.totalDispatched,
        unhandled: res.data.totalViolations - res.data.totalDispatched,
        details: res.data.typeDetails.map(item => ({
          type: item.type,
          count: item.violations,
          handled: item.dispatched
        }))
      };
    }
  } catch (error) {
    console.error("获取违法统计数据失败:", error);
  }
};

// 获取设备状态文本
const getDeviceStatusText = (status: string | number) => {
  if (typeof status === "number") {
    return status === 0 ? "在线" : "离线";
  }
  return status;
};

// 添加自动更新时段的函数
const updateCurrentPeriod = () => {
  const now = new Date();
  const targetPeriod = now.getHours() < 12 ? "am" : "pm";

  // 只在时段变化时更新
  if (currentTab.value !== targetPeriod) {
    currentTab.value = targetPeriod;
  }
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      // 更新当前时段
      updateCurrentPeriod();
      fetchViolationStats();
      fetchTrendData();
      fetchHandleData();

      // 设置定时器，每分钟检查一次时段
      const intervalId = setInterval(() => {
        updateCurrentPeriod();
      }, 60000);

      // 组件卸载时清除定时器
      onUnmounted(() => {
        clearInterval(intervalId);
      });
    }
  }
);

onMounted(() => {
  // 获取违法类型统计数据
  fetchViolationStats();
  // 获取违法趋势数据
  fetchTrendData();
  // 获取精准劝导数据
  fetchHandleData();
});

// 获取某时段的所有班次时间（去重）
const getUniqueShiftTimes = (
  period: string
): Array<{ name: string; time: string }> => {
  if (!props.pointData?.schedules) return [];

  // 收集所有班次
  const shiftMap = new Map<string, { name: string; time: string }>();

  props.pointData.schedules.forEach(schedule => {
    if (schedule.shifts && Array.isArray(schedule.shifts)) {
      schedule.shifts.forEach(shift => {
        if (shift.startTime && shift.endTime) {
          // 判断是上午还是下午班次
          const startHour = parseInt(shift.startTime.split(":")[0]);
          const isAM = startHour < 12;

          // 根据period过滤班次
          if ((period === "AM" && isAM) || (period === "PM" && !isAM)) {
            const timeKey = `${shift.startTime}-${shift.endTime}`;
            if (!shiftMap.has(timeKey)) {
              shiftMap.set(timeKey, {
                name: shift.shiftName,
                time: `${shift.startTime}-${shift.endTime}`
              });
            }
          }
        }
      });
    }
  });

  // 转换为数组并排序
  return Array.from(shiftMap.values()).sort((a, b) => {
    const [aStart] = a.time.split("-");
    const [bStart] = b.time.split("-");
    return aStart.localeCompare(bStart);
  });
};

// 状态到 tag 类型的映射
function getStatusTagType(
  status: string
): "success" | "info" | "danger" | "warning" | "primary" {
  switch (status) {
    case "在岗":
      return "success";
    case "休息":
      return "info";
    case "请假":
      return "info";
    case "脱岗":
      return "warning";
    case "故障":
      return "danger";
    default:
      return "info";
  }
}

// 获取上岗状态显示内容
const getDisplayStatus = (row: any) => {
  // 专职人员
  if (row.deptName?.includes("专职")) {
    if (row.status === "请假") {
      // 查找同岗位在岗兼职
      const staff = props.pointData?.staff || [];
      const partTimeOnDuty = staff.filter(
        s =>
          isPartTimeStaff(s) &&
          s.deptName === row.deptName &&
          s.status === "在岗"
      );
      if (partTimeOnDuty.length > 0) {
        return partTimeOnDuty.length === 1
          ? `请假/在岗(兼职)`
          : `请假/在岗(兼职${partTimeOnDuty.length}人)`;
      } else {
        return "请假";
      }
    } else {
      return row.status;
    }
  }
  // 兼职人员
  if (isPartTimeStaff(row)) {
    if (row.status === "在岗") {
      return "在岗";
    } else {
      return "-";
    }
  }
  // 其他情况
  return "-";
};

// 判断人员是否为兼职
const isPartTimeStaff = (staff: any): boolean => {
  // 直接使用isPartTime属性判断
  if (staff.isPartTime !== undefined) {
    return staff.isPartTime;
  }

  // 使用deptName属性判断
  return staff.deptName?.includes("兼职") || false;
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  padding: 20px;
}

// 弹窗内容
.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%); // 居中定位，不进行缩放
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 20px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .title {
    display: flex;
    gap: 8px;
    align-items: center;

    .title-text {
      @extend .fs-dialog-title; // 弹窗标题文字字体大小
      font-weight: 500;
      color: #fff;
      letter-spacing: 1px;
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.dialog-body {
  height: calc(100% - 80px);
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(255 255 255 / 20%);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgb(0 0 0 / 10%);
    border-radius: 3px;
  }
}

.point-info {
  padding: 20px;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 4px;

  .flex-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .device-info {
      flex: 1;
      padding: 16px;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 4px;

      :deep(.el-table) {
        background: transparent !important;

        &::before {
          display: none;
        }

        .el-table__header-wrapper {
          th {
            @extend .fs-map-dialog-table-header; // 设备信息表格表头字体大小（设备名称、状态、维保电话等列标题）
            font-weight: 500 !important;
            color: rgb(255 255 255 / 80%) !important;
            background: rgb(0 24 75 / 60%) !important;
            border-bottom: 1px solid rgb(64 158 255 / 20%) !important;
          }
        }

        .el-table__body-wrapper {
          tr {
            background: transparent !important;

            &:hover > td {
              background: rgb(64 158 255 / 10%) !important;
            }

            td {
              padding: 8px 0 !important;
              @extend .fs-map-dialog-table-content; // 设备信息表格内容字体大小
              color: rgb(255 255 255 / 90%) !important;
              background: transparent !important;
              border-bottom: 1px solid rgb(64 158 255 / 10%) !important;
            }
          }
        }

        .el-table__inner-wrapper {
          &::before {
            display: none !important;
          }
        }

        .el-table__empty-block {
          background: transparent !important;

          .el-table__empty-text {
            color: rgb(255 255 255 / 50%) !important;
          }
        }

        .el-button--primary.el-button--link {
          height: 28px !important;
          padding: 4px 12px !important;
          opacity: 0.8;
          transition: all 0.3s;

          &:hover {
            background: rgb(64 158 255 / 10%) !important;
            opacity: 1;
            transform: scale(1.05);
          }
        }

        .el-tag {
          height: 24px !important;
          padding: 2px 8px !important;
          @extend .fs-map-dialog-table-content; // 设备信息表格状态标签字体大小
          line-height: 20px !important;
          border: none !important;
          border-radius: 2px !important;

          &.el-tag--success {
            color: #67c23a !important;
            background: rgb(103 194 58 / 15%) !important;
            border-left: 2px solid #67c23a !important;
          }

          &.el-tag--danger {
            color: #f56c6c !important;
            background: rgb(245 108 108 / 15%) !important;
            border-left: 2px solid #f56c6c !important;
          }
        }
      }
    }

    .staff-info {
      flex: 1;
      padding: 16px;
      overflow: hidden;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 4px;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 14px;
        @extend .fs-map-dialog-sub-title; // 上岗时间区域标题字体大小
        font-weight: 500;
        color: rgb(255 255 255 / 90%);
      }

      .staff-list {
        height: 100%;

        :deep(.el-table) {
          color: rgb(255 255 255 / 80%) !important;
          background: transparent !important;

          .el-table__body {
            background: transparent !important;
          }

          .el-table__empty-text {
            color: rgb(255 255 255 / 50%) !important;
          }

          .el-table__header-wrapper,
          .el-table__body-wrapper {
            background: transparent !important;

            tr {
              background: transparent !important;
            }

            th {
              color: rgb(255 255 255 / 80%) !important;
              background: rgb(0 24 75 / 60%) !important;
              border-bottom: 1px solid rgb(64 158 255 / 20%) !important;
            }

            td {
              color: rgb(255 255 255 / 80%) !important;
              background: rgb(0 24 75 / 30%) !important;
              border-bottom: 1px solid rgb(64 158 255 / 10%) !important;
            }
          }

          &::before {
            display: none;
          }
        }
      }
    }

    .attendance-info {
      flex: 1;
      padding: 12px;
      overflow: hidden;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 4px;

      :deep(.el-table) {
        background-color: transparent !important;

        &::before {
          display: none !important;
        }

        .el-table__header-wrapper {
          th {
            @extend .fs-map-dialog-table-header; // 当前状态表格表头字体大小（姓名、岗位、手机号、上岗状态等列标题）
            font-weight: 500 !important;
            color: rgb(255 255 255 / 80%) !important;
            background: rgb(0 24 75 / 60%) !important;
            border-bottom: 1px solid rgb(64 158 255 / 20%) !important;
          }
        }

        .el-table__body-wrapper {
          tr {
            background-color: transparent !important;

            &:hover > td {
              background-color: rgb(64 158 255 / 10%) !important;
            }
          }

          td {
            @extend .fs-map-dialog-table-content; // 当前状态表格内容字体大小
            color: rgb(255 255 255 / 80%) !important;
            background-color: rgb(0 24 75 / 30%) !important;
            border-bottom: 1px solid rgb(64 158 255 / 10%) !important;
          }
        }

        .el-table__empty-text {
          color: rgb(255 255 255 / 50%) !important;
          background-color: transparent !important;
        }

        .el-tag {
          height: auto !important;
          padding: 0 !important;
          @extend .fs-map-dialog-table-content; // 当前状态表格中状态标签字体大小（在岗、请假、脱岗等状态文字）
          line-height: 1 !important;
          background: transparent !important;
          border: none !important;

          &.el-tag--success {
            color: #67c23a !important;
          }

          &.el-tag--warning {
            color: #e6a23c !important;
          }

          &.el-tag--danger {
            color: #f56c6c !important;
          }

          &.el-tag--info {
            color: #909399 !important;
          }
        }
      }
    }
  }

  .section {
    margin-bottom: 20px;

    .section-title {
      position: relative;
      padding-left: 12px;
      margin-bottom: 16px;
      @extend .fs-map-dialog-sub-title; // 各个区域标题字体大小（设备信息、违法类型统计、当前状态等）
      font-weight: 500;
      color: #fff;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: #409eff;
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }

  .violation-info {
    position: relative;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .section-title {
        margin: 0;
      }
    }
  }

  .chart-info {
    display: flex;
    flex-direction: row;

    .trend-chart-info {
      flex: 1;
      height: 100%;
      padding: 20px;
      margin-right: 10px;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 4px;

      .chart-header {
        display: flex;
        justify-content: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .title {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .title-text {
            position: relative;
            display: inline-block;
            padding-left: 12px;
            @extend .fs-map-dialog-sub-title; // 违法趋势图表标题字体大小（"违法趋势"文字）
            font-weight: 500;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s;

            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: #409eff;
              border-radius: 2px;
              transition: all 0.3s;
              transform: translateY(-50%);
            }
          }
        }
      }

      .trend-chart {
        width: 100%;
        height: 400px;
      }
    }

    // HandleStatistics组件容器样式
    :deep(.handle-statistics) {
      flex: 1;
      height: 494px;
      margin-left: 5px;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 4px;
    }
  }
}

.violation-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 16px;

  .violation-card {
    padding: 17px 12px;
    background: rgb(2 12 39 / 90%);
    border-radius: 4px;

    .card-header {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-bottom: 12px;

      .icon {
        color: #e6a23c;
      }

      .type-name {
        @extend .fs-map-dialog-illegal-name; // 违法类型名称字体大小（如"超载"、"闯红灯"等违法类型名称）
        color: rgb(255 255 255 / 90%);
      }
    }

    .card-body {
      .stat-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          @extend .fs-map-dialog-illegal-type-value; // 违法类型统计标签字体大小（"数量"、"占比"等标签文字）
          color: rgb(255 255 255 / 60%);
        }

        .value {
          @extend .fs-map-dialog-illegal-type-value; // 违法类型统计数值字体大小（违法数量和占比的具体数值）
          color: rgb(255 255 255 / 90%);
        }

        &.highlight .value {
          color: #409eff;
        }
      }
    }
  }

  &.odd-cards {
    .violation-card:last-child {
      grid-column: span 1;
    }
  }
}

.device-name {
  display: flex;
  gap: 8px;
  align-items: center;
  transition: opacity 0.2s;

  &:hover {
    opacity: 0.8;
  }
}

.time-item {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 0;
  @extend .fs-map-dialog-shift-info-card; // 班次信息卡片字体大小（班次时间、人员信息等）
  color: rgb(255 255 255 / 90%);
  background: rgb(64 158 255 / 10%);
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background: rgb(64 158 255 / 20%);
    transform: translateX(4px);
  }

  .shift-name {
    min-width: 40px;
    font-weight: 500;
    color: #409eff;
  }

  .shift-time {
    color: rgb(255 255 255 / 90%);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.shift-time-container {
  display: flex;
  gap: 20px;
  height: 262px;
  padding: 15px;
  background: rgb(0 24 75 / 20%);
  border-radius: 4px;

  .shift-time-section {
    display: flex;
    flex: 1;
    flex-direction: column;

    .shift-time-header {
      padding-left: 10px;
      margin-bottom: 15px;
      @extend .fs-map-dialog-shift-name; // 班次字体大小（具体的人员姓名）
      font-weight: 500;
      line-height: 1.2;
      color: #fff;
      border-left: 3px solid #409eff;
    }

    .shift-time-list {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      background: rgb(0 24 75 / 30%);
      border-radius: 4px;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgb(64 158 255 / 30%);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }
  }
}

.no-shifts {
  padding: 20px 0;
  @extend .fs-map-dialog-no-data-text; // 无班次数据状态文字字体大小（"暂无班次数据"）
  font-style: italic;
  color: rgb(255 255 255 / 50%);
  text-align: center;
}

// 空状态样式
.empty-status {
  display: inline-block;
  padding: 4px 8px;
  @extend .fs-map-dialog-no-data-text; // 无人员数据状态文字字体大小（"暂无人员"）
  font-style: italic;
  color: rgb(255 255 255 / 30%);
}
</style>
