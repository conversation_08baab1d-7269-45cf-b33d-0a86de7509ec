<template>
  <!-- 履职情况弹窗 -->
  <div class="dialog-container" @click.self="handleClose">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <!-- 标题栏 -->
        <div class="dialog-header">
          <div class="title">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <span class="title-text">履职情况分析</span>
          </div>
          <div class="large-screen-close-btn" @click="handleClose">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <!-- 内容区 -->
        <div class="dialog-body">
          <div class="persuasion-statistics">
            <div class="stat-header">
              <!-- 左侧节点树 -->
              <div class="dialog-area-selector">
                <AreaCascader
                  ref="areaCascaderRef"
                  v-model="selectedArea"
                  :show-home-button="true"
                  @change="handleAreaChange"
                />
              </div>

              <!-- 中间 Tab 切换 -->
              <div class="header-center">
                <div class="tab-group">
                  <div
                    v-for="tab in tabs"
                    :key="tab.value"
                    class="tab-item"
                    :class="{ active: currentTab === tab.value }"
                    @click="handleTabChange(tab.value)"
                  >
                    {{ tab.label }}
                  </div>
                </div>
              </div>

              <!-- 排序相关控件 -->
              <div class="header-sort">
                <div class="custom-select">
                  <div
                    class="select-trigger"
                    @click="sortVisible = !sortVisible"
                  >
                    <span>{{
                      sortOptions.find(opt => opt.value === sortType)?.label
                    }}</span>
                    <el-icon
                      class="select-icon"
                      :class="{ 'is-reverse': sortVisible }"
                    >
                      <ArrowDown />
                    </el-icon>
                  </div>
                  <div v-show="sortVisible" class="select-dropdown">
                    <div
                      v-for="option in sortOptions"
                      :key="option.value"
                      class="select-option"
                      :class="{ 'is-active': sortType === option.value }"
                      @click="handleSortChange(option.value)"
                    >
                      {{ option.label }}
                    </div>
                  </div>
                </div>

                <div class="custom-select">
                  <div
                    class="select-trigger"
                    @click="countVisible = !countVisible"
                  >
                    <span>{{
                      countOptions.find(opt => opt.value === displayCount)
                        ?.label
                    }}</span>
                    <el-icon
                      class="select-icon"
                      :class="{ 'is-reverse': countVisible }"
                    >
                      <ArrowDown />
                    </el-icon>
                  </div>
                  <div v-show="countVisible" class="select-dropdown">
                    <div
                      v-for="option in countOptions"
                      :key="option.value"
                      class="select-option"
                      :class="{
                        'is-active': displayCount === option.value
                      }"
                      @click="handleCountChange(option.value)"
                    >
                      {{ option.label }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧时间选择器 -->
              <div class="header-right">
                <template v-if="currentTab === 'area'">
                  <TimeRangeSelector
                    ref="timeRangeSelectorRef"
                    v-model="selectedView"
                    :enabled-ranges="['day', 'month', 'year']"
                    :custom-labels="{
                      day: '日',
                      month: '月',
                      year: '年'
                    }"
                    :disable-future="true"
                    :initial-date="selectedDate"
                    :initial-month="selectedMonth"
                    :initial-year="selectedYear"
                    @change="handleTimeChange"
                    @date-change="handleDateChange"
                    @month-change="handleMonthChange"
                    @year-change="handleYearChange"
                  />
                </template>

                <template v-if="currentTab === 'point'">
                  <TimeRangeSelector
                    v-model="selectedView"
                    :enabled-ranges="['month', 'year']"
                    :custom-labels="{ month: '月', year: '年' }"
                    :disable-future="true"
                    :initial-month="selectedMonth"
                    :initial-year="selectedYear"
                    @change="handleTimeChange"
                    @month-change="handleMonthChange"
                    @year-change="handleYearChange"
                  />
                </template>
              </div>
            </div>
            <div class="stat-chart">
              <div ref="chartRef" class="stat-chart" />
            </div>
          </div>
        </div>

        <!-- 底部装饰 -->
        <dv-decoration-3
          class="dialog-footer"
          style="width: 100%; height: 4px"
        />
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts" name="PersuasionStatistics">
import {
  getPersuasionStatsData,
  getLocationTrendsByYear
} from "@/api/largeScreen";
import { getPersuasionTrendsByDay } from "@/api/screen";
import { useAppStoreHook } from "@/store/modules/app";
import { useUserStoreHook } from "@/store/modules/user";
import { AddressLevel } from "@/types/business";
import { TreeNode } from "@/views/system/system-user/utils/types";
import { getNodePathById } from "@/utils/tree";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector, {
  type TimeRangeType
} from "@/components/TimeRangeSelector.vue";
import { ArrowDown, Close, House } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import * as echarts from "echarts";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";

// 注册插件
dayjs.extend(isSameOrBefore);

const props = defineProps<{
  // 大屏传递的初始时间值
  initialMode?: "day" | "month" | "year";
  initialDate?: string;
  initialMonth?: string;
  initialYear?: string;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

const sortType = ref("violations"); // 默认按违法数量排序
const displayCount = ref(0);
const persuasionStatisticsData = ref(null);
const userStore = useUserStoreHook();
const appStore = useAppStoreHook();

const currentLevel = ref<keyof AddressLevel>("city");
const currentAddress = ref<AddressLevel>({
  [userStore.getHighestRoleCode]:
    userStore.userInfo[userStore.getHighestRoleCode]
});

// 级联选择器引用
const cascaderAreaRef = ref();

// 排序选项
const sortOptions = [
  { label: "违法数量排序", value: "violations" },
  { label: "已劝导排序", value: "persuaded" },
  { label: "有效劝导排序", value: "effective" },
  { label: "劝导率排序", value: "rate" }
];

// 显示数量选项
const countOptions = [
  { label: "前10条", value: 10 },
  { label: "前20条", value: 20 },
  { label: "前50条", value: 50 },
  { label: "全部", value: 0 }
];

const sortVisible = ref(false);
const countVisible = ref(false);
const selectedType = ref("");
const selectedArea = ref<any>([]);

const handleSortChange = (value: string) => {
  sortType.value = value;
  sortVisible.value = false;
};

const handleCountChange = (value: number) => {
  displayCount.value = value;
  countVisible.value = false;
};

// 点击外部关闭下拉框
const closeDropdowns = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest(".custom-select")) {
    sortVisible.value = false;
    countVisible.value = false;
  }
};

// 定义请求参数接口
interface RequestParams {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * 处理区域视图下图表数据
 */
const handleFormatAreaTabData = (chartData: any[]) => {
  // 根据不同的 tab 类型处理数据
  if (currentTab.value === "point") {
    // 生成选择日期范围内的所有日期
    const dates: string[] = [];
    const startDate = dayjs(selectedDateRange.value[0]);
    const endDate = dayjs(selectedDateRange.value[1]);
    let currentDate = startDate;

    while (currentDate.valueOf() <= endDate.valueOf()) {
      dates.push(currentDate.format("YYYY-MM-DD"));
      currentDate = currentDate.add(1, "day");
    }

    // 根据时间范围类型处理数据
    if (currentRange.value === "year") {
      // 年份数据：直接使用后端返回的数据
      const formattedData = chartData.map(item => ({
        name: dayjs(item.date).format("M月"), // 显示月份格式（1月,2月,3月...12月）
        originalDate: item.date, // 保留原始日期 "2025-03"
        totalViolations: item.totalViolations || 0,
        totalPersuaded: item.totalPersuaded || 0,
        totalEffective: item.effectiveCount || 0,
        overallPersuasionRate: item.persuasionRate || "0.00"
      }));

      // 按月份排序（提取数字部分进行排序）
      return formattedData.sort((a, b) => {
        const monthA = parseInt(a.name.replace("月", ""));
        const monthB = parseInt(b.name.replace("月", ""));
        return monthA - monthB;
      });
    } else {
      // 月份数据：需要生成完整日期序列并填充数据
      const dataMap = new Map(
        chartData.map(item => [dayjs(item.date).format("YYYY-MM-DD"), item])
      );

      // 为每个日期生成数据
      const formattedData = dates.map(date => {
        let displayName = date;
        if (currentRange.value === "month") {
          // 月份视图显示日期格式（1日,2日,3日...）
          displayName = dayjs(date).format("D日");
        }

        return {
          name: displayName,
          originalDate: date, // 保留原始日期用于数据查找
          totalViolations: dataMap.get(date)?.totalViolations || 0,
          totalPersuaded: dataMap.get(date)?.totalPersuaded || 0,
          totalEffective: dataMap.get(date)?.effectiveCount || 0,
          overallPersuasionRate: dataMap.get(date)?.persuasionRate || "0.00"
        };
      });

      // 按日期升序排序
      return formattedData.sort((a, b) => {
        return (
          dayjs(a.originalDate).valueOf() - dayjs(b.originalDate).valueOf()
        );
      });
    }
  }

  // 格式化数据
  const formattedData = chartData.map(item => {
    return {
      name: item.display,
      totalViolations: item.count,
      totalPersuaded: item.persuaded_count,
      totalEffective: item.effective_count,
      overallPersuasionRate: item.persuasion_rate
    };
  });

  // 根据排序类型进行降序排序
  if (currentTab.value === "area") {
    const sortedData = [...formattedData].sort((a, b) => {
      switch (sortType.value) {
        case "violations":
          return b.totalViolations - a.totalViolations;
        case "persuaded":
          return b.totalPersuaded - a.totalPersuaded;
        case "effective":
          return b.totalEffective - a.totalEffective;
        case "rate":
          return (
            parseFloat(b.overallPersuasionRate) -
            parseFloat(a.overallPersuasionRate)
          );
        default:
          return b.totalViolations - a.totalViolations;
      }
    });

    // 根据显示数量限制结果
    if (displayCount.value > 0) {
      return sortedData.slice(0, displayCount.value);
    }
    return sortedData;
  }

  return formattedData;
};

const renderChart = () => {
  if (!chartRef.value || !chart || !persuasionStatisticsData.value) return;

  // 每次渲染前销毁旧的图表实例
  if (chart) {
    chart.dispose();
  }
  chart = echarts.init(chartRef.value);

  // 根据不同类型视图显示处理不同数据
  const finalData = handleFormatAreaTabData(persuasionStatisticsData.value);
  const xAxisNames = finalData.map(item => item.name);

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(64, 158, 255, 0.1)",
          shadowColor: "rgba(64, 158, 255, 0.2)",
          shadowBlur: 10
        }
      },
      backgroundColor: "rgba(0, 24, 75, 0.8)",
      borderColor: "rgba(64, 158, 255, 0.3)",
      borderWidth: 1,
      padding: [8, 12],
      textStyle: {
        color: "rgba(255, 255, 255, 0.8)",
        fontSize: 24 // 鼠标悬浮 文字大小
      },
      formatter: (params: any) => {
        // 时段对比显示原始日期，区域对比显示name
        let displayTitle = params[0].name;
        if (currentTab.value === "point") {
          const dataPoint = finalData.find(
            item => item.name === params[0].name
          ) as any;
          displayTitle = dataPoint?.originalDate || params[0].name;
        }

        let result = `<div style="color: #fff; font-weight: 500; margin-bottom: 4px;">
          ${displayTitle}
        </div>`;
        params.forEach((param: any) => {
          const value = param.value === undefined ? "-" : param.value;
          const marker = `<span style="display: inline-block;
            width: 8px;
            height: 8px;
            background-color: ${param.color};
            border-radius: 50%;
            margin-right: 6px;"></span>`;

          if (param.seriesName === "违法总数") {
            result += `<div style="display: flex; align-items: center; margin: 4px 0;">
              ${marker}${param.seriesName}：<span style="color: #409EFF; margin-left: 4px;">${value}</span>
            </div>`;
          } else if (param.seriesName === "劝导率") {
            result += `<div style="display: flex; align-items: center; margin: 4px 0;">
              ${marker}${param.seriesName}：<span style="color: #67C23A; margin-left: 4px;">${value}%</span>
            </div>`;
          } else {
            result += `<div style="display: flex; align-items: center; margin: 4px 0;">
              ${marker}${param.seriesName}：<span style="color: rgba(255, 255, 255, 0.8); margin-left: 4px;">${value}</span>
            </div>`;
          }
        });
        return result;
      }
    },
    legend: {
      data: ["违法数量", "已劝导", "有效劝导", "劝导率"],
      top: 0,
      textStyle: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // 图例文字大小
      },
      itemWidth: 18,
      itemHeight: 12,
      itemGap: 25
    },
    dataZoom: [
      {
        show: true,
        height: 15,
        xAxisIndex: [0],
        bottom: 10,
        start: 0,
        end: finalData.length <= 7 ? 100 : (7 / finalData.length) * 100,
        handleIcon:
          "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
        handleSize: "110%",
        handleStyle: {
          color: "#409EFF"
        },
        textStyle: {
          color: "rgba(255, 255, 255, 0.7)"
        },
        borderColor: "rgba(255, 255, 255, 0.2)",
        backgroundColor: "rgba(0, 24, 75, 0.3)",
        fillerColor: "rgba(64, 158, 255, 0.1)",
        moveHandleStyle: {
          color: "#409EFF"
        },
        selectedDataBackground: {
          lineStyle: {
            color: "#409EFF"
          },
          areaStyle: {
            color: "#409EFF"
          }
        }
      },
      {
        type: "inside",
        xAxisIndex: [0],
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
        preventDefaultMouseMove: true
      }
    ],
    grid: {
      top: "12%",
      bottom: "15%",
      left: "3%",
      right: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisNames,
      axisLabel: {
        interval: 0,
        rotate: 0,
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22, // X轴标签文字大小
        width: 90,
        overflow: "truncate",
        formatter: function (value) {
          return value;
        }
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.2)"
        }
      }
    },
    yAxis: [
      {
        type: "value",
        name: "数量",
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 22 // Y轴名称文字大小
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 22 // Y轴标签文字大小
        },
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.2)"
          }
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)"
          }
        }
      }
    ],
    series: [
      {
        name: "违法数量",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#F56C6C" },
            { offset: 1, color: "rgba(245, 108, 108, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        data: finalData.map(item => item.totalViolations),
        label: {
          show: true,
          position: "top",
          distance: 4,
          color: "#F56C6C",
          fontSize: 22, // 数据标签文字大小
          fontWeight: 500
        }
      },
      {
        name: "已劝导",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#409EFF" },
            { offset: 1, color: "rgba(64, 158, 255, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        data: finalData.map(item => item.totalPersuaded),
        label: {
          show: true,
          position: "top",
          distance: 4,
          color: "#409EFF",
          fontSize: 22, // 数据标签文字大小
          fontWeight: 500
        }
      },
      {
        name: "有效劝导",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#67C23A" },
            { offset: 1, color: "rgba(103, 194, 58, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        data: finalData.map(item => item.totalEffective),
        label: {
          show: true,
          position: "top",
          distance: 4,
          color: "#67C23A",
          fontSize: 22, // 数据标签文字大小
          fontWeight: 500
        }
      },
      {
        name: "劝导率",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#E6A23C" },
            { offset: 1, color: "rgba(230, 162, 60, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: "top",
          formatter: "{c}%",
          color: "#E6A23C",
          fontSize: 22, // 数据标签文字大小
          distance: 4,
          fontWeight: 500
        },
        data: finalData.map(item => parseFloat(item.overallPersuasionRate))
      }
    ]
  };

  chart.setOption(option);

  // 添加窗口大小变化监听
  window.addEventListener("resize", handleResize);
};

const visible = ref(true);

const handleClose = () => {
  visible.value = false;
  emit("update:visible", false);
};

// 获取层级文本
const getLevelText = (level: keyof AddressLevel) => {
  const textMap = {
    city: "",
    county: "区县",
    township: "乡镇",
    hamlet: "村/社区",
    site: "点位"
  };
  return textMap[level];
};

/**
 * 请求统计列表
 * @param params 点位信息
 */
const fetchChartDataHandle = (params: Object) => {
  if (currentTab.value === "area") {
    getPersuasionStatsData({
      ...params,
      timeType: currentRange.value,
      startDate: selectedDateRange.value[0],
      endDate: selectedDateRange.value[1]
    }).then(res => {
      if (res.code === 200) {
        persuasionStatisticsData.value = res.data;
        nextTick(() => {
          renderChart();
        });
      }
    });
  }
  // 点位违法趋势
  else if (currentTab.value === "point-trend") {
    currentAddress.value = params;
  } else {
    // 时段对比
    if (currentRange.value === "year") {
      // 年份统计使用专门的年份接口
      getLocationTrendsByYear({
        ...params,
        year: parseInt(selectedYear.value)
      })
        .then(res => {
          if (res.code === 200) {
            persuasionStatisticsData.value = res.data;
            // 数据更新后重新渲染图表
            nextTick(() => {
              renderChart();
            });
          }
        })
        .catch(err => {
          console.error("年份接口调用失败:", err);
        });
    } else {
      // 月份和其他时间范围使用日期范围接口
      getPersuasionTrendsByDay({
        ...params,
        timeType: currentRange.value,
        startDate: selectedDateRange.value[0],
        endDate: selectedDateRange.value[1]
      })
        .then(res => {
          if (res.code === 200) {
            // 检查数据结构，如果没有 trends 字段，直接使用 data
            if (res.data && res.data.trends) {
              persuasionStatisticsData.value = res.data.trends;
            } else if (res.data) {
              persuasionStatisticsData.value = res.data;
            } else {
              persuasionStatisticsData.value = [];
            }

            // 数据更新后重新渲染图表
            nextTick(() => {
              renderChart();
            });
          }
        })
        .catch(err => {
          console.error("月份接口调用失败:", err);
        });
    }
  }
};

onMounted(() => {
  visible.value = true;
  // 设置默认时间范围
  currentRange.value = currentTab.value === "area" ? "day" : "month";
  handleRangeChange(currentRange.value);

  selectedArea.value = appStore.getLargeScreenPathId;
  fetchChartDataHandle(appStore.getLargeScreenArea);
  document.addEventListener("click", closeDropdowns);

  nextTick(() => {
    if (chartRef.value && !chart) {
      chart = echarts.init(chartRef.value);
      renderChart();
    }
  });
});

// 监听数据变化
watch(
  persuasionStatisticsData,
  newData => {
    if (!newData) return;
    nextTick(() => {});
  },
  { deep: true, immediate: true }
);

// 监听排序和显示数量变化
watch([sortType, displayCount], (newVal, oldVal) => {
  if (newVal[0] === oldVal[0] && newVal[1] === oldVal[1]) return;
  renderChart();
});

// 监听窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

window.addEventListener("resize", handleResize);

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
  document.removeEventListener("click", closeDropdowns);
});

// 监听可见性变化
watch(visible, newVal => {
  if (newVal) {
    // 弹窗打开时，重置时间选择器状态
    if (timeRangeSelectorRef.value) {
      timeRangeSelectorRef.value.resetTimeSelector();
    }
    // 重置本地状态
    selectedView.value = "month";
    selectedDate.value = dayjs().format("YYYY-MM-DD");
    selectedMonth.value = dayjs().format("YYYY-MM");
    selectedYear.value = dayjs().format("YYYY");

    nextTick(() => {
      if (chartRef.value && !chart) {
        chart = echarts.init(chartRef.value);
        fetchChartDataHandle(userStore.getUserLocationPath);
      }
    });
  }
});

// 监听类型变化
watch(selectedType, () => {
  renderChart();
});

// Tab 配置
const tabs = [
  { label: "区域对比", value: "area" },
  { label: "时段对比", value: "point" }
  // { label: "分时统计", value: "point-trend" }
];

const currentTab = ref("area");
const currentRange = ref("day"); // 默认为"日"，适配区域对比

// 区域对比时间范围选项
const areaTimeRanges = [
  { label: "日", value: "day" },
  { label: "月", value: "month" },
  { label: "年", value: "year" }
];

// 时段对比时间范围选项
const timeRanges = [
  { label: "月", value: "month" },
  { label: "年", value: "year" }
];

const handleTabChange = (tab: string) => {
  currentTab.value = tab;

  // 清除旧的图表实例
  if (chart) {
    chart.dispose();
    chart = null;
  }

  // 重置地址
  currentAddress.value = appStore.getLargeScreenArea;
  selectedArea.value = appStore.getLargeScreenPathId;

  // 重置数据
  persuasionStatisticsData.value = null;

  // 根据不同 tab 设置默认时间范围
  if (tab === "area") {
    currentRange.value = "day"; // 区域对比默认为"日"
  } else {
    currentRange.value = "month"; // 时段对比默认为"本月"
  }

  // 根据选择的时间范围设置日期，handleRangeChange 内部会调用 fetchChartDataHandle
  // 所以这里不需要再手动调用 fetchChartDataHandle
  handleRangeChange(currentRange.value);

  // 在下一个 tick 重新初始化图表
  nextTick(() => {
    if (chartRef.value && !chart) {
      chart = echarts.init(chartRef.value);
      renderChart();
    }
  });
};

const handleRangeChange = (range: string) => {
  currentRange.value = range;
  const today = dayjs();

  // 根据选择的时间范围自动设置相应的时间选择器值和日期范围
  switch (range) {
    case "day":
      selectedDate.value = today.format("YYYY-MM-DD");
      selectedDateRange.value = [
        today.format("YYYY-MM-DD"),
        today.format("YYYY-MM-DD")
      ];
      break;
    case "month":
      selectedMonth.value = today.format("YYYY-MM");
      selectedDateRange.value = [
        today.startOf("month").format("YYYY-MM-DD"),
        today.format("YYYY-MM-DD") // 使用今天而不是月末，避免未来日期
      ];
      break;
    case "year":
      selectedYear.value = today.format("YYYY");
      selectedDateRange.value = [
        today.startOf("year").format("YYYY-MM-DD"),
        today.format("YYYY-MM-DD") // 使用今天而不是年末，避免未来日期
      ];
      break;
  }
  fetchChartDataHandle(currentAddress.value);
};

// 根据ID查找节点
const findNodeById = (
  nodes: TreeNode[] | null,
  id: number
): TreeNode | null => {
  if (!nodes) return null;

  for (const node of nodes) {
    if (node.id === id) return node;
    const found = findNodeById(node.childList, id);
    if (found) return found;
  }

  return null;
};

// 处理地区选择变化
const handleAreaChange = (value: number[]) => {
  selectedArea.value = value;
  // 根据选择的地区重新获取数据
  const params = getAddressParams(value);
  // 更新 currentAddress 以保持状态同步
  currentAddress.value = params || {};
  fetchChartDataHandle(currentAddress.value);
};

// 地区节点点击事件
const areaNodeClickHandle = (item: any) => {
  const areaPath = getNodePathById(item.id, userStore.getUserRegionTree);
  const ids = areaPath.map(node => node.id);
  selectedArea.value = ids;
  handleAreaChange(ids);
  if (cascaderAreaRef.value) {
    cascaderAreaRef.value.togglePopperVisible(false);
  }
};

// 回到默认节点
const backToDefaultNode = () => {
  selectedArea.value = appStore.getLargeScreenPathId;
  handleAreaChange(appStore.getLargeScreenPathId);
};

// 获取地址参数
const getAddressParams = (areaIds: number[]): RequestParams => {
  const params: RequestParams = {
    city: userStore.userInfo.city
  };
  if (!areaIds?.length) return params;
  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return {};
  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }
  return params;
};

// 时间选择器相关的响应式变量 - 使用初始值
const selectedDate = ref(props.initialDate || dayjs().format("YYYY-MM-DD"));
const selectedMonth = ref(props.initialMonth || dayjs().format("YYYY-MM"));
const selectedYear = ref(props.initialYear || dayjs().format("YYYY"));
const selectedView = ref<TimeRangeType>(props.initialMode || "month");
const selectedDateRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD")
]);

// TimeRangeSelector 组件引用
const timeRangeSelectorRef = ref();

// TimeRangeSelector 事件处理
const handleTimeChange = (value: TimeRangeType) => {
  selectedView.value = value;
  currentRange.value = value;
  handleRangeChange(value);
};

const handleDateChange = (date: string) => {
  selectedDate.value = date;
  selectedDateRange.value = [date, date];
  // 使用当前选中的地址参数并更新 currentAddress
  const params = getAddressParams(selectedArea.value);
  currentAddress.value = params || {};
  fetchChartDataHandle(currentAddress.value);
};

const handleMonthChange = (month: string) => {
  selectedMonth.value = month;
  const startDate = dayjs(month).startOf("month").format("YYYY-MM-DD");
  const today = dayjs();
  const monthEnd = dayjs(month).endOf("month");

  // 如果选择的月份是当前月份，结束日期使用今天；否则使用月末
  const endDate = monthEnd.isAfter(today)
    ? today.format("YYYY-MM-DD")
    : monthEnd.format("YYYY-MM-DD");

  selectedDateRange.value = [startDate, endDate];
  // 使用当前选中的地址参数并更新 currentAddress
  const params = getAddressParams(selectedArea.value);
  currentAddress.value = params || {};
  fetchChartDataHandle(currentAddress.value);
};

const handleYearChange = (year: string) => {
  selectedYear.value = year;
  const startDate = dayjs(year).startOf("year").format("YYYY-MM-DD");
  const today = dayjs();
  const yearEnd = dayjs(year).endOf("year");

  // 如果选择的年份是当前年份，结束日期使用今天；否则使用年末
  const endDate = yearEnd.isAfter(today)
    ? today.format("YYYY-MM-DD")
    : yearEnd.format("YYYY-MM-DD");

  selectedDateRange.value = [startDate, endDate];
  // 使用当前选中的地址参数并更新 currentAddress
  const params = getAddressParams(selectedArea.value);
  currentAddress.value = params || {};
  fetchChartDataHandle(currentAddress.value);
};

// 监听props变化
watch(
  () => props.initialMode,
  newMode => {
    if (newMode && newMode !== selectedView.value) {
      selectedView.value = newMode;
      currentRange.value = newMode;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialDate,
  newDate => {
    if (newDate && newDate !== selectedDate.value) {
      selectedDate.value = newDate;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialMonth,
  newMonth => {
    if (newMonth && newMonth !== selectedMonth.value) {
      selectedMonth.value = newMonth;
    }
  },
  { immediate: true }
);

watch(
  () => props.initialYear,
  newYear => {
    if (newYear && newYear !== selectedYear.value) {
      selectedYear.value = newYear;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute; // 更改为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 居中定位
  transform: translate(-50%, -50%) scale(var(--scale-ratio)); // 居中定位，保持大屏页面相同缩放比例
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整高度，设计定稿高度1271px，根据弹窗大小调整高度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 20px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .title {
    display: flex;
    gap: 8px;
    align-items: center;

    .title-text {
      @extend .fs-dialog-title; // 标题文字大小
      font-weight: 500;
      color: #fff;
      letter-spacing: 1px;
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.dialog-body {
  height: calc(100% - 80px);

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(255 255 255 / 20%);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgb(0 0 0 / 10%);
    border-radius: 3px;
  }
}

.persuasion-statistics {
  height: 100%;
  padding: 20px;
  overflow: hidden;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 4px;

  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid rgb(64 158 255 / 20%);

    .dialog-area-selector {
      flex: 0 0 300px;
      margin-right: 16px;
    }

    .header-center {
      flex: 0 0 auto;
      margin: 0 16px;

      .tab-group {
        display: flex;
        align-items: center;
        height: 40px;
        gap: 2px;
        padding: 2px;
        background: rgb(0 24 75 / 30%);
        border: 1px solid rgb(64 158 255 / 20%);
        border-radius: 4px;

        .tab-item {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 36px;
          padding: 0 16px;
          font-size: 24px; // Tab 文字大小
          line-height: 36px;
          color: rgb(255 255 255 / 70%);
          cursor: pointer;
          border-radius: 2px;
          transition: all 0.3s;

          &.active {
            color: #fff;
            background: #409eff;
          }

          &:hover:not(.active) {
            color: #fff;
          }
        }
      }
    }

    .header-sort {
      flex: 0 0 auto;
      display: flex;
      gap: 12px;
      align-items: center;
      margin: 0 16px;

      .custom-select {
        position: relative;
        margin-right: 12px;

        .select-trigger {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 200px;
          height: 36px;
          padding: 0 12px;
          font-size: 24px; // 选择器触发器文字大小
          line-height: 36px;
          color: rgb(255 255 255 / 90%);
          cursor: pointer;
          background: rgb(0 24 75 / 60%);
          border: 1px solid rgb(64 158 255 / 30%);
          border-radius: 4px;
          transition: all 0.3s;

          &:hover {
            background: rgb(0 24 75 / 80%);
            border-color: #409eff;
          }

          .select-icon {
            font-size: 24px; // 选择器图标大小
            color: rgb(255 255 255 / 70%);
            transition: transform 0.3s;

            &.is-reverse {
              transform: rotate(180deg);
            }
          }
        }

        .select-dropdown {
          position: absolute;
          top: calc(100% + 4px);
          left: 0;
          z-index: 10;
          width: 100%;
          padding: 4px 0;
          background: rgb(0 24 75 / 95%);
          border: 1px solid rgb(64 158 255 / 30%);
          border-radius: 4px;
          box-shadow: 0 2px 12px rgb(0 0 0 / 10%);

          .select-option {
            height: 36px;
            padding: 0 12px;
            font-size: 24px; // 下拉选项文字大小
            line-height: 36px;
            color: rgb(255 255 255 / 90%);
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              color: #fff;
              background: rgb(64 158 255 / 10%);
            }

            &.is-active {
              color: #409eff;
              background: rgb(64 158 255 / 10%);
            }
          }
        }
      }
    }

    .header-right {
      flex: 1;
      display: flex;
      gap: 12px;
      align-items: center;
      justify-content: flex-end;
      // 原有的时间选择器样式已移除，现在使用统一的 TimeRangeSelector 组件
    }
  }

  .stat-chart {
    width: 100%;
    height: calc(100% - 60px);
  }
}

// 原有的重复样式已移除，现在使用统一的组件样式

// 日期选择器样式已移除，使用 TimeRangeSelector 组件的统一样式
</style>

<style lang="scss">
// 级联选择器样式已移除，使用 AreaCascader 组件的统一样式
</style>
