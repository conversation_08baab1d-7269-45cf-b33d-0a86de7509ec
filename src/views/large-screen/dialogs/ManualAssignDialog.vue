<template>
  <!-- 人工下派弹窗 -->
  <teleport to="body">
    <div
      v-if="dialogVisible"
      class="dialog-container"
      @click.self="handleClose"
    >
      <div class="dialog-content">
        <dv-border-box-12
          class="dialog-border"
          style="background: transparent !important"
        >
          <!-- 标题栏 -->
          <div class="dialog-header">
            <div class="title">
              <dv-decoration-5 style="width: 60px; height: 30px" />
              <span class="title-text">人工下派</span>
            </div>
            <div class="large-screen-close-btn" @click="handleClose">
              <el-icon><Close /></el-icon>
            </div>
          </div>

          <!-- 内容区 -->
          <div class="dialog-body">
            <div class="content-placeholder">
              <div class="placeholder-icon">
                <el-icon size="64">
                  <svg
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                    />
                    <path
                      d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"
                    />
                  </svg>
                </el-icon>
              </div>
              <div class="placeholder-text">
                <h3>功能开发中</h3>
                <p>人工下派功能正在开发中，敬请期待...</p>
              </div>
            </div>
          </div>
        </dv-border-box-12>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts" name="ManualAssignDialog">
import { ref, watch } from "vue";
import { Close } from "@element-plus/icons-vue";

// Props
const props = defineProps<{
  modelValue: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "closed"): void;
}>();

// 弹窗显示状态
const dialogVisible = ref(props.modelValue);

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    dialogVisible.value = newValue;
  }
);

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  emit("update:modelValue", false);
  emit("closed");
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(var(--scale-ratio));
  width: 1792px;
  height: 1024px;
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

// 确保 dv-border-box-12 组件背景透明
:deep(.dv-border-box-12) {
  background: transparent !important;

  .border-box-content {
    background: transparent !important;
  }

  svg {
    background: transparent !important;
  }
}

.dialog-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);

  .title {
    display: flex;
    align-items: center;
    gap: 15px;

    .title-text {
      @extend .fs-dialog-title;
      font-weight: 600;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.dialog-body {
  flex: 1;
  padding: 20px 30px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;

  .placeholder-icon {
    margin-bottom: 20px;
    color: rgba(64, 158, 255, 0.6);
    opacity: 0.8;

    svg {
      width: 64px;
      height: 64px;
      fill: currentColor;
    }
  }

  .placeholder-text {
    h3 {
      margin: 0 0 10px 0;
      @extend .fs-dialog-title;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
    }

    p {
      margin: 0;
      @extend .fs-dialog-content;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.6;
    }
  }
}
</style>
