<template>
  <!-- 处理趋势分析弹窗 -->
  <teleport to="body">
    <div
      v-if="dialogVisible"
      class="dialog-container"
      @click.self="handleClose"
    >
      <div class="dialog-content">
        <dv-border-box-12
          class="dialog-border"
          style="background: transparent !important"
        >
          <!-- 标题栏 -->
          <div class="dialog-header">
            <div class="title">
              <dv-decoration-5 style="width: 60px; height: 30px" />
              <span class="title-text">精准劝导分析</span>
            </div>
            <div class="large-screen-close-btn" @click="handleClose">
              <el-icon><Close /></el-icon>
            </div>
          </div>

          <!-- 控制栏 -->
          <div class="control-header">
            <!-- 左侧节点树选择器 -->
            <div class="dialog-area-selector">
              <AreaCascader
                v-model="selectedArea"
                :show-home-button="true"
                :popper-append-to-body="true"
                @change="handleAreaChange"
              />
            </div>

            <!-- 右侧时间选择器 -->
            <div class="header-right">
              <TimeRangeSelector
                ref="timeRangeSelectorRef"
                v-model="selectedTimeRange"
                :enabled-ranges="['day', 'month', 'year']"
                :disable-future="true"
                @change="handleTimeRangeChange"
                @dateChange="handleDateChange"
                @monthChange="handleMonthChange"
                @yearChange="handleYearChange"
              />
            </div>
          </div>

          <!-- 内容区 -->
          <div class="dialog-body">
            <!-- 图表区域 -->
            <div class="chart-section">
              <!-- 图表容器 - 始终存在，通过CSS控制显示 -->
              <div
                ref="chartRef"
                class="detail-chart"
                :class="{ 'chart-hidden': loading || !hasData }"
              />

              <!-- 无数据时显示提示 -->
              <div v-if="!loading && !hasData" class="empty-state">
                <div class="empty-icon">📊</div>
                <div class="empty-text">暂无数据</div>
              </div>

              <!-- 加载状态 -->
              <div v-if="loading" class="loading-overlay">
                <div class="loading-spinner" />
                <div class="loading-text">数据加载中...</div>
              </div>
            </div>

            <!-- 数据表格区域 -->
            <div class="table-section">
              <div class="table-header">
                <h3 class="table-title">
                  地区汇总数据
                  <span v-if="selectedTimeRange !== 'day'" class="expand-hint">
                    （点击箭头展开{{
                      selectedTimeRange === "month" ? "每日" : "每月"
                    }}详情）
                  </span>
                </h3>
              </div>
              <div class="table-container">
                <el-table
                  :data="tableData"
                  stripe
                  style="width: 100%"
                  row-key="region"
                  max-height="280"
                  class="handle-detail-table"
                >
                  <!-- 展开行（仅月视图和年视图） -->
                  <el-table-column
                    v-if="selectedTimeRange !== 'day'"
                    type="expand"
                    width="50"
                    fixed="left"
                  >
                    <template #default="{ row }">
                      <div class="expand-content">
                        <div class="expand-table">
                          <div class="expand-table-wrapper">
                            <el-table
                              :data="getExpandData(row)"
                              size="small"
                              max-height="200"
                              class="expand-detail-table"
                            >
                              <el-table-column
                                prop="date"
                                :label="
                                  selectedTimeRange === 'month'
                                    ? '日期'
                                    : '月份'
                                "
                                width="150"
                                align="center"
                              />
                              <el-table-column
                                prop="totalTasks"
                                label="总任务数"
                                width="150"
                                align="center"
                              />
                              <el-table-column
                                prop="onTimeTasks"
                                label="按时完成"
                                width="150"
                                align="center"
                              />
                              <el-table-column
                                prop="completedTasks"
                                label="已完成"
                                width="120"
                                align="center"
                              />
                              <el-table-column
                                prop="canceledTasks"
                                label="30日过期数"
                                width="120"
                                align="center"
                              />
                              <el-table-column
                                prop="completionRate"
                                label="完成率"
                                width="120"
                                align="center"
                              >
                                <template #default="{ row: detailRow }">
                                  <span class="rate-text"
                                    >{{ detailRow.completionRate }}%</span
                                  >
                                </template>
                              </el-table-column>
                              <el-table-column
                                prop="onTimeRate"
                                label="按时完成率"
                                width="150"
                                align="center"
                              >
                                <template #default="{ row: detailRow }">
                                  <span class="rate-text"
                                    >{{ detailRow.onTimeRate }}%</span
                                  >
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                  <!-- 主要数据列 -->
                  <el-table-column
                    v-for="column in tableColumns"
                    :key="column.prop"
                    :prop="column.prop"
                    :label="column.label"
                    :width="column.width"
                    align="center"
                    :resizable="false"
                  >
                    <!-- 百分比列特殊处理 -->
                    <template
                      v-if="
                        column.prop === 'completionRate' ||
                        column.prop === 'onTimeRate'
                      "
                      #default="{ row }"
                    >
                      <span class="rate-text">{{ row[column.prop] }}%</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>

          <!-- 底部装饰 -->
          <dv-decoration-3
            class="dialog-footer"
            style="width: 100%; height: 4px"
          />
        </dv-border-box-12>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, nextTick, computed } from "vue";
import type { TimeRangeType } from "@/components/TimeRangeSelector.vue";
import type { TreeNode } from "@/views/system/system-user/utils/types";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector from "@/components/TimeRangeSelector.vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";
import { Close } from "@element-plus/icons-vue";
import {
  getDisposalEfficiencyAnalysisByDay,
  getDisposalEfficiencyAnalysisByMonth,
  getDisposalEfficiencyAnalysisByYear
} from "@/api/screen";
import { useUserStoreHook } from "@/store/modules/user";
import { useAppStoreHook } from "@/store/modules/app";

// 声明 store 实例
const userStore = useUserStoreHook();
const appStore = useAppStoreHook();

// 定义数据接口
interface TaskData {
  completedTasks: number;
  onTimeTasks: number;
  overdueTasks: number;
  canceledTasks: number;
  totalTasks: number;
  completionRate: number;
  onTimeRate: number;
  region: string;
  // 月数据特有的每日详情
  dailyDetails?: Array<{
    date: string; // 后端返回已格式化的日期字符串
    dailyCanceledTasks: number;
    dailyCompletedTasks: number;
    dailyOverdueTasks: number;
    dailyCompletionRate: number;
    dailyTotalTasks: number;
    dailyOnTimeTasks: number;
    dailyOnTimeRate: number;
    region: string;
  }>;
  // 年数据特有的每月详情
  monthlyDetails?: Array<{
    monthlyOnTimeTasks: number;
    monthlyOverdueTasks: number;
    month: string;
    monthlyCompletionRate: number;
    monthlyTotalTasks: number;
    monthlyCompletedTasks: number;
    monthlyCanceledTasks: number;
    region: string;
    monthlyOnTimeRate: number;
  }>;
}

interface Props {
  modelValue: boolean;
  initialArea?: number[];
  initialTimeRange?: TimeRangeType;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  initialArea: () => [],
  initialTimeRange: "month"
});

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "closed"): void;
}>();

// 弹窗状态
const dialogVisible = ref(props.modelValue);
const selectedArea = ref(props.initialArea);
const selectedTimeRange = ref<TimeRangeType>(props.initialTimeRange || "month"); // 默认显示月

// TimeRangeSelector 组件引用
const timeRangeSelectorRef = ref();

// 日期相关状态
const selectedDate = ref(new Date().toISOString().split("T")[0]);
const selectedMonth = ref(new Date().toISOString().slice(0, 7));
const selectedYear = ref(new Date().getFullYear().toString());

// 图表实例
const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 数据状态
const loading = ref(false);
const rawData = ref<TaskData[]>([]);

// 计算属性：判断是否有数据
const hasData = computed(() => {
  return rawData.value && rawData.value.length > 0;
});

// 计算属性：表格数据（总是显示外层汇总数据）
const tableData = computed(() => {
  return rawData.value || [];
});

// 后端已处理时间格式，无需前端格式化

// 获取展开行的详细数据
const getExpandData = (row: TaskData) => {
  switch (selectedTimeRange.value) {
    case "month":
      return (
        row.dailyDetails?.map(item => ({
          ...item,
          date: item.date, // 直接使用后端返回的日期
          totalTasks: item.dailyTotalTasks,
          completedTasks: item.dailyCompletedTasks,
          onTimeTasks: item.dailyOnTimeTasks,
          overdueTasks: item.dailyOverdueTasks,
          canceledTasks: item.dailyCanceledTasks,
          completionRate: item.dailyCompletionRate,
          onTimeRate: item.dailyOnTimeRate
        })) || []
      );
    case "year":
      return (
        row.monthlyDetails?.map(item => ({
          ...item,
          date: item.month, // 直接使用后端返回的月份
          totalTasks: item.monthlyTotalTasks,
          completedTasks: item.monthlyCompletedTasks,
          onTimeTasks: item.monthlyOnTimeTasks,
          overdueTasks: item.monthlyOverdueTasks,
          canceledTasks: item.monthlyCanceledTasks,
          completionRate: item.monthlyCompletionRate,
          onTimeRate: item.monthlyOnTimeRate
        })) || []
      );
    default:
      return [];
  }
};

// 表格列配置（只显示汇总数据的列）
const tableColumns = computed(() => {
  return [
    { prop: "region", label: "地区", width: 150 },
    { prop: "totalTasks", label: "总任务数", width: 150 },
    { prop: "onTimeTasks", label: "按时完成", width: 150 },
    { prop: "completedTasks", label: "已完成", width: 120 },
    { prop: "overdueTasks", label: "30日过期数", width: 120 },
    { prop: "completionRate", label: "完成率", width: 120 },
    { prop: "onTimeRate", label: "按时完成率", width: 150 }
  ];
});

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  val => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, val => {
  emit("update:modelValue", val);
  if (!val) {
    emit("closed");
    // 弹窗关闭时清理图表
    if (chart) {
      chart.dispose();
      chart = null;
    }
  } else {
    // 弹窗打开时，重置时间选择器状态
    if (timeRangeSelectorRef.value) {
      timeRangeSelectorRef.value.resetTimeSelector();
    }
    // 重置本地状态
    selectedTimeRange.value = "month";
    selectedDate.value = new Date().toISOString().split("T")[0];
    selectedMonth.value = new Date().toISOString().slice(0, 7);
    selectedYear.value = new Date().getFullYear().toString();

    // 弹窗显示时，立即获取数据
    fetchTrendData();
  }
});

// 获取地址参数
const getAddressParams = (areaIds: number[]) => {
  const params = {
    city: userStore.userInfo.city
  } as any;

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }

  return params;
};

// 根据ID查找节点
const findNodeById = (nodes: TreeNode[], id: number): TreeNode | undefined => {
  if (!nodes || !id) return undefined;

  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.childList) {
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }
  }
  return undefined;
};

// 获取趋势数据
const fetchTrendData = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    let response;
    const addressParams = selectedArea.value?.length
      ? getAddressParams(selectedArea.value)
      : appStore.getLargeScreenArea;

    switch (selectedTimeRange.value) {
      case "day":
        response = await getDisposalEfficiencyAnalysisByDay(
          selectedDate.value,
          addressParams
        );
        break;
      case "month":
        const [year, month] = selectedMonth.value.split("-").map(Number);
        response = await getDisposalEfficiencyAnalysisByMonth(
          year,
          month,
          addressParams
        );
        break;
      case "year":
        response = await getDisposalEfficiencyAnalysisByYear(
          Number(selectedYear.value),
          addressParams
        );
        break;
    }

    if (response?.code === 200 && response?.data?.data) {
      const { data } = response.data;
      rawData.value = data;

      // 数据更新后，等待DOM渲染完成再初始化图表
      await nextTick();
      await nextTick(); // 双重nextTick确保DOM完全渲染

      setTimeout(() => {
        initChart();
      }, 100); // 额外延迟确保容器可见
    } else {
      console.warn("API 返回数据格式异常:", response);
      rawData.value = []; // 清空数据，图表将不会渲染
    }
  } catch (error) {
    console.error("获取趋势数据失败:", error);
    rawData.value = []; // 清空数据，图表将不会渲染
  } finally {
    loading.value = false;
  }
};

// 根据时间范围处理图表数据
const getChartData = () => {
  if (!rawData.value || rawData.value.length === 0) {
    return { xAxisData: [], chartData: [] };
  }

  const timeRange = selectedTimeRange.value;

  if (timeRange === "day") {
    // 日数据：直接使用地区汇总数据
    const xAxisData = rawData.value.map(item => item.region);
    const chartData = rawData.value.map(item => ({
      totalTasks: item.totalTasks,
      completedTasks: item.completedTasks,
      onTimeTasks: item.onTimeTasks,
      completionRate: item.completionRate,
      onTimeRate: item.onTimeRate,
      region: item.region
    }));
    return { xAxisData, chartData };
  }

  if (timeRange === "month") {
    // 月数据：使用每日详情数据
    const firstRegion = rawData.value[0];
    if (!firstRegion.dailyDetails || firstRegion.dailyDetails.length === 0) {
      // 如果没有每日详情，使用汇总数据
      const xAxisData = rawData.value.map(item => item.region);
      const chartData = rawData.value.map(item => ({
        totalTasks: item.totalTasks,
        completedTasks: item.completedTasks,
        onTimeTasks: item.onTimeTasks,
        completionRate: item.completionRate,
        onTimeRate: item.onTimeRate,
        region: item.region
      }));
      return { xAxisData, chartData };
    }

    // 使用每日详情，格式化X轴显示为只显示日期（去掉前导零）
    const xAxisData = firstRegion.dailyDetails.map(item => {
      // 从完整日期 "2025-01-15" 中提取日期部分 "15"，并去掉前导零
      const date = item.date;
      if (date && date.includes("-")) {
        const parts = date.split("-");
        if (parts.length >= 3) {
          const dayNum = parseInt(parts[2]);
          return `${dayNum}号`;
        }
      }
      // 如果是纯数字格式，直接转换并去掉前导零
      const dayNum = parseInt(date);
      return isNaN(dayNum) ? date : `${dayNum}号`;
    });
    const chartData = firstRegion.dailyDetails.map(item => ({
      totalTasks: item.dailyTotalTasks,
      completedTasks: item.dailyCompletedTasks,
      onTimeTasks: item.dailyOnTimeTasks,
      completionRate: item.dailyCompletionRate,
      onTimeRate: item.dailyOnTimeRate,
      region: item.date, // 保留完整日期用于tooltip显示
      originalDate: item.date // 明确保存原始日期
    }));
    return { xAxisData, chartData };
  }

  if (timeRange === "year") {
    // 年数据：使用每月详情数据
    const firstRegion = rawData.value[0];
    if (
      !firstRegion.monthlyDetails ||
      firstRegion.monthlyDetails.length === 0
    ) {
      // 如果没有每月详情，使用汇总数据
      const xAxisData = rawData.value.map(item => item.region);
      const chartData = rawData.value.map(item => ({
        totalTasks: item.totalTasks,
        completedTasks: item.completedTasks,
        onTimeTasks: item.onTimeTasks,
        completionRate: item.completionRate,
        onTimeRate: item.onTimeRate,
        region: item.region
      }));
      return { xAxisData, chartData };
    }

    // 使用每月详情，格式化X轴显示为只显示月份（去掉前导零）
    const xAxisData = firstRegion.monthlyDetails.map(item => {
      // 从完整月份 "2025-01" 中提取月份部分 "01"，并去掉前导零
      const month = item.month;
      if (month && month.includes("-")) {
        const parts = month.split("-");
        if (parts.length >= 2) {
          const monthNum = parseInt(parts[1]);
          return `${monthNum}月`;
        }
      }
      // 如果是纯数字格式，直接转换并去掉前导零
      const monthNum = parseInt(month);
      return isNaN(monthNum) ? month : `${monthNum}月`;
    });
    const chartData = firstRegion.monthlyDetails.map(item => ({
      totalTasks: item.monthlyTotalTasks,
      completedTasks: item.monthlyCompletedTasks,
      onTimeTasks: item.monthlyOnTimeTasks,
      completionRate: item.monthlyCompletionRate,
      onTimeRate: item.monthlyOnTimeRate,
      region: item.month, // 保留完整月份用于tooltip显示
      originalMonth: item.month // 明确保存原始月份
    }));
    return { xAxisData, chartData };
  }

  return { xAxisData: [], chartData: [] };
};

// 更新图表数据
const updateChart = () => {
  if (!chart) {
    console.warn("图表实例不存在，无法更新");
    return;
  }

  const { xAxisData, chartData } = getChartData();

  // 如果没有数据，显示空状态
  if (!chartData || chartData.length === 0) {
    const emptyOption: EChartsOption = {
      title: {
        text: "暂无数据",
        left: "center",
        top: "middle",
        textStyle: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 48 // 图表无数据提示文字字体大小
        }
      },
      xAxis: { show: false },
      yAxis: { show: false },
      series: []
    };
    chart.setOption(emptyOption);
    return;
  }

  // 提取各项数据
  const totalTasks = chartData.map(item => item.totalTasks);
  const completedTasks = chartData.map(item => item.completedTasks);
  const onTimeTasks = chartData.map(item => item.onTimeTasks);

  // 计算默认显示范围
  const defaultEndPercent = Math.min(
    100,
    (10 / Math.max(xAxisData.length, 1)) * 100
  );

  const option: EChartsOption = {
    legend: {
      data: ["总任务数", "已完成", "按时完成"],
      top: 10,
      textStyle: {
        color: "#fff", // 图表图例文字颜色
        fontSize: 22 // 图表图例文字颜色
      }
    },
    grid: {
      top: 60,
      right: 60,
      bottom: 60,
      left: 80,
      containLabel: true
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 24, 75, 0.9)",
      borderColor: "rgba(64, 158, 255, 0.3)",
      borderWidth: 1,
      padding: [6, 8], // 减小内边距，让tooltip更紧凑
      textStyle: {
        color: "#fff", // 图表提示框文字颜色
        fontSize: 14 // 减小字体大小，让tooltip更小巧
      },
      // 优化tooltip位置，防止被遮挡
      confine: true, // 限制tooltip在图表容器内
      appendToBody: true, // 将tooltip添加到body，避免被对话框容器裁剪
      extraCssText:
        "box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4); border-radius: 4px; z-index: 99999;",
      position: function (point, params, dom, rect, size) {
        // 智能位置计算，防止tooltip超出边界
        const [mouseX, mouseY] = point;
        const { contentSize, viewSize } = size;
        const [tooltipWidth, tooltipHeight] = contentSize;
        const [containerWidth, containerHeight] = viewSize;

        let x = mouseX + 10; // 默认在鼠标右侧
        let y = mouseY - tooltipHeight / 2; // 垂直居中

        // 检查右边界，如果超出则显示在左侧
        if (x + tooltipWidth > containerWidth) {
          x = mouseX - tooltipWidth - 10;
        }

        // 检查上下边界
        if (y < 0) {
          y = 10;
        } else if (y + tooltipHeight > containerHeight) {
          y = containerHeight - tooltipHeight - 10;
        }

        return [x, y];
      },
      formatter: (params: any) => {
        // 自定义提示框内容，显示完整日期/月份信息
        let displayTitle = params[0].name;

        // 如果是月视图或年视图，获取完整的日期/月份信息
        if (params[0] && params[0].dataIndex !== undefined) {
          const dataIndex = params[0].dataIndex;
          const currentData = chartData[dataIndex] as any;
          if (currentData) {
            // 月视图显示完整日期，年视图显示完整月份
            if (
              selectedTimeRange.value === "month" &&
              currentData.originalDate
            ) {
              displayTitle = currentData.originalDate;
            } else if (
              selectedTimeRange.value === "year" &&
              currentData.originalMonth
            ) {
              displayTitle = currentData.originalMonth;
            } else if (currentData.region) {
              displayTitle = currentData.region;
            }
          }
        }

        let result = `<div style="color: #fff; font-weight: 500; margin-bottom: 2px; font-size: 13px;">
          ${displayTitle}
        </div>`;

        // 显示柱状图数据
        params.forEach((param: any) => {
          const value = param.value === undefined ? "-" : param.value;
          result += `<div style="display: flex; align-items: center; margin: 2px 0; font-size: 12px; line-height: 1.2;">
            <span style="display: inline-block; width: 6px; height: 6px; background-color: ${param.color}; border-radius: 50%; margin-right: 4px;"></span>
            ${param.seriesName}：<span style="color: rgba(255, 255, 255, 0.8); margin-left: 2px;">${value}</span>
          </div>`;
        });

        // 添加完成率信息
        if (params[0] && params[0].dataIndex !== undefined) {
          const dataIndex = params[0].dataIndex;
          const currentData = chartData[dataIndex];
          if (currentData) {
            result += `<div style="border-top: 1px solid rgba(255, 255, 255, 0.2); margin: 4px 0 2px 0; padding-top: 2px;">`;
            result += `<div style="display: flex; align-items: center; margin: 2px 0; font-size: 12px; line-height: 1.2;">
              <span style="display: inline-block; width: 6px; height: 6px; background-color: #67C23A; border-radius: 50%; margin-right: 4px;"></span>
              完成率：<span style="color: rgba(255, 255, 255, 0.8); margin-left: 2px;">${currentData.completionRate}%</span>
            </div>`;
            result += `<div style="display: flex; align-items: center; margin: 2px 0; font-size: 12px; line-height: 1.2;">
              <span style="display: inline-block; width: 6px; height: 6px; background-color: #E6A23C; border-radius: 50%; margin-right: 4px;"></span>
              按时完成率：<span style="color: rgba(255, 255, 255, 0.8); margin-left: 2px;">${currentData.onTimeRate}%</span>
            </div>`;
            result += `</div>`;
          }
        }

        return result;
      }
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)"
        }
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22, // 图表X轴标签字体大小
        interval: 0 // 强制显示所有标签
      }
    },
    yAxis: {
      type: "value",
      name: "任务数量",
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // Y轴名称文字颜色，使用默认字体大小
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.3)"
        }
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 22 // Y轴标签文字颜色，使用默认字体大小
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      }
    },
    dataZoom: [
      {
        type: "inside", // 内置缩放，支持鼠标滚轮和双指缩放
        xAxisIndex: 0,
        start: 0,
        end: defaultEndPercent // 默认显示前10条数据
      },
      {
        type: "slider", // 滑动条缩放
        xAxisIndex: 0,
        start: 0,
        end: defaultEndPercent, // 默认显示前10条数据
        bottom: 12,
        zoomLock: true, // 锁定缩放，只允许平移
        moveOnMouseMove: true, // 鼠标拖拽时平移
        moveOnMouseWheel: false, // 禁用鼠标滚轮在滑动条上的缩放
        textStyle: {
          color: "rgba(255, 255, 255, 0.7)" // 图表缩放滑动条文字颜色，使用默认字体大小
        },
        fillerColor: "rgba(64, 158, 255, 0.3)",
        backgroundColor: "rgba(0, 24, 75, 0.5)",
        borderColor: "rgba(64, 158, 255, 0.3)"
      }
    ],
    series: [
      {
        name: "总任务数",
        type: "bar",
        data: totalTasks,
        barWidth: 30,
        barGap: "20%",
        itemStyle: {
          color: "#409EFF",
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: "#66B3FF"
          }
        },
        label: {
          show: true,
          position: "top",
          color: "rgba(255, 255, 255, 0.8)",
          fontSize: 18, // 总任务数柱状图数值标签字体大小
          fontWeight: 600
        }
      },
      {
        name: "已完成",
        type: "bar",
        data: completedTasks,
        barWidth: 30,
        itemStyle: {
          color: "#67C23A",
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: "#85CE61"
          }
        },
        label: {
          show: true,
          position: "top",
          color: "rgba(255, 255, 255, 0.8)",
          fontSize: 18, // 已完成柱状图数值标签字体大小
          fontWeight: 600
        }
      },
      {
        name: "按时完成",
        type: "bar",
        data: onTimeTasks,
        barWidth: 30,
        itemStyle: {
          color: "#E6A23C",
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: "#EEBE77"
          }
        },
        label: {
          show: true,
          position: "top",
          color: "rgba(255, 255, 255, 0.8)",
          fontSize: 18, // 按时完成柱状图数值标签字体大小
          fontWeight: 600
        }
      }
    ]
  };

  chart.setOption(option, true);

  // 强制重新计算尺寸
  setTimeout(() => {
    if (chart) {
      chart.resize();
    }
  }, 100);
};

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize();
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};

// 处理时间范围变化
const handleTimeRangeChange = (range: TimeRangeType) => {
  selectedTimeRange.value = range;
  fetchTrendData();
};

// 处理日期变化
const handleDateChange = (date: string) => {
  selectedDate.value = date;
  if (selectedTimeRange.value === "day") {
    fetchTrendData();
  }
};

// 处理月份变化
const handleMonthChange = (month: string) => {
  selectedMonth.value = month;
  if (selectedTimeRange.value === "month") {
    fetchTrendData();
  }
};

// 处理年份变化
const handleYearChange = (year: string) => {
  selectedYear.value = year;
  if (selectedTimeRange.value === "year") {
    fetchTrendData();
  }
};

// 处理区域变化
const handleAreaChange = () => {
  fetchTrendData();
};

// 组件挂载时初始化
onMounted(() => {
  window.addEventListener("resize", handleResize);

  // 如果弹窗已经显示，立即获取数据
  if (dialogVisible.value) {
    fetchTrendData();
  }
});

// 初始化图表函数
const initChart = () => {
  if (!chartRef.value) {
    console.warn("chartRef.value为null，图表容器未找到");
    return;
  }

  // 检查容器是否可见
  const rect = chartRef.value.getBoundingClientRect();

  if (rect.width === 0 || rect.height === 0) {
    console.warn("图表容器尺寸为0，延迟初始化");
    setTimeout(() => {
      initChart();
    }, 200);
    return;
  }

  try {
    // 销毁旧图表实例
    if (chart) {
      chart.dispose();
      chart = null;
    }

    chart = echarts.init(chartRef.value);

    // 图表创建完成后更新数据
    updateChart();
  } catch (error) {
    console.error("图表初始化失败:", error);
  }
};

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  chart?.dispose();
  chart = null;
});
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss"; // 导入统一字体大小配置文件

// 公共样式：隐藏滚动条但保持滚动功能
%hide-scrollbar {
  scrollbar-width: none; // Firefox 隐藏滚动条
  -ms-overflow-style: none; // IE 隐藏滚动条

  &::-webkit-scrollbar {
    display: none; // Chrome, Safari, Edge 隐藏滚动条
  }
}

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(var(--scale-ratio));
  width: 1792px;
  height: 1024px;
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

// 确保 dv-border-box-12 组件背景透明
:deep(.dv-border-box-12) {
  background: transparent !important;

  .border-box-content {
    background: transparent !important;
  }

  svg {
    background: transparent !important;
  }
}

.dialog-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);

  .title {
    display: flex;
    align-items: center;
    gap: 15px;

    .title-text {
      @extend .fs-dialog-title; // 24px - 弹窗标题字体大小
      font-weight: 600;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.control-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);

  .dialog-area-selector {
    flex: 1;
    position: relative;
    z-index: 10001; // 确保下拉菜单在弹窗上方
  }

  .header-right {
    flex-shrink: 0;
  }
}

.dialog-body {
  flex: 1;
  padding: 20px 30px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .chart-section {
    flex: 1;
    position: relative;
    min-height: 360px;

    .detail-chart {
      width: 100%;
      height: 100%;
      min-height: 360px;

      &.chart-hidden {
        visibility: hidden;
        position: absolute;
      }
    }
  }

  .table-section {
    height: 320px;
    max-height: 320px;
    display: flex;
    flex-direction: column;
    overflow: visible; // 改为visible，让内容可以超出
    align-items: center; // 表格区域水平居中
    .table-header {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      background: rgb(2 48 115 / 80%); // 使用与上岗情况分析表格一致的深蓝色背景
      border: 1px solid rgba(64, 158, 255, 0.4); // 增强边框颜色
      border-radius: 8px 8px 0 0;

      .table-title {
        @extend .fs-persuasion-table-title; // - 表格标题字体大小
        font-weight: 600;
        color: #fff; // 纯白色字体
        margin: 0;

        .expand-hint {
          @extend .fs-persuasion-table-expand-hint; // 14px - 表格展开提示文字字体大小
          font-weight: 400;
          color: rgba(255, 255, 255, 0.7); // 稍微提高提示文字的亮度
          margin-left: 10px;
        }
      }
    }

    .table-container {
      flex: 1;
      border: 1px solid rgba(64, 158, 255, 0.4); // 增强边框颜色
      border-top: none;
      border-radius: 0 0 8px 8px;
      overflow: auto; // 容器本身可以滚动
      max-height: 280px; // 限制最大高度

      @extend %hide-scrollbar;

      .rate-text {
        color: #67c23a;
        font-weight: 600;
      }

      // 主表格样式
      :deep(.handle-detail-table) {
        background: transparent;
        color: rgba(255, 255, 255, 0.8);

        .el-table__inner-wrapper {
          &::before {
            background: none;
          }
        }

        .el-table__body-wrapper {
          background: transparent;
        }

        // 表头样式（仅作用于主表格） - 参考上岗情况分析表格
        &.handle-detail-table .el-table__header-wrapper {
          th {
            position: relative;
            background: rgb(2 48 115 / 80%) !important; // 使用深蓝色背景
            color: #fff !important; // 纯白色字体
            border: none !important;
            font-weight: 600 !important;
            padding: 12px 0; // 表格表头内边距
            @extend .fs-persuasion-table-title; // 主表格表头字体大小

            // 添加底部渐变线条
            &::after {
              position: absolute;
              right: 0;
              bottom: 0;
              left: 0;
              height: 2px;
              content: "";
              background: linear-gradient(
                90deg,
                rgb(64 158 255 / 20%) 0%,
                rgb(64 158 255 / 80%) 50%,
                rgb(64 158 255 / 20%) 100%
              );
            }
          }
        }

        &.handle-detail-table .el-table__row {
          background: transparent;

          td {
            background: rgba(0, 24, 75, 0.3) !important;
            color: rgba(255, 255, 255, 0.8) !important;
            border-right: 1px solid rgba(64, 158, 255, 0.2) !important; // 添加右边框
            border-bottom: 1px solid rgba(64, 158, 255, 0.2) !important; // 增强底边框
            @extend .fs-persuasion-table; // 主表格单元格字体大小 - 确保可读性

            &:last-child {
              border-right: none !important; // 最后一列不显示右边框
            }
          }

          &:hover td {
            background: rgba(64, 158, 255, 0.2) !important;
            border-bottom-color: rgba(
              64,
              158,
              255,
              0.3
            ) !important; // 悬停时增强边框颜色
          }

          &.el-table__row--striped td {
            background: rgba(0, 24, 75, 0.2) !important;
          }

          &:last-child td {
            border-bottom: none !important; // 最后一行不显示底边框
          }
        }

        .el-table__empty-block {
          background: rgba(0, 24, 75, 0.3);
          color: rgba(255, 255, 255, 0.6);
        }

        // 展开行样式
        .el-table__expand-column {
          .el-table__expand-icon {
            color: rgba(255, 255, 255, 0.7);

            &:hover {
              color: #409eff;
            }
          }
        }

        .el-table__expanded-cell {
          background: rgba(0, 24, 75, 0.4) !important;
          padding: 10px 20px;
        }
      }

      // 展开内容样式
      .expand-content {
        margin-left: 28px; // 展开内容向右移动，减少左边距

        .expand-table {
          .expand-table-wrapper {
            max-height: 200px; // 限制展开内容高度
            overflow: auto; // 允许滚动
            border: 1px solid rgba(64, 158, 255, 0.2);
            border-radius: 4px;
            @extend %hide-scrollbar;
          }

          // 展开表格样式
          :deep(.expand-detail-table) {
            background: transparent;
            .el-table__header-wrapper {
              th {
                background: rgb(2 48 115 / 80%) !important; // 使用深蓝色背景
                color: #fff !important; // 纯白色字体
                @extend .fs-persuasion-table-expand-header; // 展开表格表头字体大小
                border-right: 1px solid rgba(64, 158, 255, 0.3) !important; // 添加右边框
                border-bottom: 1px solid rgba(64, 158, 255, 0.4) !important; // 添加底边框

                &:last-child {
                  border-right: none !important; // 最后一列不显示右边框
                }
              }
            }

            .el-table__body-wrapper {
              background: rgba(0, 24, 75, 0.2);
            }

            .el-table__row {
              td {
                background: rgba(0, 24, 75, 0.2) !important;
                color: rgba(255, 255, 255, 0.7) !important;
                @extend .fs-persuasion-table-expand-header; // 展开表格单元格字体大小
                border-right: 1px solid rgba(64, 158, 255, 0.2) !important; // 添加右边框
                border-bottom: 1px solid rgba(64, 158, 255, 0.2) !important; // 添加底边框

                &:last-child {
                  border-right: none !important; // 最后一列不显示右边框
                }
              }

              &:hover td {
                background: rgba(64, 158, 255, 0.15) !important;
                border-bottom-color: rgba(
                  64,
                  158,
                  255,
                  0.3
                ) !important; // 悬停时增强边框颜色
              }

              &:last-child td {
                border-bottom: none !important; // 最后一行不显示底边框
              }
            }
          }
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    background: rgba(0, 24, 75, 0.8);
    backdrop-filter: blur(4px);
    z-index: 10;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(64, 158, 255, 0.3);
      border-top: 3px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      @extend .fs-persuasion-table-loading-text; // 16px - 加载提示文字字体大小
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    gap: 20px;

    .empty-icon {
      font-size: 48px; // 空状态图标字体大小
      opacity: 0.6;
    }

    .empty-text {
      @extend .fs-persuasion-table-loading-text; // 18px - 空状态提示文字字体大小
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

.dialog-footer {
  height: 4px;
  margin-top: auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 主题色适配
:root {
  --handle-detail-primary: var(--el-color-primary, #409eff);
  --handle-detail-success: var(--el-color-success, #67c23a);
  --handle-detail-warning: var(--el-color-warning, #e6a23c);
}

// 确保Element Plus下拉菜单在弹窗中正常显示
:deep(.el-cascader__dropdown),
:deep(.el-popper),
:deep(.el-picker__popper) {
  z-index: 10002 !important;
}

// 全局CSS覆盖，确保所有下拉菜单都在弹窗上方
:global(.el-cascader__dropdown),
:global(.el-popper),
:global(.el-picker__popper),
:global(.el-select__popper),
:global(.el-autocomplete-suggestion) {
  z-index: 19999 !important;
}
</style>
