<template>
  <!-- 未处理报警弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="报警信息"
    :close-on-click-modal="false"
    class="unhandled-alarms-dialog"
    width="1000px"
    :show-close="false"
  >
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <div :id="titleId" :class="titleClass" class="dialog-title">
          报警信息
        </div>
        <div class="large-screen-close-btn" @click="dialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </template>
    <el-tabs v-model="activeTab" class="alarm-tabs">
      <el-tab-pane label="未处理" name="unhandled">
        <div class="alarm-list">
          <div
            v-for="alarm in unhandledAlarms"
            :key="alarm.id"
            class="alarm-item"
          >
            <div class="alarm-header">
              <span class="alarm-time">{{
                new Date(alarm.createTime).toLocaleString()
              }}</span>
              <el-tag :type="getAlarmTypeTag(alarm.type)">{{
                alarm.alarmType
              }}</el-tag>
            </div>
            <div class="alarm-content">
              <div class="alarm-location">
                {{
                  `${alarm.city}${alarm.county}${alarm.township}${alarm.hamlet}${alarm.site}`
                }}
              </div>
              <div class="alarm-desc">
                <div>报警内容: {{ alarm.content }}</div>
              </div>
            </div>
            <div class="alarm-footer">
              <el-button
                type="primary"
                size="small"
                @click="handleAlarm(alarm)"
              >
                查看视频
              </el-button>
            </div>
          </div>
          <el-empty
            v-if="!unhandledAlarms.length"
            style="margin-top: 60px"
            :image-size="380"
            :image="EmptyImg"
            description="暂无未处理报警"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane label="已处理" name="handled">
        <div class="alarm-list">
          <div
            v-for="alarm in handledAlarms"
            :key="alarm.id"
            class="alarm-item"
          >
            <div class="alarm-header">
              <span class="alarm-time">{{
                new Date(alarm.createTime).toLocaleString()
              }}</span>
              <el-tag :type="getAlarmTypeTag(alarm.type)">{{
                alarm.alarmType
              }}</el-tag>
            </div>
            <div class="alarm-content">
              <div class="alarm-location">
                {{
                  `${alarm.city}${alarm.county}${alarm.township}${alarm.hamlet}${alarm.site}`
                }}
              </div>
              <div class="alarm-desc">
                <div>报警内容: {{ alarm.content }}</div>
              </div>
            </div>
            <div class="alarm-footer">
              <span class="handle-time"
                >处理时间:
                {{ new Date(alarm.updateTime).toLocaleString() }}</span
              >
            </div>
          </div>
          <el-empty
            v-if="!handledAlarms.length"
            style="margin-top: 60px"
            :image-size="380"
            :image="EmptyHandle"
            description="暂无已处理报警"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup lang="ts">
import { getAlarmPrompt } from "@/api/alarm";
// @ts-ignore
import EmptyImg from "@/assets/svg/empty.svg?.component";
// @ts-ignore
import EmptyHandle from "@/assets/svg/empty-handle.svg?.component";
import { Close } from "@element-plus/icons-vue";
import { computed, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { useUserStoreHook } from "@/store/modules/user";
const props = defineProps<{
  visible: boolean;
}>();

// 定义定时器变量
let timer: ReturnType<typeof setInterval> | null = null;
const userStore = useUserStoreHook();
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "handle", alarm: any): void;
  (e: "update:alarmCount", count: number): void;
}>();

const dialogVisible = computed({
  get: () => props.visible,
  set: val => emit("update:visible", val)
});

const activeTab = ref<string | number>("unhandled");

// 报警数据
const unhandledAlarms = ref<any[]>([]);
const handledAlarms = ref<any[]>([]);

// 处理报警
const handleAlarm = (alarm: any) => {
  emit("handle", alarm);
};

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    const res = await getAlarmPrompt(userStore.getUserLocationPath);
    if (res.code === 200) {
      // 使用接口返回的已处理和未处理数据
      unhandledAlarms.value = res.data.unhandled || [];
      handledAlarms.value = res.data.handled || [];
      // 触发父组件更新报警数量
      emit("update:alarmCount", unhandledAlarms.value.length);
    }
  } catch (error) {
    console.error("获取报警数据失败:", error);
  }
};

const getAlarmTypeTag = (type: string) => {
  const typeMap = {
    手动报警: "danger",
    设备报警: "warning",
    其他: "info"
  };
  return typeMap[type] || "info";
};

// 初始加载数据
onMounted(() => {
  fetchAlarmData(); // 初始加载
  // 设置定时刷新
  if (!timer) {
    timer = setInterval(fetchAlarmData, 10000); // 改为10秒刷新一次
  }
});

// 监听对话框显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      fetchAlarmData(); // 打开对话框时刷新数据
    }
  }
);

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style lang="scss">
.unhandled-alarms-dialog {
  overflow: hidden;
  background: rgb(0 12 23 / 90%);
  backdrop-filter: blur(12px);
  border: 1px solid rgb(64 158 255 / 30%);
  border-radius: 8px;
  box-shadow: 0 0 30px rgb(0 110 255 / 15%);

  .el-dialog__header {
    position: relative;
    padding: 0;
    margin: 0;
    border-bottom: 1px solid rgb(64 158 255 / 15%);

    &::after {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      content: "";
      background: linear-gradient(
        90deg,
        transparent,
        rgb(64 158 255 / 30%),
        transparent
      );
    }
  }

  .dialog-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 20px 16px;

    .dialog-title {
      display: flex;
      gap: 8px;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 30%);

      .el-icon {
        font-size: 24px;
        color: #409eff;
        filter: drop-shadow(0 0 3px rgb(64 158 255 / 50%));
      }
    }

    // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
  }

  .el-dialog__body {
    padding: 0;
    background: rgb(0 24 75 / 50%);

    .alarm-tabs {
      .el-tabs__header {
        position: relative;
        padding: 0 20px;
        margin: 16px 0;
        border-bottom: 1px solid rgb(64 158 255 / 20%);

        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1px;
          content: "";
          background: linear-gradient(
            90deg,
            transparent,
            rgb(64 158 255 / 30%),
            transparent
          );
        }

        .el-tabs__nav-wrap::after {
          background-color: transparent;
        }

        .el-tabs__item {
          height: 40px;
          padding: 0 24px;
          font-size: 16px;
          line-height: 40px;
          color: rgb(255 255 255 / 70%);
          transition: all 0.3s;

          &.is-active {
            font-weight: 500;
            color: #409eff;
            background: rgb(64 158 255 / 10%);
            border-radius: 4px 4px 0 0;
            box-shadow: inset 0 0 20px rgb(64 158 255 / 10%);
          }

          &:hover {
            color: #409eff;
            background: rgb(64 158 255 / 5%);
          }
        }

        .el-tabs__active-bar {
          height: 3px;
          background-color: #409eff;
          border-radius: 2px;
          box-shadow: 0 0 6px rgb(64 158 255 / 50%);
        }
      }

      .el-tabs__content {
        height: 600px;
        padding: 0 20px 20px;
      }
    }
  }

  .alarm-list {
    max-height: 600px;
    padding-right: 10px;
    overflow-y: auto;

    .el-empty {
      .el-empty__description {
        transform: translateY(-90px);
      }
    }

    &::-webkit-scrollbar {
      width: 6px;
      background: rgb(0 24 75 / 20%);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgb(64 158 255 / 30%);
      border-radius: 3px;

      &:hover {
        background: rgb(64 158 255 / 50%);
      }
    }
  }

  .alarm-item {
    position: relative;
    padding: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    background: rgb(6 30 93 / 35%);
    backdrop-filter: blur(4px);
    border: 1px solid rgb(64 158 255 / 20%);
    border-radius: 4px;
    transition: all 0.3s;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 1px;
      content: "";
      background: linear-gradient(
        90deg,
        transparent,
        rgb(64 158 255 / 30%),
        transparent
      );
    }

    .alarm-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .alarm-time {
        font-family: Orbitron, monospace;
        font-size: 14px;
        color: rgb(255 255 255 / 70%);
        text-shadow: 0 0 10px rgb(64 158 255 / 30%);
      }

      .el-tag {
        padding: 4px 8px;
        font-size: 13px;
        border: none;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgb(0 0 0 / 10%);

        &.el-tag--danger {
          color: #f56c6c;
          background: rgb(245 108 108 / 20%);
          border-left: 2px solid #f56c6c;
        }

        &.el-tag--warning {
          color: #e6a23c;
          background: rgb(230 162 60 / 20%);
          border-left: 2px solid #e6a23c;
        }

        &.el-tag--info {
          color: #909399;
          background: rgb(144 147 153 / 20%);
          border-left: 2px solid #909399;
        }
      }
    }

    .alarm-content {
      margin-bottom: 12px;

      .alarm-location {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        text-shadow: 0 0 10px rgb(64 158 255 / 30%);
      }

      .alarm-desc {
        font-size: 14px;
        line-height: 1.5;
        color: rgb(255 255 255 / 80%);
      }
    }

    .alarm-footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .el-button {
        padding: 8px 16px;
        font-size: 14px;
        color: #fff;
        background: rgb(64 158 255 / 20%);
        border: 1px solid rgb(64 158 255 / 30%);
        border-radius: 2px;
        transition: all 0.3s;

        &:hover {
          background: rgb(64 158 255 / 30%);
          border-color: rgb(64 158 255 / 50%);
          box-shadow: 0 2px 12px rgb(64 158 255 / 25%);
          transform: translateY(-1px);
        }
      }

      .handle-time {
        font-family: Orbitron, monospace;
        font-size: 13px;
        color: rgb(255 255 255 / 60%);
      }
    }
  }

  // 隐藏默认的关闭按钮
  :deep(.el-dialog__headerbtn) {
    display: none;
  }
}
</style>
