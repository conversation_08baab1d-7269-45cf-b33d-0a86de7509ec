<template>
  <!-- 效率分析弹窗 -->
  <div v-if="visible" class="dialog-container" @click.self="closeDialog">
    <div class="dialog-content">
      <dv-border-box-12 class="dialog-border">
        <!-- 标题栏 -->
        <div class="dialog-header">
          <div class="title-section">
            <dv-decoration-5 style="width: 60px; height: 30px" />
            <div class="title-content">
              <h2 class="title-text">勤务安排详情</h2>
              <!-- <span class="time-badge">{{ displayTime }}</span> -->
            </div>
          </div>
          <div class="large-screen-close-btn" @click="closeDialog">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <!-- 控制栏 -->
        <div class="control-header">
          <!-- 左侧节点选择器 -->
          <div class="dialog-area-selector">
            <AreaCascader
              v-model="selectedArea"
              :show-home-button="true"
              @change="handleAreaChange"
            />
          </div>

          <!-- 右侧时间选择器 -->
          <div class="header-right">
            <TimeRangeSelector
              ref="timeRangeSelectorRef"
              v-model="selectedTimeRange"
              :enabled-ranges="['day', 'month', 'year']"
              :custom-labels="{ day: '日', month: '月', year: '年' }"
              :disable-future="true"
              :initial-date="currentDate"
              :initial-month="currentMonth"
              :initial-year="currentYear"
              @change="handleTimeRangeChange"
              @date-change="handleDateChange"
              @month-change="handleMonthChange"
              @year-change="handleYearChange"
            />
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="dialog-body">
          <!-- 统计概览卡片 -->
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-label">总勤务安排</div>
              <div class="stat-value">
                {{ summaryStats.totalWorkHours }}小时
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-label">总上岗时长</div>
              <div class="stat-value">
                {{ summaryStats.totalOnDutyHours }}小时
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-label">总加班时长</div>
              <div class="stat-value">
                {{ summaryStats.totalOvertimeHours }}小时
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-label">平均上岗率</div>
              <div class="stat-value">
                <span class="rate-text">
                  {{ summaryStats.avgOnDutyRate }}%
                </span>
              </div>
            </div>
          </div>

          <!-- 数据表格区域 -->
          <div class="table-section">
            <div
              v-loading="loading"
              element-loading-text="正在加载数据..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 24, 75, 0.8)"
              class="table-container"
            >
              <el-table
                :data="currentData"
                stripe
                style="width: 100%"
                class="efficiency-table"
              >
                <el-table-column
                  prop="area_name"
                  label="区域"
                  width="140"
                  align="center"
                />
                <el-table-column
                  prop="total_work_hours"
                  label="勤务安排(小时)"
                  width="190"
                  align="center"
                />
                <el-table-column
                  prop="on_duty_hours"
                  label="上岗时长(小时)"
                  width="190"
                  align="center"
                />
                <el-table-column
                  prop="overtime_hours"
                  label="加班时长(小时)"
                  width="190"
                  align="center"
                />
                <el-table-column label="在岗率" width="110" align="center">
                  <template #default="{ row }">
                    <span class="rate-text">
                      {{ calculateOnDutyRate(row).toFixed(1) }}%
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="总在岗率" width="130" align="center">
                  <template #default="{ row }">
                    <span class="rate-text">
                      {{ calculateTotalOnDutyRate(row).toFixed(1) }}%
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 底部装饰 -->
        <dv-decoration-3 class="dialog-footer" />
      </dv-border-box-12>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { Close } from "@element-plus/icons-vue";
import AreaCascader from "@/components/AreaCascader.vue";
import TimeRangeSelector, {
  type TimeRangeType
} from "@/components/TimeRangeSelector.vue";
import dayjs from "dayjs";
import { useAppStoreHook } from "@/store/modules/app";
import { useUserStoreHook } from "@/store/modules/user";
import {
  getAttendanceAndWorkTimeByday,
  getAttendanceAndWorkTimeByMonth,
  getAttendanceAndWorkTimeByYears
} from "@/api/largeScreen";

// Props 定义
interface EfficiencyData {
  area_name: string; // 区域名称
  total_work_hours: string; // 总工作小时数
  on_duty_hours: string; // 在岗小时数
  overtime_hours: string; // 加班小时数
  attended_count: number; // 实际出勤人数
  total_scheduled: number; // 排班人数
}

interface Props {
  visible: boolean;
  data?: EfficiencyData[];
  selectedDate?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: () => [],
  selectedDate: ""
});

// Emits 定义
const emit = defineEmits<{
  "update:visible": [value: boolean];
  close: [];
}>();

// 数据状态
const selectedArea = ref<number[]>([]);
const selectedTimeRange = ref<TimeRangeType>("month"); // 默认显示月
const currentDate = ref(props.selectedDate || dayjs().format("YYYY-MM-DD"));
const currentMonth = ref(dayjs().format("YYYY-MM"));
const currentYear = ref(dayjs().format("YYYY"));
const loading = ref(false);
const currentData = ref<EfficiencyData[]>([]);

// TimeRangeSelector 组件引用
const timeRangeSelectorRef = ref();

// 显示时间计算属性
const displayTime = computed(() => {
  switch (selectedTimeRange.value) {
    case "month":
      return currentMonth.value;
    case "year":
      return currentYear.value;
    default:
      return currentDate.value;
  }
});

// 根据ID查找节点
const findNodeById = (nodes: any[], id: number): any => {
  if (!nodes || !id) return undefined;

  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.childList) {
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }
  }
  return undefined;
};

// 获取地址参数（参考IllegalDetailDialog的实现）
const getAreaParams = (areaIds: number[]) => {
  const userStore = useUserStoreHook();
  const params = {
    city: userStore.userInfo.city
  } as any;

  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return params;

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }

  return params;
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    // 构建地址参数：使用节点树选择的地址或默认大屏区域
    const areaParams = selectedArea.value?.length
      ? getAreaParams(selectedArea.value)
      : useAppStoreHook().getLargeScreenArea;
    let response;

    switch (selectedTimeRange.value) {
      case "month":
        response = await getAttendanceAndWorkTimeByMonth({
          date: currentMonth.value,
          ...areaParams
        });
        break;
      case "year":
        response = await getAttendanceAndWorkTimeByYears({
          date: currentYear.value,
          ...areaParams
        });
        break;
      default:
        response = await getAttendanceAndWorkTimeByday({
          date: currentDate.value,
          ...areaParams
        });
        break;
    }

    if (response.code === 200 && response.data) {
      // 根据返回数据结构处理数据
      currentData.value = Array.isArray(response.data)
        ? response.data
        : response.data.data || [response.data];
    } else {
      currentData.value = [];
    }
  } catch (error) {
    currentData.value = [];
  } finally {
    loading.value = false;
  }
};

// 关闭弹窗
const closeDialog = () => {
  emit("update:visible", false);
  emit("close");
};

// 区域变化处理
const handleAreaChange = (area: number[]) => {
  selectedArea.value = area;
  fetchData();
};

// 时间范围变化处理
const handleTimeRangeChange = (timeRange: TimeRangeType) => {
  selectedTimeRange.value = timeRange;
  fetchData();
};

// 日期变化处理
const handleDateChange = (date: string) => {
  currentDate.value = date;
  if (selectedTimeRange.value === "day") {
    fetchData();
  }
};

// 月份变化处理
const handleMonthChange = (month: string) => {
  currentMonth.value = month;
  if (selectedTimeRange.value === "month") {
    fetchData();
  }
};

// 年份变化处理
const handleYearChange = (year: string) => {
  currentYear.value = year;
  if (selectedTimeRange.value === "year") {
    fetchData();
  }
};

// 计算汇总统计数据
const summaryStats = computed(() => {
  if (!currentData.value.length) {
    return {
      totalWorkHours: "0.0",
      totalOnDutyHours: "0.0",
      totalOvertimeHours: "0.0",
      avgOnDutyRate: 0
    };
  }

  const totalWorkHours = currentData.value.reduce(
    (sum, item) => sum + parseFloat(item.total_work_hours || "0"),
    0
  );
  const totalOnDutyHours = currentData.value.reduce(
    (sum, item) => sum + parseFloat(item.on_duty_hours || "0"),
    0
  );
  const totalOvertimeHours = currentData.value.reduce(
    (sum, item) => sum + parseFloat(item.overtime_hours || "0"),
    0
  );

  // 计算平均在岗率
  const avgOnDutyRate =
    totalWorkHours > 0 ? (totalOnDutyHours / totalWorkHours) * 100 : 0;

  return {
    totalWorkHours: totalWorkHours.toFixed(1),
    totalOnDutyHours: totalOnDutyHours.toFixed(1),
    totalOvertimeHours: totalOvertimeHours.toFixed(1),
    avgOnDutyRate: Math.round(avgOnDutyRate)
  };
});

// 计算在岗率
const calculateOnDutyRate = (row: EfficiencyData) => {
  if (parseFloat(row.total_work_hours || "0") === 0) {
    return 0;
  }
  return (
    (parseFloat(row.on_duty_hours || "0") /
      parseFloat(row.total_work_hours || "0")) *
    100
  );
};

// 计算总在岗率（包含加班时长）
const calculateTotalOnDutyRate = (row: EfficiencyData) => {
  if (parseFloat(row.total_work_hours || "0") === 0) {
    return 0;
  }
  const totalOnDutyHours =
    parseFloat(row.on_duty_hours || "0") +
    parseFloat(row.overtime_hours || "0");
  return (totalOnDutyHours / parseFloat(row.total_work_hours || "0")) * 100;
};

// 计算出勤率
const calculateAttendanceRate = (row: EfficiencyData) => {
  if (row.total_scheduled === 0) {
    return 0;
  }
  return (row.attended_count / row.total_scheduled) * 100;
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      // 弹窗打开时，重置时间选择器状态
      if (timeRangeSelectorRef.value) {
        timeRangeSelectorRef.value.resetTimeSelector();
      }
      // 重置本地状态
      selectedTimeRange.value = "month";
      currentDate.value = props.selectedDate || dayjs().format("YYYY-MM-DD");
      currentMonth.value = dayjs().format("YYYY-MM");
      currentYear.value = dayjs().format("YYYY");
      fetchData();
    }
  }
);

// 监听区域变化
watch(
  () => useAppStoreHook().largeScreenArea,
  () => {
    if (props.visible && useAppStoreHook().largeScreenArea) {
      fetchData();
    }
  }
);
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
// 弹窗容器
.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  padding: 20px;
}

// 弹窗内容
.dialog-content {
  position: absolute; // 更爱为绝对定位，保证居中
  left: 50%; // 居中定位
  top: 50%; // 剧中定位
  transform: translate(-50%, -50%); // 居中定位，不进行缩放
  width: 1792px; // 根据需求调整宽度，设计定稿宽度2560px，根据弹窗大小调整宽度，小于2560
  height: 1024px; // 根据需求调整宽度，设计定稿宽度1271px，根据弹窗大小调整宽度，小于1271
  // animation: zoomIn 0.3s ease-out; // 取消窗口动画
}

// 边框容器
.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

// 标题栏
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-text {
  @extend .fs-dialog-title; // 弹窗标题字体大小
  font-weight: 600;
  color: #fff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

// 关闭按钮样式现在使用统一的 .large-screen-close-btn 类

/* 控制栏样式 */
.control-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid rgb(64 158 255 / 20%);

  .dialog-area-selector,
  .header-right {
    display: flex;
    align-items: center;
  }
}

// 内容区域
.dialog-body {
  height: calc(100% - 160px); /* 调整高度以适应新增的控制栏 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 统计卡片网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.stat-card {
  padding: 20px;
  text-align: center;
  background: rgb(0 24 75 / 30%);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &:hover {
    border-color: rgb(64 158 255 / 40%);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.2);
    transform: translateY(-2px);
  }
}

.stat-label {
  @extend .fs-attendance-detail-card-label; // 统计卡片标签字体大小
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.stat-value {
  @extend .fs-attendance-detail-card-label; // 统计卡片数值字体大小
  font-weight: bold;
  color: #409eff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

// 表格区域
.table-section {
  height: 640px; // 根据弹窗大小调整
  max-height: 640px;
  display: flex;
  flex-direction: column;
  overflow: visible;
  align-items: center;
}

.table-header {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  background: rgb(2 48 115 / 80%); // 使用与表头一致的深蓝色背景
  border: 1px solid rgba(64, 158, 255, 0.4); // 适中的边框颜色
  border-radius: 8px 8px 0 0;

  .table-title {
    @extend .fs-dialog-title; // 表格标题字体大小
    font-weight: 600;
    color: #fff; // 纯白色字体，与表头一致
    margin: 0;
  }
}

.table-container {
  flex: 1;
  border: 1px solid rgba(64, 158, 255, 0.4); // 适中的边框颜色
  border-top: none;
  border-radius: 0 0 8px 8px;
  overflow: auto; // 容器本身可以滚动
  max-height: 600px; // 根据弹窗大小调整合适的最大高度

  // 隐藏滚动条，但保持滚动功能
  scrollbar-width: none; // Firefox 隐藏滚动条
  -ms-overflow-style: none; // IE 隐藏滚动条

  &::-webkit-scrollbar {
    display: none; // Chrome, Safari, Edge 隐藏滚动条
  }

  :deep(.el-table) {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);

    .el-table__inner-wrapper {
      &::before {
        background: none;
      }
    }

    .el-table__body-wrapper {
      background: transparent;
    }

    // 表头样式 - 参考上岗情况分析表格
    .el-table__header-wrapper {
      th {
        position: relative;
        background: rgb(
          2 48 115 / 80%
        ) !important; // 使用上岗情况分析表格的深蓝色背景
        color: #fff !important; // 纯白色字体
        @extend .fs-efficiency-table; //表头字体大小
        font-weight: 600;
        padding: 12px 0; // 表格表头内边距
        border: none !important;

        // 添加底部渐变线条
        &::after {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 2px;
          content: "";
          background: linear-gradient(
            90deg,
            rgb(64 158 255 / 20%) 0%,
            rgb(64 158 255 / 80%) 50%,
            rgb(64 158 255 / 20%) 100%
          );
        }
      }
    }

    // 表格行和单元格样式
    .el-table__row {
      background: transparent;

      td {
        background: rgba(0, 24, 75, 0.2) !important;
        color: rgba(255, 255, 255, 0.7) !important; // 保持原来的字体亮度
        @extend .fs-efficiency-table; //表格内容字体大小
        border-right: 1px solid rgba(64, 158, 255, 0.2) !important; // 添加右边框
        border-bottom: 1px solid rgba(64, 158, 255, 0.2) !important; // 添加底边框，增强可见性

        &:last-child {
          border-right: none !important; // 最后一列不显示右边框
        }
      }

      &:hover td {
        background: rgba(64, 158, 255, 0.2) !important;
        border-bottom-color: rgba(
          64,
          158,
          255,
          0.3
        ) !important; // 悬停时适度增强边框颜色
      }

      &.el-table__row--striped td {
        background: rgba(0, 24, 75, 0.2) !important;
      }

      &:last-child td {
        border-bottom: none !important; // 最后一行不显示底边框
      }
    }

    .el-table__empty-block {
      background: rgba(0, 24, 75, 0.3);
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .rate-text {
    color: #67c23a;
    font-weight: 600;
  }
}

// 底部装饰
.dialog-footer {
  width: 100%;
  height: 4px;
  margin-top: 16px;
  flex-shrink: 0;
  opacity: 0.6; // 降低透明度
  :deep(svg) {
    rect {
      fill: rgba(64, 158, 255, 0.3) !important; // 修改填充颜色
    }
    polyline {
      stroke: rgba(64, 158, 255, 0.3) !important; // 修改线条颜色
    }
  }
}

// 滚动条样式
.dialog-body,
.table-container {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
}

// 自定义loading样式
:deep(.el-loading-mask) {
  .el-loading-text {
    color: #00eaff !important;
    @extend .fs-efficiency-table; // 加载文本字体大小
    font-weight: 500 !important;
    text-shadow: 0 0 10px rgba(0, 234, 255, 0.5) !important;
  }

  .el-loading-spinner {
    .el-icon-loading {
      color: #00eaff !important;
      @extend .fs-efficiency-table; // 加载图标字体大小
      filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.6)) !important;
    }
  }
}
</style>
