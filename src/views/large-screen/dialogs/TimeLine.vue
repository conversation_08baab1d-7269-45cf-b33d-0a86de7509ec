<template>
  <div class="time-line-container">
    <!-- 无数据时显示提示 -->
    <div v-if="!hasData" class="no-data-message">暂无班次数据</div>

    <div v-else class="time-line-wrapper">
      <!-- 时间段标题 -->
      <div v-if="props.timeOfDay !== 'all'" class="time-period-title">
        {{ props.timeOfDay === "morning" ? "上午" : "下午" }} ({{
          formattedTimeRange
        }})
      </div>

      <!-- 时间标记 - 放在上方 -->
      <div class="time-marks">
        <div v-for="mark in timeMarks" :key="mark" class="time-mark">
          {{ formatTime(mark) }}
        </div>
      </div>

      <!-- 时间轴内容 -->
      <div class="time-line-content">
        <!-- 时间段 -->
        <div
          v-for="(segment, index) in filteredTimeSegments"
          :key="index"
          class="time-segment"
          :class="getSegmentClass(segment)"
          :style="getSegmentStyle(segment)"
          @click="showSegmentTooltip($event, segment)"
        />
      </div>
    </div>
  </div>

  <!-- 使用teleport将tooltip传送到body -->
  <teleport to="body">
    <div
      v-if="tooltipVisible && activeSegment"
      ref="tooltip"
      class="timeline-global-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-header">
        <div class="tooltip-title">时间段详情</div>
        <div class="tooltip-close" @click="hideTooltip">×</div>
      </div>

      <div class="tooltip-content">
        <!-- 1. 时间信息 -->
        <div class="info-section time-section">
          <div class="section-title">
            <i class="section-icon">⏰</i>
            时间
          </div>
          <div class="section-content">
            {{ formatTimeWithoutDate(activeSegment.startTime) }} -
            {{ formatTimeWithoutDate(activeSegment.endTime) }}
          </div>
        </div>

        <!-- 2. 时长信息 -->
        <div class="info-section duration-section">
          <div class="section-title">
            <i class="section-icon">⏱️</i>
            时长
          </div>
          <div class="section-content">
            {{ getSegmentDuration(activeSegment) }}
          </div>
        </div>

        <!-- 3. 状态信息 -->
        <div class="info-section status-section">
          <div class="section-title">
            <i class="section-icon">📊</i>
            状态
          </div>
          <div class="section-content">
            <span
              class="status-badge"
              :class="`status-${activeSegment.status}`"
            >
              {{ getStatusText(activeSegment.status) }}
            </span>
            <div class="status-stats">
              <span class="stat-item">
                总人数: <strong>{{ getTotalStaffCount(activeSegment) }}</strong>
              </span>
              <span
                v-if="
                  activeSegment.status === 'leave' ||
                  getLeaveStaffCount(activeSegment) > 0
                "
                class="stat-item leave-stat"
              >
                脱岗人数:
                <strong>{{ getLeaveStaffCount(activeSegment) }}</strong>
              </span>
            </div>
          </div>
        </div>

        <!-- 4. 人员信息 -->
        <div class="info-section staff-section">
          <div class="section-title">
            <i class="section-icon">👥</i>
            人员信息
          </div>
          <div class="section-content">
            <!-- 显示所有相关人员 -->
            <div
              v-if="getSegmentStaffList(activeSegment).length > 0"
              class="staff-list"
            >
              <div
                v-for="staff in getSegmentStaffList(activeSegment)"
                :key="staff.name"
                class="staff-item"
              >
                <div class="staff-basic">
                  <span class="staff-name">{{ staff.name }}</span>
                </div>
              </div>
            </div>
            <div v-else class="no-staff-info">当前时间段无人员信息</div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  nextTick,
  onMounted,
  onBeforeUnmount,
  watch
} from "vue";

interface LeavePostDetail {
  startTime: string;
  endTime: string;
  duration: number;
  status: string;
  staffName?: string;
}

interface Shift {
  scheduleId?: number;
  shiftName: string;
  startTime: string;
  endTime: string;
  status: string;
  statusText?: string;
  leavePostDetails?: LeavePostDetail[];
  abnormalRecords?: LeavePostDetail[];
  staffName?: string;
  staffId?: number;
  period?: string; // 时段标识：AM 或 PM
}

// 定义时间段接口
interface TimeSegment {
  startTime: string;
  endTime: string;
  status: "normal" | "leave" | "future" | "fault"; // 添加设备故障状态
  sourceShift: Shift;
  leaveRecords?: LeavePostDetail[];
  isPartialShift?: boolean;
}

const props = defineProps<{
  shifts: Shift[];
  viewDate?: string; // 可选的查看日期，格式为 'YYYY-MM-DD'
  timeOfDay?: "morning" | "afternoon" | "all"; // 时间段：上午/下午/全天
  deviceFaultRecords?: Array<{
    equipmentNumber: string;
    ip: string;
    startTime: string;
    endTime: string;
    duration: string;
    description: string;
  }>; // 设备故障记录
  freezeData?: boolean; // 是否冻结数据，防止外部刷新影响
}>();

// 数据快照 - 在组件初始化时保存，避免外部刷新影响
const dataSnapshot = ref<{
  shifts: Shift[];
  deviceFaultRecords?: Array<{
    equipmentNumber: string;
    ip: string;
    startTime: string;
    endTime: string;
    duration: string;
    description: string;
  }>;
  viewDate?: string;
  timeOfDay?: "morning" | "afternoon" | "all";
}>({
  shifts: [],
  deviceFaultRecords: [],
  viewDate: props.viewDate,
  timeOfDay: props.timeOfDay || "all"
});

// 初始化数据快照
const initializeDataSnapshot = () => {
  dataSnapshot.value = {
    shifts: JSON.parse(JSON.stringify(props.shifts)), // 深拷贝
    deviceFaultRecords: props.deviceFaultRecords
      ? JSON.parse(JSON.stringify(props.deviceFaultRecords))
      : [],
    viewDate: props.viewDate,
    timeOfDay: props.timeOfDay || "all"
  };
};

// 获取当前使用的数据（快照或实时数据）
const currentData = computed(() => {
  if (props.freezeData) {
    return dataSnapshot.value;
  }
  return {
    shifts: props.shifts,
    deviceFaultRecords: props.deviceFaultRecords,
    viewDate: props.viewDate,
    timeOfDay: props.timeOfDay || "all"
  };
});

// 检查日期是否有效
const isValidDate = (date: Date): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

// 安全解析日期，支持多种时间格式
const parseDateTime = (dateTimeStr: string): Date | null => {
  try {
    // 特殊情况处理：如果是"至今"或类似表达，返回当前时间
    if (
      dateTimeStr === "至今" ||
      dateTimeStr === "至现在" ||
      dateTimeStr === "现在" ||
      dateTimeStr === "now" ||
      dateTimeStr === "present"
    ) {
      return new Date();
    }

    // 如果是完整的日期时间字符串（如 "2025-04-27 07:40:00"）
    if (dateTimeStr.includes("-") && dateTimeStr.includes(":")) {
      const date = new Date(dateTimeStr);
      if (isValidDate(date)) {
        return date;
      }
    }

    // 尝试检测是否为只有时间的格式 (HH:mm 或 HH:mm:ss)
    const timeRegex = /^(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?$/;
    const match = dateTimeStr.match(timeRegex);

    if (match) {
      // 是时间格式，添加今天的日期
      const today = new Date();
      const hours = parseInt(match[1]);
      const minutes = parseInt(match[2]);
      const seconds = match[3] ? parseInt(match[3]) : 0;

      // 创建今天的日期加上指定的时间
      const date = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        hours,
        minutes,
        seconds
      );
      return date;
    }

    // 首先尝试原样解析
    let date = new Date(dateTimeStr);

    // 检查是否为有效日期
    if (isValidDate(date)) {
      return date;
    }

    // 如果所有尝试都失败，记录警告日志
    console.warn(`无法解析的时间格式: "${dateTimeStr}"`);
    return null;
  } catch (error) {
    console.error(`解析时间出错: "${dateTimeStr}"`, error);
    return null;
  }
};

// 格式化时间
const formatTime = (hour: number) => {
  const h = Math.floor(hour);
  const m = Math.round((hour - h) * 60);
  return `${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}`;
};

// 格式化时间（不带日期）
const formatTimeWithoutDate = (dateStr: string) => {
  try {
    // 特殊情况处理：如果是"至今"或类似表达，直接返回原文本
    if (dateStr === "至今") {
      return dateStr;
    }

    const date = parseDateTime(dateStr);
    if (!date) {
      return "--:--"; // 返回占位符
    }
    return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  } catch (error) {
    return "--:--"; // 出错时返回占位符
  }
};

const tooltipVisible = ref(false);
const keepTooltipVisible = ref(false);
const tooltipStyle = ref({
  left: "0px",
  top: "0px"
});

// 获取当前时间 - 使用响应式引用确保一致性
const currentTime = ref(new Date());

// 更新当前时间的函数
const updateCurrentTime = () => {
  currentTime.value = new Date();
};

// 在组件挂载时设置定时器（可选，用于实时更新）
onMounted(() => {
  // 立即初始化数据快照
  initializeDataSnapshot();

  // 立即更新一次时间，确保初始状态正确
  updateCurrentTime();

  // 每分钟更新一次当前时间（降低频率以提高性能）
  const timer = setInterval(() => {
    updateCurrentTime();
  }, 60000); // 60秒更新一次

  // 延迟添加全局点击事件，避免与组件内部点击冲突
  setTimeout(() => {
    document.addEventListener("click", handleDocumentClick);
  }, 100);

  // 在组件卸载时清除定时器
  onBeforeUnmount(() => {
    clearInterval(timer);
  });
});

// 暴露刷新函数供外部调用
defineExpose({
  refreshTime: updateCurrentTime,
  refreshData: initializeDataSnapshot, // 刷新数据快照
  enableRealTimeData: () => {
    /* 可以在需要时启用实时数据 */
  }
});

// 时间比较专用工具函数
const compareTimeOnly = (time1: Date | null, time2: Date | null): number => {
  if (!time1 || !time2) return 0;

  // 只比较时分秒，忽略年月日
  const t1Minutes = time1.getHours() * 60 + time1.getMinutes();
  const t2Minutes = time2.getHours() * 60 + time2.getMinutes();

  return t1Minutes - t2Minutes;
};

// 处理时间段 - 优化算法，减少分钟级别计算
const timeSegments = computed(() => {
  const data = currentData.value;
  if (!data.shifts || data.shifts.length === 0) {
    return [];
  }

  // 使用缓存避免重复计算
  // const cacheKey = `${JSON.stringify(data.shifts)}-${data.viewDate}-${data.timeOfDay}`;

  // 判断基准日期
  let referenceDate: Date | null = null;
  try {
    const firstShift = data.shifts[0];
    const firstLeaveDetail =
      firstShift.leavePostDetails?.[0] || firstShift.abnormalRecords?.[0];

    if (firstLeaveDetail && firstLeaveDetail.startTime.includes("-")) {
      const fullDate = new Date(firstLeaveDetail.startTime);
      if (isValidDate(fullDate)) {
        referenceDate = fullDate;
      }
    }

    if (!referenceDate) {
      if (data.viewDate) {
        referenceDate = new Date(data.viewDate);
      } else {
        referenceDate = new Date();
      }
    }
  } catch (error) {
    referenceDate = new Date();
  }

  const refYear = referenceDate.getFullYear();
  const refMonth = referenceDate.getMonth();
  const refDay = referenceDate.getDate();
  const referenceBaseDate = new Date(refYear, refMonth, refDay, 0, 0, 0);
  const realNow = currentTime.value;

  // 找出工作时间范围
  let minStartTime: Date | null = null;
  let maxEndTime: Date | null = null;

  data.shifts.forEach(shift => {
    try {
      const shiftStart = parseDateTime(shift.startTime);
      const shiftEnd = parseDateTime(shift.endTime);

      if (shiftStart && shiftEnd) {
        if (!minStartTime || compareTimeOnly(shiftStart, minStartTime) < 0) {
          minStartTime = shiftStart;
        }
        if (!maxEndTime || compareTimeOnly(shiftEnd, maxEndTime) > 0) {
          maxEndTime = shiftEnd;
        }
      }
    } catch (error) {
      // 处理错误
    }
  });

  if (!minStartTime || !maxEndTime) {
    return [];
  }

  // 优化：使用15分钟间隔而不是1分钟间隔
  const segments: TimeSegment[] = [];
  const startMinutes = minStartTime.getHours() * 60 + minStartTime.getMinutes();
  const endMinutes = maxEndTime.getHours() * 60 + maxEndTime.getMinutes();

  // 使用5分钟间隔
  const interval = 5; // 5分钟间隔

  for (let minute = startMinutes; minute < endMinutes; minute += interval) {
    const segmentStartHour = Math.floor(minute / 60);
    const segmentStartMin = minute % 60;
    const segmentEndHour = Math.floor((minute + interval) / 60);
    const segmentEndMin = (minute + interval) % 60;

    const segmentStart = new Date(referenceBaseDate);
    segmentStart.setHours(segmentStartHour, segmentStartMin, 0, 0);

    const segmentEnd = new Date(referenceBaseDate);
    segmentEnd.setHours(segmentEndHour, segmentEndMin, 0, 0);

    // 使用段中点进行状态判断
    const segmentMiddle = new Date(referenceBaseDate);
    segmentMiddle.setHours(
      segmentStartHour,
      segmentStartMin + Math.floor(interval / 2),
      0,
      0
    );

    // 优化状态判断逻辑
    let segmentStatus: "normal" | "leave" | "future" | "fault" = "normal";

    // 检查是否为未来时间段
    const today = currentTime.value;
    const isReferenceToday =
      referenceDate.getFullYear() === today.getFullYear() &&
      referenceDate.getMonth() === today.getMonth() &&
      referenceDate.getDate() === today.getDate();

    if (isReferenceToday && segmentMiddle > realNow) {
      segmentStatus = "future";
    } else {
      // 预处理设备故障时间段
      const faultTimeRanges = (data.deviceFaultRecords || [])
        .map(fault => {
          const faultStart = parseDateTime(fault.startTime);
          const faultEnd = parseDateTime(fault.endTime);
          if (faultStart && faultEnd) {
            return {
              start: faultStart.getHours() * 60 + faultStart.getMinutes(),
              end: faultEnd.getHours() * 60 + faultEnd.getMinutes()
            };
          }
          return null;
        })
        .filter(Boolean);

      // 检查设备故障状态（优先级最高）
      const hasFault = faultTimeRanges.some(
        range => range && minute >= range.start && minute < range.end
      );

      if (hasFault) {
        segmentStatus = "fault";
      } else {
        // 优化脱岗状态检查（排除请假员工，支持全请假为黄色）
        let hasActiveStaff = false; // 有非请假员工
        let allStaffLeaving = true; // 所有非请假员工都脱岗
        let allStaffOnLeave = true; // 所有员工都请假

        for (const shift of data.shifts) {
          const shiftStart = parseDateTime(shift.startTime);
          const shiftEnd = parseDateTime(shift.endTime);

          if (shiftStart && shiftEnd) {
            const shiftStartMin =
              shiftStart.getHours() * 60 + shiftStart.getMinutes();
            const shiftEndMin =
              shiftEnd.getHours() * 60 + shiftEnd.getMinutes();

            // 检查此员工是否应该在此时间段内工作
            if (minute >= shiftStartMin && minute < shiftEndMin) {
              // 跳过请假员工
              if (
                shift.status === "请假" ||
                shift.statusText?.includes("请假")
              ) {
                continue;
              }
              allStaffOnLeave = false; // 有非请假员工
              hasActiveStaff = true;

              // 检查此员工是否在此时间点脱岗
              const allLeaveRecords = [
                ...(shift.leavePostDetails || []),
                ...(shift.abnormalRecords || [])
              ];

              let isLeaving = false;
              for (const leave of allLeaveRecords) {
                const leaveStart = parseDateTime(leave.startTime);
                const leaveEnd = parseDateTime(leave.endTime);

                if (leaveStart && leaveEnd) {
                  const leaveStartMin =
                    leaveStart.getHours() * 60 + leaveStart.getMinutes();
                  const leaveEndMin =
                    leaveEnd.getHours() * 60 + leaveEnd.getMinutes();

                  if (minute >= leaveStartMin && minute <= leaveEndMin) {
                    isLeaving = true;
                    break;
                  }
                }
              }

              if (!isLeaving) {
                allStaffLeaving = false;
                break; // 只要有一个非请假员工不是脱岗就不是全脱岗
              }
            }
          }
        }

        // 设置状态
        if (allStaffOnLeave) {
          segmentStatus = "leave"; // 全请假也显示黄色
        } else if (hasActiveStaff && allStaffLeaving) {
          segmentStatus = "leave";
        }
        // 其余情况默认 normal
      }
    }

    // 找到相关班次（简化查找）
    const sourceShift = data.shifts.find(shift => {
      const shiftStart = parseDateTime(shift.startTime);
      const shiftEnd = parseDateTime(shift.endTime);

      if (shiftStart && shiftEnd) {
        const shiftStartMin =
          shiftStart.getHours() * 60 + shiftStart.getMinutes();
        const shiftEndMin = shiftEnd.getHours() * 60 + shiftEnd.getMinutes();
        return minute >= shiftStartMin && minute < shiftEndMin;
      }
      return false;
    });

    if (sourceShift) {
      segments.push({
        startTime: segmentStart.toISOString(),
        endTime: segmentEnd.toISOString(),
        status: segmentStatus,
        sourceShift,
        leaveRecords: [],
        isPartialShift: true
      });
    }
  }

  // 合并相邻的相同状态时间段（保持原有逻辑）
  const mergedSegments: TimeSegment[] = [];
  let currentSegment: TimeSegment | null = null;

  segments.forEach(segment => {
    if (!currentSegment) {
      currentSegment = { ...segment };
    } else if (
      currentSegment.status === segment.status &&
      new Date(currentSegment.endTime).getTime() ===
        new Date(segment.startTime).getTime()
    ) {
      // 合并相同状态的相邻时间段
      currentSegment.endTime = segment.endTime;
    } else {
      // 状态不同或不相邻，保存当前段并开始新段
      mergedSegments.push(currentSegment);
      currentSegment = { ...segment };
    }
  });

  if (currentSegment) {
    mergedSegments.push(currentSegment);
  }

  return mergedSegments;
});

// 获取时间段样式
const getSegmentStyle = (segment: TimeSegment) => {
  const startTime = new Date(segment.startTime);
  const endTime = new Date(segment.endTime);

  const startHour = startTime.getHours() + startTime.getMinutes() / 60;
  const endHour = endTime.getHours() + endTime.getMinutes() / 60;

  const { start, end } = timeRange.value;
  const range = end - start;

  const startPosition = ((startHour - start) / range) * 100;
  const endPosition = ((endHour - start) / range) * 100;
  const width = endPosition - startPosition;

  // 确保宽度不小于1%，便于用户点击
  const safeWidth = Math.max(width, 1);

  return {
    left: `${startPosition}%`,
    width: `${safeWidth}%`
  };
};

// 获取时间段的类名
const getSegmentClass = (segment: TimeSegment) => {
  return {
    "segment-normal": segment.status === "normal",
    "segment-leave": segment.status === "leave",
    "segment-fault": segment.status === "fault",
    "segment-future": segment.status === "future"
  };
};

// 防抖定时器
let tooltipDebounceTimer: number | null = null;

// 显示时间段提示 - 改为点击事件
const showSegmentTooltip = (event: MouseEvent, segment: TimeSegment) => {
  event.stopPropagation(); // 阻止事件冒泡
  event.preventDefault(); // 阻止默认行为

  // 清除之前的防抖定时器
  if (tooltipDebounceTimer) {
    clearTimeout(tooltipDebounceTimer);
    tooltipDebounceTimer = null;
  }

  // 如果点击了同一个segment，且tooltip已经显示，则隐藏tooltip
  if (activeSegment.value === segment && tooltipVisible.value) {
    hideTooltip();
    return;
  }

  // 简化显示逻辑，减少异步操作
  // 先隐藏现有的tooltip
  if (tooltipVisible.value) {
    tooltipVisible.value = false;
    activeSegment.value = null;
  }

  // 立即显示新的tooltip
  tooltipVisible.value = true;
  activeSegment.value = segment;

  // 计算tooltip位置
  nextTick(() => {
    const tooltip = document.querySelector(
      ".timeline-global-tooltip"
    ) as HTMLElement;
    if (!tooltip) return;

    const tooltipWidth = tooltip.offsetWidth;
    const tooltipHeight = tooltip.offsetHeight;

    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 默认位置 - 优先显示在鼠标右侧
    let left = event.clientX + 15;
    let top = event.clientY - 20;

    // 检查是否会超出右边界
    if (left + tooltipWidth > viewportWidth - 10) {
      left = event.clientX - tooltipWidth - 15;
    }

    // 检查是否会超出下边界
    if (top + tooltipHeight > viewportHeight - 10) {
      // 优先尝试显示在点击位置上方
      top = Math.max(10, event.clientY - tooltipHeight - 15);
    }

    // 确保tooltip不会显示在顶部之外
    top = Math.max(10, top);

    // 设置相对于视口的固定位置
    tooltipStyle.value = {
      left: `${left}px`,
      top: `${top}px`
    };
  });
};

// 隐藏tooltip
const hideTooltip = () => {
  tooltipVisible.value = false;
  activeSegment.value = null;

  // 重置tooltip样式
  tooltipStyle.value = {
    left: "0px",
    top: "0px"
  };
};

// 添加全局点击事件来隐藏tooltip
const handleDocumentClick = (event: MouseEvent) => {
  // 检查点击是否在tooltip外部和时间段外部
  const tooltip = document.querySelector(".timeline-global-tooltip");
  const timelineContainer = document.querySelector(".time-line-container");

  if (
    tooltipVisible.value &&
    tooltip &&
    !tooltip.contains(event.target as Node) &&
    timelineContainer &&
    !timelineContainer.contains(event.target as Node)
  ) {
    hideTooltip();
  }
};

// 在组件卸载前移除全局点击事件
onBeforeUnmount(() => {
  // 清理事件监听器
  document.removeEventListener("click", handleDocumentClick);

  // 清理防抖定时器
  if (tooltipDebounceTimer) {
    clearTimeout(tooltipDebounceTimer);
    tooltipDebounceTimer = null;
  }

  // 隐藏tooltip
  hideTooltip();
});

// 活动的时间段
const activeSegment = ref<TimeSegment | null>(null);

// 是否有数据
const hasData = computed(() => {
  return timeSegments.value.length > 0;
});

// 根据 timeOfDay 属性过滤时间段
const filteredTimeSegments = computed(() => {
  const data = currentData.value;
  // 如果是全天模式，返回所有时间段
  if (data.timeOfDay === "all") {
    return timeSegments.value;
  }

  // 根据 period 属性过滤班次
  return timeSegments.value.filter(segment => {
    // 如果 segment 的源班次有 period 属性，则按此属性过滤
    const period = segment.sourceShift?.period;

    if (period) {
      // 上午时间段只显示 AM 时段的数据
      if (data.timeOfDay === "morning" && period === "AM") {
        return true;
      }
      // 下午时间段只显示 PM 时段的数据
      if (data.timeOfDay === "afternoon" && period === "PM") {
        return true;
      }
      return false;
    }

    // 如果没有 period 属性，则根据时间范围过滤
    const segmentTime = new Date(segment.startTime);
    const hours = segmentTime.getHours();

    if (data.timeOfDay === "morning") {
      return hours >= 0 && hours < 12; // 上午: 0:00-11:59
    } else if (data.timeOfDay === "afternoon") {
      return hours >= 12 && hours < 24; // 下午: 12:00-23:59
    }

    return true;
  });
});

// 安全获取时间标记 - 确保时间范围计算不会出错
const timeRange = computed(() => {
  try {
    const data = currentData.value;
    if (!data.shifts || !data.shifts.length) {
      return { start: 0, end: 24 };
    }

    // 根据 timeOfDay 属性筛选当前时段的班次
    const currentTimeSegments = data.shifts.filter(shift => {
      if (data.timeOfDay === "all") return true;

      // 优先使用 period 属性判断
      if (shift.period) {
        return (
          (data.timeOfDay === "morning" && shift.period === "AM") ||
          (data.timeOfDay === "afternoon" && shift.period === "PM")
        );
      }

      // 根据班次名称判断（如 "早"、"晚" 等）
      if (shift.shiftName) {
        const name = shift.shiftName.toLowerCase();
        if (
          data.timeOfDay === "morning" &&
          (name.includes("早") || name.includes("上午"))
        ) {
          return true;
        }
        if (
          data.timeOfDay === "afternoon" &&
          (name.includes("晚") || name.includes("下午"))
        ) {
          return true;
        }
      }

      // 如果没有 period 属性，根据时间判断
      try {
        const shiftStart = parseDateTime(shift.startTime);
        if (shiftStart) {
          const hours = shiftStart.getHours();

          if (data.timeOfDay === "morning") {
            return hours >= 0 && hours < 12;
          } else if (data.timeOfDay === "afternoon") {
            return hours >= 12 && hours < 24;
          }
        } else {
          console.warn(`无法解析班次时间: ${shift.startTime}`);
        }
      } catch (error) {
        console.error("解析班次时间出错:", error);
      }

      return false; // 如果无法确定，则默认不包含
    });

    // 如果当前时段没有班次数据，使用默认值
    if (currentTimeSegments.length === 0) {
      if (data.timeOfDay === "morning") {
        return { start: 7, end: 12 }; // 默认上午时段
      } else if (data.timeOfDay === "afternoon") {
        return { start: 12, end: 18 }; // 默认下午时段
      }
      return { start: 0, end: 24 }; // 默认全天
    }

    // 从当前时段的班次中获取时间范围
    let minHour = 24;
    let maxHour = 0;
    let validTimeCount = 0;

    currentTimeSegments.forEach(shift => {
      try {
        const startTime = parseDateTime(shift.startTime);
        const endTime = parseDateTime(shift.endTime);

        if (startTime && endTime) {
          const startHour = startTime.getHours() + startTime.getMinutes() / 60;
          const endHour = endTime.getHours() + endTime.getMinutes() / 60;

          minHour = Math.min(minHour, startHour);
          maxHour = Math.max(maxHour, endHour);
          validTimeCount++;
        } else {
          console.warn(`班次时间解析失败: ${shift.startTime}-${shift.endTime}`);
        }
      } catch (error) {
        console.error("计算时间范围出错:", error);
      }
    });
    // 如果计算出有效范围
    if (validTimeCount > 0 && minHour < 24 && maxHour > 0) {
      // 不再添加缓冲时间，直接使用精确的时间范围

      // 确保范围合理
      if (data.timeOfDay === "morning" && maxHour > 12) {
        maxHour = 12;
      } else if (data.timeOfDay === "afternoon" && minHour < 12) {
        minHour = 12;
      }
      return { start: minHour, end: maxHour };
    } else {
      console.warn("无法计算有效时间范围，使用默认值");
    }

    // 如果无法计算出有效范围，使用默认值
    if (data.timeOfDay === "morning") {
      return { start: 7, end: 12 };
    } else if (data.timeOfDay === "afternoon") {
      return { start: 12, end: 18 };
    }

    return { start: 0, end: 24 }; // 默认全天时间范围
  } catch (error) {
    console.error("timeRange 计算属性出错:", error);
    // 根据 timeOfDay 返回默认时间范围
    if (props.timeOfDay === "morning") {
      return { start: 7, end: 12 };
    } else if (props.timeOfDay === "afternoon") {
      return { start: 12, end: 18 };
    }
    return { start: 0, end: 24 }; // 返回默认时间范围
  }
});

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "normal":
      return "正常";
    case "leave":
      return "脱岗";
    case "fault":
      return "设备故障";
    case "future":
      return "未来";
    default:
      return "未知状态";
  }
};

// 获取脱岗员工数量
const getLeaveStaffCount = (segment: TimeSegment) => {
  return (
    segment.leaveRecords?.filter(record => {
      const now = currentTime.value; // 使用统一的当前时间引用
      const recordStartTime = parseDateTime(record.startTime);
      const recordEndTime = parseDateTime(record.endTime);

      // 脱岗中或脱岗已结束的人员都计入脱岗人数
      if (
        record.status === "leave" ||
        record.status === "脱岗" ||
        record.status === "已结束" ||
        record.status?.includes("脱岗")
      ) {
        // 待上岗状态不计入脱岗人数
        if (recordStartTime && recordStartTime > now) {
          return false;
        }
        return true;
      }

      return false;
    }).length || 0
  );
};

// 生成时间刻度
const timeMarks = computed(() => {
  const { start, end } = timeRange.value;
  const range = end - start;

  // 根据不同的时间范围调整间隔
  let interval = 1; // 默认1小时间隔

  // 时间段过短时，使用更小的间隔
  if (range <= 2) {
    interval = 0.25; // 15分钟间隔
  } else if (range <= 4) {
    interval = 0.5; // 30分钟间隔
  }

  const marks = [];
  for (let hour = start; hour <= end; hour += interval) {
    marks.push(hour);
  }
  return marks;
});

// 添加一个格式化时间范围的计算属性
const formattedTimeRange = computed(() => {
  const { start, end } = timeRange.value;
  return `${formatTime(start)}-${formatTime(end)}`;
});

// 获取时间段时长
const getSegmentDuration = (segment: TimeSegment) => {
  const startTime = new Date(segment.startTime);
  const endTime = new Date(segment.endTime);
  const durationMs = endTime.getTime() - startTime.getTime();
  const durationMinutes = Math.round(durationMs / (1000 * 60));

  if (durationMinutes >= 60) {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
  } else {
    return `${durationMinutes}分钟`;
  }
};

// 获取总员工数量 - 计算在这个时间段应该工作的员工总数
const getTotalStaffCount = (segment: TimeSegment) => {
  // 计算在这个时间段内应该工作的员工总数
  const segmentStartTime = new Date(segment.startTime);
  const segmentEndTime = new Date(segment.endTime);

  // 获取在这个时间段内应该工作的所有员工
  const activeStaff = new Set<string>();
  const data = currentData.value;

  data.shifts.forEach(shift => {
    try {
      const shiftStart = parseDateTime(shift.startTime);
      const shiftEnd = parseDateTime(shift.endTime);

      if (shiftStart && shiftEnd) {
        // 检查班次时间是否与当前时间段重叠
        if (shiftStart < segmentEndTime && shiftEnd > segmentStartTime) {
          // 如果有员工姓名，加入到集合中
          if (shift.staffName) {
            activeStaff.add(shift.staffName);
          } else {
            // 如果没有具体员工信息，默认每个班次1个员工
            activeStaff.add(`${shift.shiftName}_staff`);
          }
        }
      }
    } catch (error) {
      // 处理解析错误
    }
  });

  return activeStaff.size;
};

// 获取人员列表 - 显示所有在此时间段相关的员工
const getSegmentStaffList = (segment: TimeSegment) => {
  const segmentStartTime = new Date(segment.startTime);
  const segmentEndTime = new Date(segment.endTime);
  const staffList: Array<{
    name: string;
    shiftName: string;
  }> = [];
  const data = currentData.value;

  // 获取在这个时间段内应该工作的所有员工
  data.shifts.forEach(shift => {
    try {
      const shiftStart = parseDateTime(shift.startTime);
      const shiftEnd = parseDateTime(shift.endTime);

      if (shiftStart && shiftEnd) {
        // 检查班次时间是否与当前时间段重叠
        if (shiftStart < segmentEndTime && shiftEnd > segmentStartTime) {
          const staffName = shift.staffName || `${shift.shiftName}值班人员`;

          staffList.push({
            name: staffName,
            shiftName: shift.shiftName
          });
        }
      }
    } catch (error) {
      // 处理解析错误
    }
  });

  return staffList;
};

// 监听 viewDate 变化，当日期变化时重新初始化数据快照
watch(
  () => props.viewDate,
  newValue => {
    if (newValue) {
      // 日期变化时隐藏tooltip，避免显示错误的数据
      hideTooltip();
      initializeDataSnapshot();
    }
  },
  { immediate: true }
);

// 监听时间段数据变化，当数据更新时隐藏tooltip
watch(
  () => timeSegments.value,
  () => {
    // 时间段数据变化时隐藏tooltip，避免显示过期的数据
    if (tooltipVisible.value) {
      hideTooltip();
    }
  }
);
</script>

<style lang="scss">
@import "../styles/font-sizes.scss";
/* 全局样式，不使用scoped */
.timeline-global-tooltip {
  @keyframes tooltip-fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  position: fixed;
  z-index: 999999; /* 极高的z-index */
  min-width: 250px;
  max-width: 350px;
  padding: 12px;
  color: rgb(255 255 255 / 90%);
  pointer-events: auto;
  background: rgb(0 24 75 / 95%);
  backdrop-filter: blur(12px);
  border: 1px solid rgb(64 158 255 / 20%);
  border-radius: 4px;

  /* 强烈的视觉边框效果 */
  outline: 1px solid rgb(64 158 255 / 50%);
  box-shadow: 0 8px 32px rgb(0 0 0 / 60%);
  animation: tooltip-fade-in 0.2s ease-out;

  .tooltip-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 8px;

    .tooltip-title {
      flex: 1;
      @extend .fs-timeline-tooltip-title; // 时间段详情标题字体大小
      font-weight: 500;
      color: #fff;
    }

    .tooltip-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      margin-left: 10px;
      @extend .fs-illegal-close; // 关闭按钮字体大小
      color: rgb(255 255 255 / 70%);
      cursor: pointer;
      border-radius: 50%;

      &:hover {
        color: #fff;
        background: rgb(255 255 255 / 10%);
      }
    }
  }

  .tooltip-content {
    .info-section {
      padding: 10px;
      margin-bottom: 16px;
      background: rgb(0 24 75 / 40%);
      border-left: 3px solid rgb(64 158 255 / 60%);
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background: rgb(0 24 75 / 60%);
        border-left-color: rgb(64 158 255 / 80%);
      }

      .section-title {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-bottom: 8px;
        @extend .fs-timeline-tooltip-content; // 各个信息板块标题字体大小
        font-weight: 600;
        color: rgb(255 255 255 / 90%);

        .section-icon {
          @extend .fs-timeline-tooltip-content; // 信息板块图标字体大小
          opacity: 0.8;
        }
      }

      .section-content {
        @extend .fs-timeline-tooltip-content; // 信息板块内容字体大小
        line-height: 1.5;
        color: rgb(255 255 255 / 85%);
      }
    }

    // 时间信息板块
    .time-section {
      border-left-color: rgb(64 158 255 / 60%);

      .section-content {
        @extend .fs-timeline-tooltip-content; // 时间信息字体大小
        font-weight: 500;
        color: #409eff;
      }
    }

    // 时长信息板块
    .duration-section {
      border-left-color: rgb(82 196 26 / 60%);

      .section-content {
        @extend .fs-timeline-tooltip-content; // 时长信息字体大小
        font-weight: 500;
        color: #52c41a;
      }
    }

    // 状态信息板块
    .status-section {
      border-left-color: rgb(250 140 22 / 60%);

      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        margin-bottom: 8px;
        @extend .fs-timeline-tooltip-content; // 状态标签字体大小
        font-weight: 500;
        border-radius: 4px;

        &.status-normal {
          color: rgb(82 196 26 / 90%);
          background: rgb(82 196 26 / 15%);
          border-left: 2px solid rgb(82 196 26 / 50%);
        }

        &.status-leave {
          color: rgb(250 173 20 / 90%);
          background: rgb(250 173 20 / 15%);
          border-left: 2px solid rgb(250 173 20 / 50%);
        }

        &.status-fault {
          color: rgb(245 34 45 / 90%);
          background: rgb(245 34 45 / 15%);
          border-left: 2px solid rgb(245 34 45 / 50%);
        }

        &.status-future {
          color: rgb(191 191 191 / 90%);
          background: rgb(191 191 191 / 15%);
          border-left: 2px solid rgb(191 191 191 / 50%);
        }
      }

      .status-stats {
        display: flex;
        gap: 12px;
        align-items: center;
        justify-content: space-between;

        .stat-item {
          padding: 2px 8px;
          @extend .fs-timeline-tooltip-content; // 统计数据项字体大小
          color: rgb(255 255 255 / 70%);
          background: rgb(0 24 75 / 30%);
          border-radius: 4px;

          strong {
            font-weight: 600;
            color: #409eff;
          }

          &.leave-stat strong {
            color: #f56c6c;
          }
        }
      }
    }

    // 人员信息板块
    .staff-section {
      border-left-color: rgb(135 208 104 / 60%);

      .staff-list {
        .staff-item {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 8px 10px;
          margin-bottom: 6px;
          background: rgb(0 24 75 / 40%);
          border: 1px solid rgb(64 158 255 / 10%);
          border-radius: 4px;
          transition: all 0.3s ease;

          &:hover {
            background: rgb(0 24 75 / 60%);
            border-color: rgb(64 158 255 / 30%);
            transform: translateX(2px);
          }

          .staff-basic {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .staff-name {
              @extend .fs-timeline-tooltip-content; // 人员姓名字体大小
              font-weight: 500;
              color: #409eff;
            }

            .staff-shift {
              @extend .fs-timeline-tooltip-content; // 人员班次信息字体大小
              color: rgb(255 255 255 / 50%);
            }
          }
        }
      }

      .no-staff-info {
        padding: 20px;
        @extend .fs-timeline-tooltip-content; // 无人员信息提示字体大小
        color: rgb(255 255 255 / 50%);
        text-align: center;
        background: rgb(0 24 75 / 20%);
        border: 1px dashed rgb(64 158 255 / 20%);
        border-radius: 4px;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.time-line-container {
  position: relative;
  width: 100%;
  padding: 8px;
  background: rgb(0 24 75 / 30%);
  border-radius: 4px;
}

.time-line-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.time-marks {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 0 10px;
  margin-bottom: 8px;
}

.time-mark {
  @extend .fs-attendance-detail-table-header; // 时间标记字体大小
  color: rgb(255 255 255 / 50%);
  text-align: center;
}

.time-line-content {
  position: relative;
  width: 100%;
  height: 40px;
  margin-bottom: 10px;
  background: rgb(0 24 75 / 50%);
  border-radius: 4px;
}

.time-segment {
  position: absolute;
  height: 100%;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;

  &.segment-normal {
    background: rgb(82 196 26 / 30%); // 绿色 - 正常在岗
    border: 1px solid rgb(82 196 26 / 50%);

    &:hover {
      background: rgb(82 196 26 / 50%);
    }
  }

  &.segment-leave {
    background: rgb(250 173 20 / 60%); // 黄色 - 脱岗状态
    border: 1px solid rgb(250 173 20 / 50%);

    &:hover {
      background: rgb(250 173 20 / 50%);
    }
  }

  &.segment-fault {
    background: rgb(245 34 45 / 30%); // 红色 - 设备故障
    border: 1px solid rgb(245 34 45 / 50%);

    &:hover {
      background: rgb(245 34 45 / 50%);
    }
  }

  &.segment-future {
    background: rgb(191 191 191 / 30%); // 灰色 - 未来时段
    border: 1px solid rgb(191 191 191 / 50%);

    &:hover {
      background: rgb(191 191 191 / 50%);
    }
  }

  &:hover {
    z-index: 1;
    transform: scaleY(1.1);
  }
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  @extend .fs-timeline-tooltip-content; // 无数据提示字体大小
  color: rgb(255 255 255 / 60%);
  background: rgb(0 24 75 / 30%);
  border: 1px dashed rgb(64 158 255 / 30%);
  border-radius: 4px;
}

.tooltip-stats {
  padding-top: 8px;
  margin-top: 8px;
  border-top: 1px solid rgb(255 255 255 / 10%);
}

.tooltip-stats-title {
  margin-bottom: 4px;
  @extend .fs-timeline-tooltip-content; // 统计标题字体大小
  color: rgb(255 255 255 / 70%);
}

.tooltip-stats-info {
  display: flex;
  justify-content: space-between;
  padding: 4px;
  background: rgb(0 24 75 / 30%);
  border-radius: 2px;

  .stats-total {
    @extend .fs-timeline-tooltip-content; // 总人数统计字体大小
    color: #409eff;
  }

  .stats-leave {
    @extend .fs-timeline-tooltip-content; // 脱岗人数统计字体大小
    color: #f56c6c;
  }
}

.time-period-title {
  margin-bottom: 8px;
  @extend .fs-timeline-tooltip-content; // 时间段标题字体大小
  font-weight: 500;
  color: rgb(255 255 255 / 70%);
}

.staff-list {
  margin-top: 10px;

  .staff-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 10px;
    margin-bottom: 6px;
    background: rgb(0 24 75 / 40%);
    border: 1px solid rgb(64 158 255 / 10%);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgb(0 24 75 / 60%);
      border-color: rgb(64 158 255 / 30%);
      transform: translateX(2px);
    }

    .staff-basic {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .staff-name {
        @extend .fs-timeline-tooltip-content; // 人员列表中的姓名字体大小
        font-weight: 500;
        color: #409eff;
      }

      .staff-shift {
        @extend .fs-attendance-detail-other-status; // 人员列表中的班次信息字体大小
        color: rgb(255 255 255 / 50%);
      }
    }
  }
}
</style>
