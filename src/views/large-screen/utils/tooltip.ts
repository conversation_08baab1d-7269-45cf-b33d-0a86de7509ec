/**
 * ECharts tooltip 优化配置工具
 * 解决tooltip被遮挡的问题
 */

export interface TooltipPositionParams {
  point: [number, number];
  params: any;
  dom: HTMLElement;
  rect: any;
  size: {
    contentSize: [number, number];
    viewSize: [number, number];
  };
}

/**
 * 智能计算tooltip位置，防止超出容器边界
 * @param point 鼠标位置 [x, y]
 * @param params tooltip参数
 * @param dom tooltip DOM元素
 * @param rect 图表容器矩形信息
 * @param size 尺寸信息
 * @returns 计算后的位置 [x, y]
 */
export function calculateTooltipPosition(
  point: [number, number],
  params: any,
  dom: HTMLElement,
  rect: any,
  size: { contentSize: [number, number]; viewSize: [number, number] }
): [number, number] {
  const [mouseX, mouseY] = point;
  const { contentSize, viewSize } = size;
  const [tooltipWidth, tooltipHeight] = contentSize;
  const [containerWidth, containerHeight] = viewSize;

  let x = mouseX + 10; // 默认在鼠标右侧
  let y = mouseY - tooltipHeight / 2; // 垂直居中

  // 检查右边界，如果超出则显示在左侧
  if (x + tooltipWidth > containerWidth) {
    x = mouseX - tooltipWidth - 10;
  }

  // 检查上下边界
  if (y < 0) {
    y = 10;
  } else if (y + tooltipHeight > containerHeight) {
    y = containerHeight - tooltipHeight - 10;
  }

  return [x, y];
}

/**
 * 获取优化的tooltip基础配置
 * @param isInDialog 是否在对话框中
 * @param customConfig 自定义配置
 * @returns tooltip配置对象
 */
export function getOptimizedTooltipConfig(
  isInDialog: boolean = false,
  customConfig: any = {}
) {
  const baseConfig = {
    // 基础样式配置
    backgroundColor: "rgba(0, 24, 75, 0.9)",
    borderColor: "rgba(64, 158, 255, 0.3)",
    borderWidth: 1,
    padding: [6, 8], // 减小内边距，让tooltip更紧凑
    textStyle: {
      color: "rgba(255, 255, 255, 0.9)",
      fontSize: 12 // 减小字体大小，让tooltip更小巧
    },
    extraCssText:
      "box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4); border-radius: 4px;",

    // 位置优化配置
    confine: true, // 限制tooltip在图表容器内
    position: calculateTooltipPosition
  };

  // 如果在对话框中，添加appendToBody配置
  if (isInDialog) {
    (baseConfig as any).appendToBody = true;
  }

  // 合并自定义配置
  return {
    ...baseConfig,
    ...customConfig
  };
}

/**
 * 获取折线图优化的tooltip配置
 * @param isInDialog 是否在对话框中
 * @param customConfig 自定义配置
 * @returns 折线图tooltip配置
 */
export function getLineChartTooltipConfig(
  isInDialog: boolean = false,
  customConfig: any = {}
) {
  return getOptimizedTooltipConfig(isInDialog, {
    trigger: "axis",
    axisPointer: {
      type: "line",
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        width: 1,
        type: "solid"
      }
    },
    ...customConfig
  });
}

/**
 * 获取柱状图优化的tooltip配置
 * @param isInDialog 是否在对话框中
 * @param customConfig 自定义配置
 * @returns 柱状图tooltip配置
 */
export function getBarChartTooltipConfig(
  isInDialog: boolean = false,
  customConfig: any = {}
) {
  return getOptimizedTooltipConfig(isInDialog, {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    },
    ...customConfig
  });
}

/**
 * 获取饼图优化的tooltip配置
 * @param isInDialog 是否在对话框中
 * @param customConfig 自定义配置
 * @returns 饼图tooltip配置
 */
export function getPieChartTooltipConfig(
  isInDialog: boolean = false,
  customConfig: any = {}
) {
  return getOptimizedTooltipConfig(isInDialog, {
    trigger: "item",
    formatter: "{b}: {c} ({d}%)",
    ...customConfig
  });
}

/**
 * 获取地图优化的tooltip配置
 * @param isInDialog 是否在对话框中
 * @param customConfig 自定义配置
 * @returns 地图tooltip配置
 */
export function getMapTooltipConfig(
  isInDialog: boolean = false,
  customConfig: any = {}
) {
  return getOptimizedTooltipConfig(isInDialog, {
    trigger: "item",
    ...customConfig
  });
}
