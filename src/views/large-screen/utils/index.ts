// 获取图片URL
export const getImageUrl = (name: string) => {
  return new URL(`../../../assets/screen/${name}`, import.meta.url).href;
};

// 格式化时间
export const formatTime = (time: string | number) => {
  const date = new Date(time);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hour = String(date.getHours()).padStart(2, "0");
  const minute = String(date.getMinutes()).padStart(2, "0");
  const second = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 格式化数字
export const formatNumber = (num: number) => {
  return num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
};
