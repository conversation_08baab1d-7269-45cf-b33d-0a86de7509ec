<template>
  <!-- 设备状态 -->
  <div class="device-overview">
    <div class="overview-numbers">
      <div class="number-item">
        <div class="item-icon">
          <dv-decoration-8 style="width: 40px" />
        </div>
        <div class="item-info">
          <div class="item-label">点位</div>
          <div class="item-value">{{ data?.totalLocations || 0 }}</div>
        </div>
      </div>
      <div class="number-item">
        <div class="item-info">
          <div class="item-label">正常</div>
          <div class="item-value">
            {{ data?.types?.find(t => t.name === "正常点位")?.value || 0 }}
          </div>
        </div>
      </div>
      <div class="number-item">
        <div class="item-info">
          <div class="item-label">异常</div>
          <div class="item-value">
            {{ data?.types?.find(t => t.name === "异常点位")?.value || 0 }}
          </div>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="device-chart" />

    <DeviceDetailDialog
      v-model:visible="dialogVisible"
      :device-state="currentState"
    />
  </div>
</template>

<script setup lang="ts">
import type { EChartsOption } from "echarts";
import * as echarts from "echarts";
import { ref, watch, onUnmounted } from "vue";
import DeviceDetailDialog from "../dialogs/DeviceDetailDialog.vue";

interface DeviceData {
  totalLocations: number;
  types: Array<{
    name: string;
    value: number;
  }>;
  abnormalLocations: Array<{
    site: string;
    totalDevices: number;
    city: string;
    hamlet: string;
    onlineDevices: number;
    county: string;
    offlineDevices: number;
    township: string;
  }>;
  normalLocations: Array<{
    site: string;
    totalDevices: number;
    city: string;
    hamlet: string;
    onlineDevices: number;
    county: string;
    offlineDevices: number;
    township: string;
  }>;
}

const props = defineProps<{
  data: DeviceData;
}>();

const chartRef = ref<HTMLElement>();
const dialogVisible = ref(false);
const currentState = ref<0 | 1>(0); // 0-正常，1-离线

const handleChartClick = (params: any) => {
  if (params.name) {
    dialogVisible.value = true;
    currentState.value = params.name === "正常点位" ? 0 : 1; // 正常点位对应状态0
  }
};

const renderChartHanle = () => {
  if (chartRef.value && props.data?.types) {
    const chart = echarts.init(chartRef.value);

    const option: EChartsOption = {
      tooltip: {
        trigger: "item",
        formatter: "{b}: {c} ({d}%)"
      },
      legend: {
        orient: "vertical",
        right: 10,
        top: "center",
        selectedMode: false,
        textStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 14 // 饼图右侧图例("正常点位"、"异常点位")字体大小
        }
      },
      series: [
        {
          type: "pie",
          radius: ["40%", "70%"],
          center: ["30%", "50%"],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: "rgba(0, 0, 0, 0.2)",
            borderWidth: 2
          },
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          data: props.data.types.map(item => ({
            name: item.name,
            value: item.value,
            itemStyle: {
              color: item.name === "正常点位" ? "#67c23a" : "#F56C6C"
            }
          }))
        }
      ]
    };

    chart.setOption(option);

    chart.off("click");
    chart.on("click", handleChartClick);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener("resize", handleResize);

    onUnmounted(() => {
      window.removeEventListener("resize", handleResize);
      chart.dispose();
    });
  }
};

watch(() => props.data, renderChartHanle, {
  immediate: true
});
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgb(103 194 58 / 40%);
  }

  70% {
    box-shadow: 0 0 0 10px rgb(103 194 58 / 0%);
  }

  100% {
    box-shadow: 0 0 0 0 rgb(103 194 58 / 0%);
  }
}

.device-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;

  .overview-numbers {
    display: flex;
    justify-content: space-between;
    padding: 0;

    .number-item {
      display: flex;
      flex: 1;
      gap: 8px;
      align-items: center;
      justify-content: center;

      .item-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        padding: 6px;
        background: rgb(255 255 255 / 10%);
        border-radius: 8px;
      }

      .item-info {
        text-align: center;

        .item-label {
          margin-bottom: 4px;
          @extend .fs-device-label; /* 设备统计标签("点位"、"正常"、"异常")字体大小 */
          color: rgb(255 255 255 / 60%);
        }

        .item-value {
          @extend .fs-device-value; /* 设备统计数值字体大小 */
          font-weight: bold;
          color: transparent;
          background: linear-gradient(to bottom, #fff, #7eb9ff);
          background-clip: text;
        }
      }
    }
  }

  .device-chart {
    flex: 1;
    min-height: 0;
    padding: 0;
  }
}

:deep(.device-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__header {
    padding: 15px 20px;
    margin: 0;
    background: #f5f7fa;
  }

  .el-table {
    --el-table-header-bg-color: #f5f7fa;

    .el-table__header-wrapper {
      th {
        color: #606266;
        background: #f5f7fa;
      }
    }
  }
}

// 添加暗色主题支持
:deep(.dark) {
  .device-dialog {
    .el-dialog__header {
      background: #1d1e1f;
    }

    .el-table {
      --el-table-header-bg-color: #1d1e1f;

      .el-table__header-wrapper {
        th {
          color: #a3a6ad;
          background: #1d1e1f;
        }
      }
    }
  }
}
</style>
