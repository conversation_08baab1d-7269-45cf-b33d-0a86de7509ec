<template>
  <!-- 违法排名-->
  <div class="hotspot-rank">
    <div class="rank-list">
      <div
        v-for="(item, index) in data"
        :key="item.location"
        class="rank-item"
        :class="{ 'top-3': index < 3 }"
        @click="handleClick(item)"
      >
        <div class="rank-info">
          <span class="rank-number">{{ index + 1 }}</span>
          <span class="rank-name">{{ item.location }}</span>
          <span class="rank-value">{{ item.count }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from "vue";

const props = defineProps<{
  data: Array<{
    site: string;
    city: string;
    hamlet: string;
    count: number;
    county: string;
    location: string;
    township: string;
  }>;
  type: "day" | "month";
}>();

const emit = defineEmits<{
  (
    e: "itemClick",
    item: {
      site: string;
      city: string;
      hamlet: string;
      count: number;
      county: string;
      location: string;
      township: string;
      type: "day" | "month";
    }
  ): void;
}>();

const handleClick = (item: any) => {
  emit("itemClick", { ...item, type: props.type });
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.hotspot-rank {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  padding: 10px;
  padding-top: 48px;
}

.rank-list {
  flex: 1;
  padding-right: 10px;
  margin-right: -10px;
  overflow: hidden auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(255 255 255 / 20%);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.rank-item {
  padding: 10px;
  cursor: pointer;
  background: rgb(255 255 255 / 5%);
  border-radius: 4px;
  transition: all 0.3s ease;

  &:not(:last-child) {
    margin-bottom: 8px;
  }

  &.top-3 {
    background: rgb(245 108 108 / 10%);

    .rank-number {
      color: #f56c6c;
    }
  }

  &:hover {
    background: rgb(255 255 255 / 10%);
    transform: translateX(4px);
  }
}

.rank-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.rank-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  @extend .fs-illegal-rank; /* 违法排名序号(1,2,3...)字体大小 */
  font-weight: bold;
  color: #409eff;
}

.rank-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  @extend .fs-illegal-rank; /* 违法排名地点名称字体大小 */
  color: rgb(255 255 255 / 80%);
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rank-value {
  min-width: 48px;
  @extend .fs-illegal-rank; /* 违法排名数量值字体大小 */
  font-weight: bold;
  color: #409eff;
  text-align: right;
}
</style>
