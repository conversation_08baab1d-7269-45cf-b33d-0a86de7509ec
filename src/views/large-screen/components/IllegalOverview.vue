<template>
  <!-- 违法概览 -->
  <div class="illegal-overview">
    <div class="overview-numbers">
      <div class="number-item">
        <div class="item-icon">
          <dv-decoration-9 style="width: 40px" />
        </div>
        <div class="item-info">
          <div class="item-label">违法总数</div>
          <div class="item-value">{{ data.total }}</div>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="trend-chart" />
  </div>
</template>

<script setup lang="ts">
import type { EChartsOption } from "echarts";
import * as echarts from "echarts";
import { onUnmounted, ref, watch, computed } from "vue";
import type { OverviewData } from "../types";

const props = defineProps<{
  data: OverviewData;
  timeRange?: string;
  selectedMonth?: string; // 新增：选择的月份，格式如 "2024-12"
}>();

const emit = defineEmits<{
  (e: "pointClick"): void;
}>();

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

const handleChartClick = (params: any) => {
  emit("pointClick");
};

const filteredTrendData = computed(() => {
  if (!props.data?.trend) return [];

  const now = new Date();

  // 根据时间范围类型进行不同的过滤逻辑
  if (props.timeRange === "day") {
    // 今日：过滤到当前小时和分钟
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    return props.data.trend.filter(item => {
      const timeStr = item.date;
      let itemHour = 0;
      let itemMinute = 0;

      if (typeof timeStr === "string") {
        if (timeStr.includes(":")) {
          const [hourStr, minuteStr] = timeStr.split(":");
          itemHour = parseInt(hourStr) || 0;
          itemMinute = parseInt(minuteStr) || 0;
        } else if (timeStr.match(/^\d+$/)) {
          itemHour = parseInt(timeStr) || 0;
          itemMinute = 0;
        } else if (timeStr.includes("时")) {
          const match = timeStr.match(/(\d+)时/);
          itemHour = match ? parseInt(match[1]) : 0;
          itemMinute = 0;
        }
      }

      if (itemHour < currentHour) {
        return true;
      } else if (itemHour === currentHour) {
        return itemMinute <= currentMinute;
      }
      return false;
    });
  } else if (props.timeRange === "month") {
    // 月度数据：需要区分当前月份和历史月份
    const currentDate = now.getDate();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();

    // 判断是否是当前年月
    let isCurrentMonth = false;
    if (props.selectedMonth) {
      const [selectedYear, selectedMonthNum] = props.selectedMonth.split("-");
      isCurrentMonth =
        parseInt(selectedYear) === currentYear &&
        parseInt(selectedMonthNum) === currentMonth;
    } else {
      // 如果没有传递选择的月份，默认认为是当前月
      isCurrentMonth = true;
    }

    if (isCurrentMonth) {
      return props.data.trend.filter(item => {
        const dateStr = item.date;
        if (typeof dateStr === "string" && dateStr.match(/^\d{1,2}$/)) {
          // 纯数字格式，如 "01", "02", "31"
          const itemDay = parseInt(dateStr) || 0;
          return itemDay <= currentDate;
        }
        return true; // 其他格式的数据保留
      });
    } else {
      // 历史月份，显示所有数据
      return props.data.trend;
    }
  }

  // 其他时间范围（如年度）：返回所有数据
  return props.data.trend;
});

const renderChartHanle = () => {
  if (chartRef.value && props.data?.trend) {
    if (chart) {
      chart.dispose();
    }
    chart = echarts.init(chartRef.value);

    let currentPieChart: echarts.ECharts | null = null;

    const option: EChartsOption = {
      tooltip: {
        trigger: "axis",
        // 优化tooltip位置，防止被遮挡
        confine: true, // 限制tooltip在图表容器内
        backgroundColor: "rgba(0, 24, 75, 0.9)",
        borderColor: "rgba(64, 158, 255, 0.3)",
        borderWidth: 1,
        padding: [6, 8], // 减小内边距，让tooltip更紧凑
        textStyle: {
          color: "rgba(255, 255, 255, 0.9)",
          fontSize: 12 // 减小字体大小，让tooltip更小巧
        },
        position: function (point, params, dom, rect, size) {
          // 智能位置计算，防止tooltip超出边界
          const [mouseX, mouseY] = point;
          const { contentSize, viewSize } = size;
          const [tooltipWidth, tooltipHeight] = contentSize;
          const [containerWidth, containerHeight] = viewSize;

          let x = mouseX + 10; // 默认在鼠标右侧
          let y = mouseY - tooltipHeight / 2; // 垂直居中

          // 检查右边界，如果超出则显示在左侧
          if (x + tooltipWidth > containerWidth) {
            x = mouseX - tooltipWidth - 10;
          }

          // 检查上下边界
          if (y < 0) {
            y = 10;
          } else if (y + tooltipHeight > containerHeight) {
            y = containerHeight - tooltipHeight - 10;
          }

          return [x, y];
        }
      },
      grid: {
        top: 10,
        right: 10,
        bottom: 10,
        left: 10,
        containLabel: true
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data:
          filteredTrendData.value.length > 0
            ? filteredTrendData.value.map(item => {
                // 根据时间范围格式化显示
                if (props.timeRange === "day") {
                  // 日视图：格式化时间为 'HH时'
                  const timeStr = item.date;
                  let itemHour = 0;

                  if (typeof timeStr === "string") {
                    if (timeStr.includes(":")) {
                      const [hourStr] = timeStr.split(":");
                      itemHour = parseInt(hourStr) || 0;
                    } else if (timeStr.match(/^\d+$/)) {
                      itemHour = parseInt(timeStr) || 0;
                    } else if (timeStr.includes("时")) {
                      const match = timeStr.match(/(\d+)时/);
                      itemHour = match ? parseInt(match[1]) : 0;
                    }
                  }

                  return `${itemHour}时`;
                } else if (props.timeRange === "month") {
                  // 月视图：格式化日期为 'D日'（去掉前导0）
                  const dateStr = item.date;
                  if (
                    typeof dateStr === "string" &&
                    dateStr.match(/^\d{1,2}$/)
                  ) {
                    // 纯数字格式，如 "01", "02", "31"，去掉前导0并添加"日"后缀
                    const dayNumber = parseInt(dateStr) || 0;
                    return `${dayNumber}号`;
                  }
                  // 其他格式保持原样
                  return dateStr;
                }
                // 其他视图直接返回原始 date 字段
                return item.date;
              })
            : [],
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.3)"
          }
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 14, // X坐标轴标签字体
          interval: (() => {
            const dataLength = filteredTrendData.value.length;
            if (dataLength <= 10) return 0; // 10天以内，每天显示
            if (dataLength <= 20) return 1; // 10-20天，隔1天显示
            return 2; // 20天以上，隔2天显示
          })()
        }
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)"
          }
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 14 // Y坐标轴标签字体
        },
        minInterval: 1
      },
      series: [
        {
          name: "违法数量",
          data:
            filteredTrendData.value.length > 0
              ? filteredTrendData.value.map(item => item.value)
              : [],
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          cursor: "pointer",
          itemStyle: {
            color: "#409eff"
          },
          lineStyle: {
            color: "#409eff",
            width: 3
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(64, 158, 255, 0.3)"
              },
              {
                offset: 1,
                color: "rgba(64, 158, 255, 0)"
              }
            ])
          }
        }
      ]
    };

    chart.setOption(option);
    chart.on("click", handleChartClick);

    const resizeHandler = () => {
      chart.resize();
      if (currentPieChart) {
        currentPieChart.resize();
      }
    };

    window.addEventListener("resize", resizeHandler);

    onUnmounted(() => {
      window.removeEventListener("resize", resizeHandler);
      chart.dispose();
      if (currentPieChart) {
        currentPieChart.dispose();
        currentPieChart = null;
      }
      chart.off("click", handleChartClick);
    });
  }
};

watch(() => [props.data, props.timeRange], renderChartHanle, {
  immediate: true
});

function getRandomColor() {
  const colors = ["#4992ff", "#f8b551", "#47d2e9", "#ff6e76", "#7ccd7c"];
  return colors[Math.floor(Math.random() * colors.length)];
}
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
// 引入大屏字体变量
.illegal-overview {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible !important;

  .overview-numbers {
    display: flex;
    justify-content: flex-start;
    padding: 0 20px;
    margin-bottom: 20px;

    .number-item {
      display: flex;
      gap: 12px;
      align-items: center;

      &:not(:last-child) {
        margin-right: 60px;
      }

      .item-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        padding: 8px;
        background: rgb(255 255 255 / 10%);
        border-radius: 8px;
      }

      .item-info {
        .item-label {
          margin-bottom: 4px;
          @extend .fs-illegal-total; // 违法总数字体
          color: rgb(255 255 255 / 60%);
        }

        .item-value {
          @extend .fs-illegal-total; // 违法总数字体（数字）
          font-weight: bold;
          color: transparent;
          background: linear-gradient(to bottom, #fff, #7eb9ff);
          background-clip: text;
        }
      }
    }
  }

  .trend-chart {
    flex: 1;
    min-height: 180px;
  }
}

:deep(.el-card__body) {
  overflow: visible !important;
}

.trend-label {
  font-size: 16px;
}
</style>
