<template>
  <!-- 基础数据 -->
  <div class="basic-data-panel">
    <!-- 城市总览信息 - 改为更紧凑的横向布局 -->
    <div v-if="cityData" class="city-overview">
      <div class="city-name">{{ cityData.name }}</div>
      <div class="city-stats">
        <div class="stat-item">
          <span class="label">总面积:</span>
          <span class="value">{{ cityData.area }}</span>
        </div>
        <div class="stat-item">
          <span class="label">总人口:</span>
          <span class="value">{{ cityData.population }}</span>
        </div>
      </div>
    </div>

    <div class="data-section">
      <div class="list-container">
        <!-- 表头 -->
        <div class="list-item header">
          <span class="col" title="地区">地区</span>
          <span class="col" title="面积">面积</span>
          <span class="col" title="人口">人口</span>
          <span class="col" title="机动车数">机动车数</span>
        </div>

        <!-- 滚动区域 - 增加高度 -->
        <div class="scroll-area">
          <!-- 区县数据 -->
          <div
            v-for="(item, index) in countyData"
            :key="index"
            class="county-item"
          >
            <div class="list-item county-row">
              <span class="col county-name" :title="item.name">{{
                item.name
              }}</span>
              <span class="col" :title="item.area">{{ item.area }}</span>
              <span class="col" :title="item.population">{{
                item.population
              }}</span>
              <span class="col" title="999">999</span>
            </div>
          </div>

          <!-- 无数据提示 -->
          <div v-if="countyData.length === 0" class="no-data">暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStoreHook } from "@/store/modules/app";
import { ref, watch, onMounted, computed } from "vue";
import { getRegionData } from "@/api/largeScreen";
import { useUserStoreHook } from "@/store/modules/user";
// 添加轮询相关的代码
let pollingTimer: NodeJS.Timeout | null = null;
const userStore = useUserStoreHook();
const appStore = useAppStoreHook();
// 区县数据
const countyData = ref([]);
const cityData = ref(null);

// 使用计算属性获取当前地址
const currentAddress = computed(() => {
  const address = appStore.largeScreenArea || userStore.getUserLocationPath;
  return address;
});

// 获取区域数据
const fetchRegionData = async () => {
  try {
    // 强制等待一下，确保地址已更新
    await new Promise(resolve => setTimeout(resolve, 100));
    const res = await getRegionData(appStore.getLargeScreenArea);

    if (res.code === 200 && res.data) {
      cityData.value = {
        name: res.data.name,
        area: res.data.area,
        population: res.data.population
      };
      countyData.value = res.data.point || [];
    } else {
      console.warn("API返回数据格式不正确:", res);
      // 清空数据，避免显示旧数据
      countyData.value = [];
      cityData.value = null;
    }
  } catch (error) {
    console.error("获取区域数据失败:", error);
    // 出错时也清空数据
    countyData.value = [];
    cityData.value = null;
  }
};
// 保留原来的 watch 作为备份
watch(
  () => appStore.largeScreenArea,
  (newVal, oldVal) => {
    if (newVal) fetchRegionData();
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.basic-data-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  min-height: 0; // 添加最小高度

  .city-overview {
    display: flex; // 改为横向布局
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px; // 减小内边距
    background: rgb(0 123 255 / 10%);
    border-left: 3px solid #007bff;
    border-radius: 4px;

    .city-name {
      margin-right: 10px;
      font-size: 16px; /* 城市总览区域城市名称字体大小 */
      font-weight: 600;
      color: #fff;
    }

    .city-stats {
      display: flex;
      gap: 15px;

      .stat-item {
        display: flex;
        align-items: center;

        .label {
          margin-right: 5px;
          font-size: 16px; /* 城市统计标签("总面积"、"总人口")字体大小 */
          color: rgb(255 255 255 / 70%);
        }

        .value {
          font-size: 16px; /* 城市统计数值字体大小 */
          font-weight: 500;
          color: #29d;
        }
      }
    }
  }

  .data-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0; // 添加最小高度
  }

  .list-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0; // 添加最小高度
    overflow: hidden;
    background: rgb(0 0 0 / 20%);
    border: 1px solid rgb(255 255 255 / 12%);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    .scroll-area {
      flex: 1;
      min-height: 0; // 确保最小高度
      padding-bottom: 8px; // 添加底部内边距
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px; // 减小滚动条宽度
        background: rgb(0 0 0 / 20%);
      }

      &::-webkit-scrollbar-thumb {
        background: rgb(255 255 255 / 20%);
        border-radius: 2px;

        &:hover {
          background: rgb(255 255 255 / 30%);
        }
      }
    }
  }

  .list-item {
    display: flex;
    align-items: center;
    padding: 8px 12px; // 减小内边距
    color: rgb(255 255 255 / 80%);
    border-bottom: 1px solid rgb(255 255 255 / 8%);
    transition: all 0.3s;

    &:last-child {
      border-bottom: none;
    }

    .col {
      flex: 1;
      min-width: 0;
      padding: 0 8px;
      overflow: hidden;
      font-size: 18px; /* 数据列表项内容字体大小 */
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .header {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 12px; /* 增加内边距以适应更大的字体 */
    font-size: 18px; /* 数据列表表头("地区"、"面积"、"人口"、"机动车数")字体大小 */
    font-weight: 500;
    color: rgb(255 255 255 / 80%);
    letter-spacing: 0.5px;
    background: rgb(0 123 255 / 15%);
  }

  .county-row {
    background: rgb(255 255 255 / 2%);
    transition: all 0.2s ease;

    &:hover {
      background: rgb(0 123 255 / 10%);
      transform: translateX(3px); // 减小位移效果
    }

    .county-name {
      font-weight: 500;
      color: #29d;
    }
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    font-size: 1.1rem; /* 无数据状态提示文字字体大小 */
    color: rgb(255 255 255 / 50%);
  }
}
</style>
