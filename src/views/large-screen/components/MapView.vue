<template>
  <!-- 地图 -->
  <div class="map-view">
    <div id="map-container" ref="mapRef" />

    <!-- 地图加载状态指示器 -->
    <div v-if="mapLoading" class="map-loading">
      <div class="loading-spinner" />
      <div class="loading-text">地图加载中...</div>
    </div>

    <div v-show="!videoDialogVisible && !dialogVisible" class="map-legend">
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #67c23a" />
        <span class="legend-text">在岗</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #faad14" />
        <span class="legend-text">脱岗</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #909399" />
        <span class="legend-text">休息</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot" style="background-color: #f56c6c" />
        <span class="legend-text">故障</span>
      </div>
    </div>
    <VideoDialog
      :visible="videoDialogVisible"
      :devices="videoDevices"
      @update:visible="handleVideoClose"
    />
    <PointDetailDialog
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :pointData="dialogData"
    />
  </div>
</template>

<script setup lang="ts">
import { confirmAlarm, getIntersection, getManualAlarmList } from "@/api/alarm";
import { useAppStoreHook } from "@/store/modules/app";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts";
import "echarts/extension/bmap/bmap";
import { ElMessage } from "element-plus";
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
import PointDetailDialog from "../dialogs/PointDetailDialog.vue";
import { ManualAlarm } from "../types";
import VideoDialog from "./VideoDialog.vue";
import {
  getDistrictBoundary,
  getDistrictStyle,
  districtCenters
} from "@/utils/yibinBoundary";
import {
  getYibinCityBoundary,
  getYibinCityStyle
} from "@/utils/yibinCityBoundary";
import type { Point, PointData } from "../types/map";

// 添加类型声明
declare global {
  interface Window {
    BMap: any;
    BMapGL: any;
  }
}

// 手动报警列表
const manualAlarmList = ref<ManualAlarm[]>([]);
const appStore = useAppStoreHook();

const props = withDefaults(
  defineProps<{
    points?: Point[];
  }>(),
  {
    points: () => []
  }
);

const emit = defineEmits<{
  (e: "pointClick", point: Point): void;
}>();

const mapRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

// 添加轮询相关的代码
let pollingTimer: NodeJS.Timeout | null = null;

// 视频对话框相关
const videoDialogVisible = ref(false);
const videoDevices = ref<
  Array<{
    id: number;
    deviceName: string;
    streamKey: string;
    equipmentNumber: string;
  }>
>([]);

// 当前报警ID
const currentAlarmId = ref<number>();
const isConfirming = ref(false);

// 添加对话框相关的响应式变量
const dialogVisible = ref(false);
const dialogTitle = ref("");
const dialogData = ref<PointData>({
  site: "",
  address: "",
  city: "",
  county: "",
  township: "",
  hamlet: "",
  devices: [],
  schedules: [],
  staff: [],
  status: ""
});

// 添加响应式变量存储地图中心点
const mapCenter = ref([104.641752, 28.772917]); // 默认中心点

// 处理报警点击事件
const handleAlarmClick = async (alarm: ManualAlarm) => {
  try {
    // 获取路口视频地址
    const res = await getIntersection(alarm.id);
    if (res.code === 200) {
      if (!Array.isArray(res.data)) {
        ElMessage.warning("返回数据格式错误");
        return;
      }
      // 动态处理所有返回的设备
      videoDevices.value = res.data.map(device => ({
        id: device.id,
        deviceName: device.deviceName,
        streamKey: device.streamKey,
        equipmentNumber: device.equipmentNumber
      }));

      videoDialogVisible.value = true;
      currentAlarmId.value = alarm.id;
    } else {
      ElMessage.warning("未获取到视频地址");
    }
  } catch (error) {
    // 只保留一个错误通知，避免重复显示
    ElMessage.error("获取视频地址失败");
    // 仍保留错误日志，但不会影响用户界面显示
    console.error("获取视频地址失败详情:", error);
  }
};

// 处理视频对话框关闭
const handleVideoClose = async (visible: boolean) => {
  videoDialogVisible.value = visible;
  if (!visible && currentAlarmId.value && !isConfirming.value) {
    isConfirming.value = true;
    try {
      const res = await confirmAlarm(currentAlarmId.value);
      if (res.code === 200) {
        ElMessage.success("已确认报警");
        // 关闭对应的报警通知
        const notification = document.querySelector(
          `div[data-alarm-id="${currentAlarmId.value}"]`
        );
        if (notification) {
          notification.remove();
        }
      }
    } catch (error) {
      // 只保留一个错误通知，避免重复显示
      ElMessage.error("确认报警失败");
      // 仍保留错误日志，但不会影响用户界面显示
      console.error("确认报警失败详情:", error);
    } finally {
      currentAlarmId.value = undefined;
      isConfirming.value = false;
    }
  }
};

const fetchMapData = () => {
  if (!mapRef.value) return;
  getManualAlarmList(appStore.getLargeScreenArea).then(res => {
    if (res.code === 200) {
      manualAlarmList.value = res.data;
      // 显示报警通知
      if (manualAlarmList.value.length > 0) {
        if (!videoDialogVisible.value)
          handleAlarmClick(manualAlarmList.value[0]);
      }
    }
  });
};

// 开始轮询
const startPolling = () => {
  // 立即执行一次
  fetchMapData();

  // 设置 10s 的轮询间隔
  pollingTimer = setInterval(() => {
    fetchMapData();
  }, 10000);
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
};

// 添加地图初始化状态跟踪
let isMapInitialized = false;
let currentMapConfig = {
  center: [104.641752, 28.772917],
  zoom: 10,
  county: null
};

// 添加加载状态和动画控制
const mapLoading = ref(true);
const animationDuration = 800; // 动画持续时间
let updateTimer: NodeJS.Timeout | null = null; // 防抖定时器

// 初始化地图（仅在首次加载时执行）
const initializeMap = () => {
  if (!mapRef.value || isMapInitialized) return;

  mapLoading.value = true;

  if (!chart) {
    chart = echarts.init(mapRef.value, null, {
      renderer: "canvas", // 使用canvas渲染，性能更好
      useDirtyRect: true // 启用脏矩形优化
    });
  }

  // 计算初始地图配置
  const mapConfig = calculateMapConfig();
  currentMapConfig = { ...mapConfig };

  const option: EChartsOption = {
    backgroundColor: "transparent",
    bmap: {
      center: mapConfig.center,
      zoom: mapConfig.zoom,
      roam: true,
      mapStyleV2: {
        styleId: "91ffadca1341e6dfc29fcaeabdf5ef5d"
      }
    },
    tooltip: getTooltipConfig(),
    // 添加全局动画配置
    animation: true,
    animationDuration: animationDuration,
    animationEasing: "cubicOut",
    series: []
  };

  chart.setOption(option);
  isMapInitialized = true;

  // 绑定事件监听器
  setupEventListeners();

  // 添加地图加载完成监听 - 缩短超时时间
  setTimeout(() => {
    if (mapLoading.value) {
      mapLoading.value = false; // 超时后强制结束加载状态
      console.log("地图加载超时，强制关闭加载指示器");
    }
  }, 1500); // 缩短到1.5秒
};

// 设置事件监听器
const setupEventListeners = () => {
  if (!chart) return;

  // 监听ECharts渲染完成事件
  chart.on("finished", () => {
    if (mapLoading.value) {
      // 延迟一点时间确保地图完全渲染
      setTimeout(() => {
        mapLoading.value = false;
        console.log("地图渲染完成，关闭加载指示器");
      }, 300);
    }
  });

  // 监听地图点击事件
  chart.on("click", handleMapClick);

  // 监听地图渲染事件（备用检测）
  chart.on("rendered", () => {
    if (mapLoading.value && isMapInitialized) {
      setTimeout(() => {
        if (mapLoading.value) {
          mapLoading.value = false;
          console.log("地图首次渲染完成，关闭加载指示器");
        }
      }, 500);
    }
  });
};

// 计算地图配置（中心点和缩放级别）
const calculateMapConfig = () => {
  const selectedCounty = appStore.getLargeScreenArea?.county;

  if (selectedCounty && districtCenters[selectedCounty]) {
    return {
      center: [
        districtCenters[selectedCounty].lng,
        districtCenters[selectedCounty].lat
      ],
      zoom: 12,
      county: selectedCounty
    };
  }

  // 如果有点位数据，根据点位分布计算
  if (props.points && props.points.length > 0) {
    const bounds = calculatePointsBounds();
    return {
      center: [
        (bounds.minLng + bounds.maxLng) / 2,
        (bounds.minLat + bounds.maxLat) / 2
      ],
      zoom: bounds.zoom,
      county: selectedCounty
    };
  }

  // 默认使用宜宾市中心
  return {
    center: [104.641752, 28.772917],
    zoom: 10,
    county: selectedCounty
  };
};

// 计算点位边界
const calculatePointsBounds = () => {
  if (!props.points || props.points.length === 0) {
    return {
      minLng: mapCenter.value[0] - 0.05,
      maxLng: mapCenter.value[0] + 0.05,
      minLat: mapCenter.value[1] - 0.05,
      maxLat: mapCenter.value[1] + 0.05,
      zoom: 14
    };
  }

  let minLng = 180,
    maxLng = -180,
    minLat = 90,
    maxLat = -90;

  props.points.forEach(point => {
    const lng = Number(point.coordinates[0]);
    const lat = Number(point.coordinates[1]);

    if (!isNaN(lng) && !isNaN(lat)) {
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
    }
  });

  // 根据分布范围计算缩放级别
  const lngDiff = maxLng - minLng;
  const latDiff = maxLat - minLat;
  let zoom = 14;

  if (lngDiff > 0.5 || latDiff > 0.5) zoom = 10;
  else if (lngDiff > 0.2 || latDiff > 0.2) zoom = 12;
  else if (lngDiff > 0.05 || latDiff > 0.05) zoom = 14;
  else zoom = 16;

  return { minLng, maxLng, minLat, maxLat, zoom };
};

// 获取Tooltip配置
const getTooltipConfig = () => ({
  trigger: "item" as const,
  backgroundColor: "rgba(0, 24, 75, 0.8)",
  borderColor: "rgba(0, 110, 255, 0.5)",
  borderWidth: 1,
  padding: [6, 8],
  confine: true,
  position: function (point, params, dom, rect, size) {
    const [mouseX, mouseY] = point;
    const { contentSize, viewSize } = size;
    const [tooltipWidth, tooltipHeight] = contentSize;
    const [containerWidth, containerHeight] = viewSize;

    let x = mouseX + 10;
    let y = mouseY - tooltipHeight / 2;

    if (x + tooltipWidth > containerWidth) {
      x = mouseX - tooltipWidth - 10;
    }

    if (y < 0) {
      y = 10;
    } else if (y + tooltipHeight > containerHeight) {
      y = containerHeight - tooltipHeight - 10;
    }

    return [x, y];
  },
  formatter: (params: any) => {
    if (!params.data || !params.data.data) return "";

    const data = params.data.data;
    if (!data.devices) return "";

    const onlineCount = data.devices.filter(d => d.status === 0).length;

    let devicesHtml = data.devices
      .map(
        device => `
        <div style="margin-top: 2px;padding-left: 8px;font-size:11px;line-height:1.2;">
          ${device.name} (${device.type}):
          <span style="color:${device.status === 0 ? "#67c23a" : "#909399"}">
            ${device.status === 0 ? "在线" : "故障"}
          </span>
        </div>
      `
      )
      .join("");

    let staffHtml = data.staff
      .map(
        staff => `
        <div style="margin-top: 2px;padding-left: 8px;font-size:11px;line-height:1.2;">
          ${staff.name} (${staff.deptName}): ${staff.phone}
        </div>
      `
      )
      .join("");

    return `
      <div style="font-size: 14px; color: #fff;">
        <div style="margin-bottom: 5px;font-weight:bold;">${data.address}</div>
        <div style="margin-bottom: 3px;">设备状态：${onlineCount}/${data.devices.length} 在线</div>
        <div style="margin-bottom: 3px;font-weight:bold;">设备列表：</div>
        ${devicesHtml}
        <div style="margin-bottom: 3px;font-weight:bold;">人员列表：</div>
        ${staffHtml}
      </div>
    `;
  }
});

// 处理地图点击事件
const handleMapClick = params => {
  if (
    params.componentType === "series" &&
    params.componentSubType === "effectScatter" &&
    params.seriesName === "点位" &&
    params.dataIndex != null
  ) {
    const point = props.points?.[params.dataIndex];
    if (point) {
      dialogTitle.value = point.data.site;
      dialogData.value = point.data;
      dialogVisible.value = true;
    }
  }
};

// 防抖更新地图数据
const debouncedUpdateMapData = () => {
  if (updateTimer) {
    clearTimeout(updateTimer);
  }

  updateTimer = setTimeout(() => {
    updateMapDataInternal();
  }, 150); // 150ms防抖延迟
};

// 渐进式更新地图数据（避免重新初始化）
const updateMapDataInternal = () => {
  if (!chart || !isMapInitialized) return;

  try {
    // 获取新的数据
    const newPointsData = getPointsData();
    const newBoundaryData = getBoundaryDataBySelection();
    const newLabelsData = getDistrictLabelsData();

    // 只更新数据相关的series，保持地图实例不变
    const seriesUpdate = [
      // 区域边界
      {
        name: "区域边界",
        type: "lines",
        coordinateSystem: "bmap",
        polyline: true,
        data: newBoundaryData,
        effect: {
          show: true,
          period: 6,
          trailLength: 0,
          symbol: "rect",
          symbolSize: 2
        },
        lineStyle: {
          color: "#00d4ff",
          width: 2,
          opacity: 0.8
        },
        zlevel: 1,
        // 添加平滑动画
        animation: true,
        animationDuration: animationDuration * 0.6
      },
      // 区县标签
      {
        name: "区县标签",
        type: "scatter",
        coordinateSystem: "bmap",
        data: newLabelsData,
        label: {
          show: true,
          formatter: "{b}",
          position: "inside" as const,
          backgroundColor: "rgba(0,0,0,0.7)",
          padding: [6, 10],
          borderRadius: 6,
          color: "#fff",
          fontSize: 14,
          fontWeight: "bold",
          // 添加文字阴影效果
          textShadowColor: "rgba(0,0,0,0.5)",
          textShadowBlur: 2,
          textShadowOffsetX: 1,
          textShadowOffsetY: 1
        },
        emphasis: {
          label: {
            color: "#00d4ff",
            fontSize: 16,
            fontWeight: "bold",
            backgroundColor: "rgba(0,24,75,0.9)"
          }
        },
        zlevel: 2,
        animation: true,
        animationDuration: animationDuration * 0.8
      },
      // 点位标记
      {
        name: "点位",
        type: "effectScatter",
        coordinateSystem: "bmap",
        data: newPointsData,
        symbolSize: function (value, params) {
          // 根据设备数量动态调整点位大小
          const deviceCount = params.data?.data?.devices?.length || 1;
          return Math.max(12, Math.min(20, 12 + deviceCount * 2));
        },
        showEffectOn: "render",
        rippleEffect: {
          brushType: "stroke",
          scale: 3,
          period: 3,
          color: "rgba(255,255,255,0.8)"
        },
        label: {
          show: true,
          position: "top",
          formatter: "{b}",
          color: "#fff",
          fontSize: 11,
          distance: 12,
          fontWeight: "500",
          // 添加文字描边
          textBorderColor: "rgba(0,0,0,0.8)",
          textBorderWidth: 2
        },
        emphasis: {
          scale: 1.8,
          label: {
            show: true,
            color: "#00d4ff",
            fontSize: 13,
            fontWeight: "bold"
          }
        },
        zlevel: 2,
        // 添加入场动画
        animation: true,
        animationDuration: animationDuration,
        animationDelay: function (idx) {
          return idx * 50; // 错开动画时间，创造波浪效果
        }
      }
    ];

    // 使用增量更新，只更新series数据
    chart.setOption(
      {
        series: seriesUpdate
      },
      {
        notMerge: false,
        silent: false // 启用动画
      }
    );

    // 数据更新完成后，确保关闭加载状态
    if (mapLoading.value) {
      setTimeout(() => {
        mapLoading.value = false;
        console.log("数据更新完成，关闭加载指示器");
      }, 200);
    }
  } catch (error) {
    console.error("更新地图数据时出错:", error);
    // 出错时也要关闭加载状态
    mapLoading.value = false;
  }
};

// 对外暴露的更新方法
const updateMapData = debouncedUpdateMapData;

// 获取区县标签数据
const getDistrictLabelsData = () => {
  const selectedCounty = appStore.getLargeScreenArea?.county;

  return Object.entries(districtCenters)
    .filter(([name]) => {
      if (selectedCounty) {
        return name === selectedCounty;
      }
      return false;
    })
    .map(([name, center]: [string, { lng: number; lat: number }]) => ({
      name,
      value: [center.lng, center.lat],
      itemStyle: {
        opacity: 0
      }
    }));
};

// 获取点位数据
const getPointsData = () => {
  return (
    props.points?.map(point => ({
      name: point.data.hamlet,
      value: [Number(point.coordinates[0]), Number(point.coordinates[1])],
      data: point.data,
      itemStyle: {
        color: getPointColor(point)
      }
    })) || []
  );
};

// 智能更新地图中心点和缩放（仅在必要时）
const updateMapCenter = () => {
  if (!chart || !isMapInitialized) return;

  const newConfig = calculateMapConfig();

  // 检查是否需要更新地图视图
  const needsUpdate =
    newConfig.center[0] !== currentMapConfig.center[0] ||
    newConfig.center[1] !== currentMapConfig.center[1] ||
    newConfig.zoom !== currentMapConfig.zoom ||
    newConfig.county !== currentMapConfig.county;

  if (needsUpdate) {
    currentMapConfig = { ...newConfig };
    mapCenter.value = newConfig.center;

    // 平滑更新地图中心点和缩放，避免重新初始化
    chart.setOption({
      bmap: {
        center: newConfig.center,
        zoom: newConfig.zoom
      }
    });

    // 同时更新数据层
    updateMapData();
  }
};

// 合并watch监听器，避免重复触发
watch(
  () => appStore.getLargeScreenArea,
  () => {
    if (isMapInitialized) {
      updateMapCenter();
    }
  },
  { immediate: true, deep: true }
);

// 监听点位数据变化，只更新数据层
watch(
  () => props.points,
  () => {
    if (isMapInitialized) {
      updateMapData();
    }
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize();
};

onMounted(() => {
  // 初始化地图（仅一次）
  initializeMap();
  // 初始化完成后更新数据
  nextTick(() => {
    if (isMapInitialized) {
      updateMapData();
    }
  });

  window.addEventListener("resize", handleResize);
  // 启动报警轮询
  startPolling();
});

onBeforeUnmount(() => {
  // 清理防抖定时器
  if (updateTimer) {
    clearTimeout(updateTimer);
    updateTimer = null;
  }

  // 清理地图相关资源
  if (chart) {
    // 移除所有事件监听器
    chart.off("click");
    chart.off("finished");
    chart.off("rendered");
    chart.dispose();
    chart = null;
  }

  // 重置状态
  isMapInitialized = false;
  mapLoading.value = false;

  // 清理其他监听器和定时器
  window.removeEventListener("resize", handleResize);
  stopPolling();
});

// 获取点位颜色
const getPointColor = (point: Point) => {
  const { status } = point.data;
  switch (status) {
    case "故障":
      return "#f56c6c"; // 红色
    case "脱岗":
      return "#faad14"; // 黄色
    case "休息":
      return "#909399"; // 灰色
    default:
      return "#67c23a"; // 绿色（在岗）
  }
};

// 添加边界数据处理函数
// 根据选择的区县返回边界数据
const getBoundaryDataBySelection = () => {
  // 所有区县名称
  const allDistricts = [
    "翠屏区",
    "南溪区",
    "叙州区",
    "江安县",
    "长宁县",
    "高县",
    "珙县",
    "筠连县",
    "兴文县",
    "屏山县"
  ];

  // 使用appStore中的区县信息
  const selectedCounty = appStore.getLargeScreenArea?.county;

  // 如果选择了特定区县
  if (selectedCounty) {
    // 检查选择的区县是否在列表中
    if (allDistricts.includes(selectedCounty)) {
      return [
        {
          name: selectedCounty,
          coords: getDistrictBoundary(selectedCounty),
          ...getDistrictStyle(selectedCounty)
        }
      ];
    }
  }

  // 如果是宜宾市总体（没有选择具体区县），显示宜宾市整体边界
  return [
    {
      name: "宜宾市",
      coords: getYibinCityBoundary()[0],
      ...getYibinCityStyle()
    }
  ];
};
</script>

<style lang="scss">
.map-view {
  position: relative;
  height: 100%;

  #map-container {
    height: 100%;
    background: rgb(0 21 41 / 40%);
    transition: opacity 0.3s ease;
  }

  // 地图加载状态指示器
  .map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 24px;
    background: rgba(0, 24, 75, 0.9);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transform: translate(-50%, -50%);
    animation: fadeInScale 0.3s ease-out;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(0, 212, 255, 0.2);
      border-left: 3px solid #00d4ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 14px;
      color: #00d4ff;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
    }
  }

  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .map-legend {
    position: absolute;
    right: 20px;
    bottom: 20px;
    z-index: 1000;
    padding: 10px;
    background: rgb(0 21 41 / 70%);
    border-radius: 4px;

    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .legend-dot {
        width: 10px;
        height: 10px;
        margin-right: 8px;
        border-radius: 50%;
      }

      .legend-text {
        font-size: 14px; // 地图图例文字大小
        color: rgb(255 255 255 / 80%);
      }
    }
  }
}

.tech-notification {
  @keyframes warningBlink {
    0% {
      box-shadow: 0 0 15px rgb(255 85 0 / 10%);
    }

    50% {
      box-shadow: 0 0 25px rgb(255 85 0 / 20%);
    }

    100% {
      box-shadow: 0 0 15px rgb(255 85 0 / 10%);
    }
  }

  @keyframes warningShake {
    0%,
    100% {
      transform: rotate(0);
    }

    25% {
      transform: rotate(-8deg);
    }

    75% {
      transform: rotate(8deg);
    }
  }

  position: absolute;
  left: 10px;
  padding: 24px !important;
  margin: 8px !important;
  overflow: hidden;
  background: rgb(24 28 47 / 80%) !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgb(255 85 0 / 20%) !important;
  border-radius: 8px !important;
  box-shadow: 0 0 20px rgb(255 85 0 / 15%) !important;
  animation: warningBlink 3s ease-in-out infinite;

  .el-notification__group {
    margin: 0;
  }

  &::before {
    position: absolute;
    inset: 0;
    z-index: 0;
    content: "";
    background: linear-gradient(135deg, rgb(255 85 0 / 10%), transparent 60%);
  }

  .el-notification__title {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 8px;
    align-items: center;
    padding-bottom: 8px !important;
    margin-bottom: 16px !important;
    font-size: 24px !important;
    font-weight: 500 !important;
    color: #f50 !important;
    border-bottom: 1px solid rgb(255 255 255 / 10%) !important;

    i {
      font-size: 24px;
      color: #f50;
      animation: warningShake 1s ease-in-out infinite;
    }
  }

  .el-notification__content {
    position: relative;
    z-index: 1;
    font-size: 22px !important;
    color: rgb(255 255 255 / 90%) !important;
  }

  .alarm-content {
    padding: 12px 0;

    .alarm-title {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-bottom: 8px;
      font-size: 22px;
      font-weight: 500;
      color: rgb(255 255 255 / 95%);

      i {
        font-size: 22px;
        color: #f50;
        animation: warningShake 1s ease-in-out infinite;
      }
    }

    .alarm-info {
      position: relative;
      padding-left: 8px;
      margin-bottom: 5px;
      font-size: 20px;
      color: rgb(255 255 255 / 80%);
      transition: all 0.3s;
    }

    .alarm-tip {
      margin-top: 8px;
      font-size: 18px;
      color: #409eff;
      text-align: right;
      cursor: pointer;
      opacity: 0.8;
      transition: all 0.3s;
    }
  }
}
</style>
