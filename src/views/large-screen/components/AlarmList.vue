<template>
  <!-- 严重违法预警 -->
  <div class="alarm-list">
    <div ref="scrollRef" class="alarm-scroll">
      <div class="scroll-content">
        <div
          v-for="item in props.data"
          :key="item.id"
          class="alarm-item"
          @click="openAlarmDetail(item)"
        >
          <div class="alarm-content">
            <!-- 第一行1：时间（无年份）- 地点 -->
            <div class="alarm-header">
              <div class="alarm-time-location">
                {{ formatTimeWithoutYear(item.time) }} - {{ item.location }}
              </div>
            </div>
            <!-- 第二行：违法行为（红色） -->
            <div class="alarm-type-red">
              {{ item.type }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 报警详情对话框 -->
    <AlarmDetailDialog
      v-model:visible="detailDialogVisible"
      :alarm-data="selectedAlarm"
      :all-alarms="props.data"
      @refresh="refreshAlarmList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { AlarmItem } from "../types";
import AlarmDetailDialog from "../dialogs/AlarmDetailDialog.vue";

const props = defineProps<{
  data: AlarmItem[];
}>();

const emit = defineEmits<{
  (e: "refresh"): void;
}>();

const scrollRef = ref<HTMLElement>();
const detailDialogVisible = ref(false);
const selectedAlarm = ref<AlarmItem | null>(null);

// 格式化时间，去除年份
function formatTimeWithoutYear(time: string): string {
  // 假设时间格式为 'YYYY-MM-DD HH:mm:ss' 或 'YYYY/MM/DD HH:mm:ss'
  return time.replace(/^\d{4}[-\/]?/, "");
}

// 打开报警详情对话框
const openAlarmDetail = (item: AlarmItem) => {
  selectedAlarm.value = item;
  detailDialogVisible.value = true;
};

// 刷新报警列表
const refreshAlarmList = () => {
  emit("refresh");
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.alarm-list {
  height: 100%;
  overflow: auto;

  // 美化滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(0 234 255 / 30%);
    border-radius: 3px;

    &:hover {
      background: rgb(0 234 255 / 50%);
    }
  }

  &::-webkit-scrollbar-track {
    background: rgb(0 24 75 / 30%);
    border-radius: 3px;
  }

  .alarm-scroll {
    position: relative;
    height: 100%;
    overflow: auto;

    // 内层滚动条隐藏
    &::-webkit-scrollbar {
      display: none;
    }

    .scroll-content {
      padding: 12px;
    }

    .alarm-item {
      position: relative;
      height: auto;
      padding: 16px;
      margin-bottom: 12px;
      overflow: hidden;
      background: rgb(255 255 255 / 5%);
      border-bottom: 1px solid rgb(255 255 255 / 30%);
      border-radius: 8px;
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &.严重 {
        background: rgb(255 77 79 / 15%);
        border-left: 4px solid #ff4d4f;
      }

      &.一般 {
        background: rgb(250 173 20 / 15%);
        border-left: 4px solid #faad14;
      }

      &.轻微 {
        background: rgb(82 196 26 / 15%);
        border-left: 4px solid #52c41a;
      }

      .alarm-content {
        .alarm-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 8px;
          margin-bottom: 12px;
          border-bottom: 1px solid rgb(255 255 255 / 10%);

          .alarm-time-location {
            margin-bottom: 2px;
            @extend .fs-alarm-time; // 报警时间与地点
            font-weight: 400;
            color: #fff;
          }
        }

        .alarm-type-red {
          margin-top: 8px;
          margin-bottom: 4px;
          @extend .fs-alarm-type; // 违法行为类型
          font-weight: 500;
          color: #ff4d4f;
        }
      }

      &:hover {
        cursor: pointer;
        background: rgb(255 255 255 / 20%);
        box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
        transform: translateX(4px);
      }
    }
  }
}
</style>
