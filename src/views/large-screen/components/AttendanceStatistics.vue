<template>
  <!-- 上岗情况 -->
  <div class="attendance-statistics">
    <div class="h-full">
      <div class="stat-header">
        <div class="stat-title">
          <span style="cursor: pointer" @click="goToAdmin">上岗情况</span>
          <div class="stat-select-wrapper">
            <TimeRangeSelector
              model-value="day"
              :enabled-ranges="['day']"
              :custom-labels="{ day: '日' }"
              :disable-future="true"
              :initial-date="selectedDate"
              @date-change="handleDateChange"
            />
          </div>
        </div>
      </div>
      <div class="stat-content">
        <div class="stat-cards">
          <div class="stat-card" @click="handleCardClick('workHours')">
            <div class="card-title">勤务安排</div>
            <div class="card-value">
              {{ efficiencyStats.totalWorkHours }}小时
            </div>
          </div>
          <div class="stat-card" @click="handleCardClick('onDutyHours')">
            <div class="card-title">上岗时长</div>
            <div class="card-value">
              {{ efficiencyStats.totalOnDutyHours }}小时
            </div>
          </div>
          <div class="stat-card" @click="handleCardClick('onDutyRate')">
            <div class="card-title">上岗率</div>
            <div class="card-value">
              <span :class="getRateClass(efficiencyStats.avgOnDutyRate)">
                {{ efficiencyStats.avgOnDutyRate }}%
              </span>
            </div>
          </div>
        </div>
        <div ref="chartRef" class="stat-chart" />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <AttendanceDetailDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :table-data="dialogData"
      :default-date="selectedDate"
      :default-table-type="defaultTableType"
      @date-change="handleDialogDateChange"
    />

    <!-- 效率分析弹窗 -->
    <EfficiencyAnalysisDialog
      v-model:visible="efficiencyDialogVisible"
      :selected-date="selectedDate"
    />
  </div>
</template>

<script setup lang="ts">
import {
  getAttendance,
  getAttendanceAndWorkTimeByday
} from "@/api/largeScreen";
import { useAppStoreHook } from "@/store/modules/app";
import * as echarts from "echarts";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import AttendanceDetailDialog from "../dialogs/AttendanceDetailDialog.vue";
import EfficiencyAnalysisDialog from "../dialogs/EfficiencyAnalysisDialog.vue";
import type { EChartsOption } from "echarts";
import { useRouter } from "vue-router";
import TimeRangeSelector from "@/components/TimeRangeSelector.vue";

// 新的接口定义
interface AbnormalRecord {
  type: string;
  startTime: string;
  endTime: string;
  duration: string;
  description: string;
}

interface Shift {
  shiftName: string;
  startTime: string;
  endTime: string;
  status?: string;
  abnormalRecords?: AbnormalRecord[];
  staffCount?: number;
}

interface Staff {
  userId: number;
  userName: string;
  phone: string;
  deptName: string;
  status: string;
  shifts: Shift[];
}

interface Location {
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
}

interface Site {
  siteId: string;
  location: Location;
  shifts: Shift[];
  staff: Staff[];
  deviceFaultRecords?: Array<{
    equipmentNumber: string;
    ip: string;
    startTime: string;
    endTime: string;
    duration: string;
    description: string;
  }>;
  status: string;
}

interface StatusCount {
  onDuty: number;
  offDuty: number;
  rest: number;
  fault: number;
}

interface Statistics {
  totalSites: number;
  statusCount: StatusCount;
}

interface AttendanceData {
  date: string;
  sites: Site[];
  statistics: Statistics;
}

interface ApiResponse {
  code: number;
  msg: string;
  data: AttendanceData;
}

// 精准劝导处置效率分析接口数据结构
interface DisposalEfficiencyData {
  area_name: string; // 地区名称
  total_scheduled: number; // 总排班人数
  attended_count: number; // 实际出勤人数
  total_work_hours: string; // 总工作时长（小时）
  on_duty_hours: string; // 在岗时长（小时）
  overtime_hours: string; // 加班时长（小时）
}

// 组件状态
const loading = ref(false);
const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;
let pollingTimer: NodeJS.Timeout | null = null;

// 当前数据
const currentData = ref<AttendanceData>({
  date: "",
  sites: [],
  statistics: {
    totalSites: 0,
    statusCount: {
      onDuty: 0,
      offDuty: 0,
      rest: 0,
      fault: 0
    }
  }
});

// 精准劝导处置效率分析数据
const efficiencyData = ref<DisposalEfficiencyData[]>([]);

// 弹窗控制
const dialogVisible = ref(false);
const dialogTitle = ref("");
const dialogData = ref<any[]>([]);
const defaultTableType = ref("site"); // 新增：默认表格类型

// 效率分析弹窗控制
const efficiencyDialogVisible = ref(false);
const efficiencyDialogType = ref("");

// 选中的日期
const selectedDate = ref<string>(new Date().toISOString().split("T")[0]);

// 计算属性 - 统计数据
const totalSites = computed(() => currentData.value.statistics.totalSites || 0);
const statusCount = computed(
  () =>
    currentData.value.statistics.statusCount || {
      onDuty: 0,
      offDuty: 0,
      rest: 0,
      fault: 0
    }
);

// 计算属性 - 效率分析汇总数据
const efficiencyStats = computed(() => {
  if (!efficiencyData.value.length) {
    return {
      totalWorkHours: "0.0",
      totalOnDutyHours: "0.0",
      avgOnDutyRate: 0
    };
  }

  // 计算总工作时长
  const totalWorkHours = efficiencyData.value.reduce(
    (sum, item) => sum + parseFloat(item.total_work_hours || "0"),
    0
  );

  // 计算总在岗时长
  const totalOnDutyHours = efficiencyData.value.reduce(
    (sum, item) => sum + parseFloat(item.on_duty_hours || "0"),
    0
  );

  // 计算平均在岗率
  const avgOnDutyRate =
    totalWorkHours > 0 ? (totalOnDutyHours / totalWorkHours) * 100 : 0;

  return {
    totalWorkHours: totalWorkHours.toFixed(1),
    totalOnDutyHours: totalOnDutyHours.toFixed(1),
    avgOnDutyRate: Math.round(avgOnDutyRate)
  };
});

// 计算在岗率
const calculateOnDutyRate = (row: DisposalEfficiencyData) => {
  if (parseFloat(row.total_work_hours || "0") === 0) {
    return 0;
  }
  return (
    (parseFloat(row.on_duty_hours || "0") /
      parseFloat(row.total_work_hours || "0")) *
    100
  );
};

// 计算出勤率
const calculateAttendanceRate = (row: DisposalEfficiencyData) => {
  if (row.total_scheduled === 0) {
    return 0;
  }
  return (row.attended_count / row.total_scheduled) * 100;
};

// 获取数据方法
const fetchData = async (date?: string) => {
  loading.value = true;
  try {
    const selectedDateVal = date || selectedDate.value;
    const areaParams = useAppStoreHook().getLargeScreenArea;

    // 并行调用两个接口
    const [attendanceRes, efficiencyRes] = await Promise.all([
      getAttendance({
        date: selectedDateVal,
        ...areaParams
      }),
      getAttendanceAndWorkTimeByday({
        date: selectedDateVal,
        ...areaParams
      })
    ]);

    // 处理考勤数据
    if (attendanceRes.code === 200 && attendanceRes.data) {
      currentData.value = attendanceRes.data;
      updateChart();
    }

    // 处理效率分析数据
    if (efficiencyRes.code === 200 && efficiencyRes.data) {
      // 确保数据是数组形式
      const responseData = efficiencyRes.data;
      efficiencyData.value = Array.isArray(responseData)
        ? responseData
        : responseData.data || [responseData];
    } else {
      efficiencyData.value = [];
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    efficiencyData.value = [];
  } finally {
    loading.value = false;
  }
};

// 添加日期变化处理方法
const handleDateChange = async (val: string) => {
  if (val) {
    await fetchData(val);
  }
};

// 处理卡片点击
const handleCardClick = (type: string) => {
  efficiencyDialogType.value = type;
  efficiencyDialogVisible.value = true;
};

// 处理图表点击
const handleChartClick = (params: any) => {
  const { name } = params;
  dialogTitle.value = `${name}列表`;

  // 根据点击的状态设置默认表格类型
  switch (name) {
    case "在岗":
      defaultTableType.value = "on-duty";
      break;
    case "脱岗":
      defaultTableType.value = "off-duty";
      break;
    case "休息":
      defaultTableType.value = "rest";
      break;
    case "故障":
      defaultTableType.value = "fault";
      break;
    default:
      defaultTableType.value = "site";
      break;
  }

  // 不再过滤站点数据，让对话框组件内部处理数据过滤
  dialogData.value = [];
  dialogVisible.value = true;
};

// 添加对话框内日期变更处理方法
const handleDialogDateChange = async (val: string) => {
  // 更新选中的日期
  selectedDate.value = val;
  // 重新获取数据
  await fetchData(val);
};

// 更新图表
const updateChart = () => {
  if (!chart) {
    return;
  }

  // 获取状态统计数据
  const { onDuty, offDuty, rest, fault } =
    currentData.value.statistics.statusCount;

  const option: EChartsOption = {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ({d}%)",
      backgroundColor: "rgba(0, 24, 75, 0.9)",
      borderColor: "rgba(64, 158, 255, 0.2)",
      borderWidth: 1
    },
    legend: {
      orient: "vertical",
      right: "5%",
      top: "middle",
      textStyle: {
        color: "#fff",
        fontSize: 18
      }
    },
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        center: ["40%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: "rgba(0, 24, 75, 0.6)",
          borderWidth: 2
        },
        label: {
          show: true,
          position: "outside",
          color: "#fff",
          fontSize: 18,
          formatter: "{b}: {c}"
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: "rgba(255, 255, 255, 0.3)"
          }
        },
        data: [
          {
            value: onDuty,
            name: "在岗",
            itemStyle: {
              color: "#67C23A" // 绿色
            }
          },
          {
            value: offDuty,
            name: "脱岗",
            itemStyle: {
              color: "#E6A23C" // 黄色
            }
          },
          {
            value: rest,
            name: "休息",
            itemStyle: {
              color: "#909399" // 灰色
            }
          },
          {
            value: fault,
            name: "故障",
            itemStyle: {
              color: "#F56C6C" // 红色
            }
          }
        ]
      }
    ]
  };

  chart.setOption(option, true);

  // 重新绑定点击事件
  chart.off("click");
  chart.on("click", handleChartClick);
};

// 修改 updateCurrentDate 函数
const updateCurrentDate = () => {
  const now = new Date();
  const targetDate = now.toISOString().split("T")[0];

  // 如果日期不一致，则更新日期
  if (selectedDate.value !== targetDate) {
    selectedDate.value = targetDate;
    fetchData(targetDate);
  }
};

// 在 onMounted 中初始化
onMounted(async () => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    await fetchData();
    chart.on("click", handleChartClick);

    // 立即执行一次更新
    updateCurrentDate();
  }
  window.addEventListener("resize", () => chart?.resize());

  // 添加定时检查，每分钟检查一次是否需要更新日期
  const timer = setInterval(updateCurrentDate, 60000);

  onUnmounted(() => {
    clearInterval(timer);
  });
  // 轮询获取出勤情况
  pollingTimer = setInterval(() => {
    fetchData();
  }, 60000);
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener("resize", () => chart?.resize());
  if (pollingTimer) {
    clearInterval(pollingTimer);
  }
});

// 添加区域变化监听
watch(
  () => useAppStoreHook().largeScreenArea,
  () => {
    if (useAppStoreHook().largeScreenArea) {
      fetchData();
    }
  },
  { immediate: true }
);

const router = useRouter();

function goToAdmin() {
  // 跳转到考勤列表页面
  router.push("/attendance-management");
}

// 判断是否为当天日期
const isToday = (date: Date) => {
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

// 根据比率获取样式类
const getRateClass = (rate: number) => {
  if (rate >= 80) return "rate-high";
  if (rate >= 60) return "rate-medium";
  return "rate-low";
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";

// 在岗率样式
.rate-high {
  color: #67c23a;
}

.rate-medium {
  color: #e6a23c;
}

.rate-low {
  color: #f56c6c;
}

// 弹窗容器
.attendance-statistics {
  height: 100%;
  padding: 16px;

  .stat-header {
    margin-bottom: 16px;

    .stat-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      span {
        position: relative;
        padding-left: 12px;
        @extend .fs-component-title; // 标题
        font-weight: 500;
        color: #fff;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          color: #409eff;
          transform: translateX(2px);
        }

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 16px;
          content: "";
          background: #409eff;
          border-radius: 2px;
          transition: all 0.3s;
          transform: translateY(-50%);
        }

        &:hover::before {
          height: 20px;
          background: #66b1ff;
        }
      }

      .stat-select-wrapper {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: flex-end;
        margin-left: 20px;

        :deep(.el-date-editor) {
          width: 180px;

          .el-input__wrapper {
            height: 40px;
            padding: 0 12px;
            background: rgb(0 24 75 / 60%) !important;
            border: 1px solid rgb(64 158 255 / 30%) !important;
            border-radius: 6px;
            box-shadow: 0 0 12px rgb(64 158 255 / 40%) !important;
            transition: all 0.3s;

            &:hover {
              background: rgb(2 6 18 / 90%) !important;
              border-color: rgb(64 158 255 / 80%) !important;
              box-shadow: 0 0 15px rgb(64 158 255 / 60%) !important;
            }

            &.is-focus {
              border-color: #409eff !important;
              box-shadow: 0 0 18px rgb(64 158 255 / 50%) !important;
            }
          }

          :deep(.el-input__inner) {
            height: 46px;
            font-size: 18px !important;
            font-weight: 500 !important;
            color: #fff !important;
            text-shadow: 0 0 5px rgb(255 255 255 / 60%) !important;
            letter-spacing: 0.8px !important;
          }

          .calendar-icon {
            font-size: 20px;
            color: #409eff !important;
            filter: drop-shadow(0 0 3px rgb(64 158 255 / 60%)) !important;
          }
        }
      }
    }
  }

  .stat-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: calc(100% - 48px);

    .stat-cards {
      display: flex;
      gap: 8px;

      .stat-card {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px 6px;
        overflow: hidden;
        background: linear-gradient(
          135deg,
          rgb(0 24 75 / 40%) 0%,
          rgb(0 24 75 / 20%) 100%
        );
        backdrop-filter: blur(4px);
        border: 1px solid rgb(64 158 255 / 20%);
        border-radius: 6px;
        transition: all 0.3s ease;

        &:nth-child(1) {
          border-left: 4px solid #409eff; // 蓝色 - 勤务安排
        }

        &:nth-child(2) {
          border-left: 4px solid #67c23a; // 绿色 - 在岗时长
        }

        &:nth-child(3) {
          border-left: 4px solid #e6a23c; // 黄色 - 在岗率
        }

        .card-title {
          margin-bottom: 4px;
          font-size: 18px;
          color: rgb(255 255 255 / 70%);
        }

        .card-value {
          font-size: 24px;
          font-weight: 500;
          color: #fff;
        }

        &:hover {
          border-color: rgb(64 158 255 / 40%);
          transform: translateY(-2px);
        }
      }
    }

    .stat-chart {
      flex: 1;
      min-height: 180px;
      background: rgb(0 24 75 / 30%);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 8px;
    }
  }
}

:deep(.attendance-detail-date-picker) {
  background-color: rgb(0 24 75 / 90%) !important;
  border: 1px solid rgb(64 158 255 / 30%) !important;

  .el-picker-panel__content {
    .custom-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      transition: all 0.3s;
    }

    .today-cell {
      font-weight: bold !important;
      color: white !important;
      background-color: #409eff !important;
      box-shadow: 0 0 8px rgb(64 158 255 / 80%);
    }
  }
}

// 为统计卡片添加点击效果
.stat-card {
  cursor: pointer;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  }
}
</style>
