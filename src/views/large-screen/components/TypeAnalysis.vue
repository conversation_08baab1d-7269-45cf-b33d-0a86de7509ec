<template>
  <!-- 违法类型占比 -->
  <div ref="chartRef" class="type-analysis-chart" @click="handleChartClick" />
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted, onUnmounted, ref, watch, computed } from "vue";
import { useRouter } from "vue-router";
import dayjs from "dayjs";

interface ViolationType {
  name: string;
  displayName: string;
  value: number;
  itemStyle: {
    color: string;
  };
}

interface ChartData {
  types: ViolationType[];
}

const props = defineProps<{
  data: ChartData;
  // 大屏的时间状态
  timeMode?: "day" | "month";
  currentDate?: string;
  currentMonth?: string;
}>();

const emit = defineEmits<{
  (
    e: "open-dialog",
    data: {
      timeMode: "day" | "month" | "year";
      currentDate: string;
      currentMonth: string;
    }
  ): void;
}>();

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

const router = useRouter();

// 饼图配置
const pieOption = computed(() => ({
  tooltip: {
    show: true,
    trigger: "item",
    formatter: function (params: any) {
      // 获取显示名称
      const item = props.data.types.find(t => t.name === params.name);
      const displayName = item ? item.displayName : params.name;

      return `
        <div style="padding: 6px 10px; background: rgba(0, 24, 75, 0.8); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 4px;">
          <div style="color: #fff; font-size: 16px;">
            <span style="opacity: 0.7;">${displayName}：</span>
            <span>${params.value}</span>
            <span style="margin-left: 4px; opacity: 0.7;">次</span>
            <span style="margin-left: 8px; opacity: 0.7;">(${params.percent}%)</span>
          </div>
        </div>
      `;
    },
    backgroundColor: "transparent",
    borderWidth: 0,
    padding: 0
  },
  legend: {
    orient: "vertical",
    right: "0%",
    top: "middle",
    width: "200",
    itemWidth: 25,
    itemHeight: 14,
    textStyle: {
      color: "rgba(255, 255, 255, 0.7)",
      fontSize: 16, // 图例文字大小
      padding: [0, 0, 0, 10],
      lineHeight: 20,
      width: 160,
      overflow: "break"
    },
    selectedMode: false,
    formatter: function (name: string) {
      const item = props.data.types.find(t => t.name === name);
      return item ? item.displayName : name;
    }
  },
  series: [
    {
      type: "pie",
      radius: ["30%", "55%"],
      center: ["30%", "50%"],
      avoidLabelOverlap: false,
      label: {
        show: false
      },
      emphasis: {
        scale: true,
        scaleSize: 3
      },
      labelLine: {
        show: false
      },
      data: props.data.types
    }
  ]
}));

const renderChart = () => {
  if (!chartRef.value) return;

  if (!chart) {
    chart = echarts.init(chartRef.value);
  }

  chart.setOption(pieOption.value);
};

// 监听数据变化
watch(
  () => props.data.types,
  () => {
    renderChart();
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize();
};

window.addEventListener("resize", handleResize);

// 添加点击事件处理
const handleChartClick = () => {
  emit("open-dialog", {
    timeMode: props.timeMode || "day",
    currentDate: props.currentDate || dayjs().format("YYYY-MM-DD"),
    currentMonth: props.currentMonth || dayjs().format("YYYY-MM")
  });
};

onMounted(() => {
  renderChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.type-analysis-chart {
  width: 100%;
  height: 100%;
}
</style>
