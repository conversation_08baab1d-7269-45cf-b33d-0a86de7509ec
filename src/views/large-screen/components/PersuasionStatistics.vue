<template>
  <!-- 履职情况 -->
  <div class="persuasion-statistics">
    <div class="stat-header">
      <div
        class="stat-title"
        style="cursor: pointer"
        @click="handlePersuasionClick"
      >
        履职情况
      </div>
      <div class="time-picker-group" @click.stop>
        <TimeRangeSelector
          :model-value="mode === 'date' ? 'day' : 'month'"
          :enabled-ranges="['day', 'month']"
          :custom-labels="{ day: '日', month: '月' }"
          :disable-future="true"
          :initial-date="dateValue"
          :initial-month="monthValue"
          @change="handleModeChange"
          @date-change="handleDateChange"
          @month-change="handleMonthChange"
        />
      </div>
    </div>
    <div ref="chartRef" class="stat-chart" @click="chartWrapperClick" />
  </div>
</template>

<script setup lang="ts" name="PersuasionStatistics">
import { getPersuasionStatsData } from "@/api/largeScreen";
import { useAppStoreHook } from "@/store/modules/app";
import TimeRangeSelector from "@/components/TimeRangeSelector.vue";
import dayjs from "dayjs";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

// 新的时间选择器状态管理
const mode = ref<"date" | "month">("date");
const dateValue = ref(new Date().toISOString().split("T")[0]);
const monthValue = ref(
  `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}`
);

// 履职情况默认展示今日数据 (保留兼容)
const timeRange = ref("day");

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;

const sortType = ref("violations");

// 添加定时器变量
let pollingTimer: NodeJS.Timeout | null = null;

const handlePersuasionClick = () => {
  router.push({
    path: "/illegal/list",
    query: { from: "largeScreen" }
  });
};

interface ViolationType {
  type: string;
  totalCount: number;
  persuadedCount: number;
  persuasionRate?: string;
  effectiveCount: number;
}

interface GroupData {
  name: string;
  totalViolations: number;
  totalPersuaded: number;
  totalEffective: number;
  details: Map<string, ViolationType>;
}

const chartWrapperClick = (event: MouseEvent) => {
  const chartContainer = chartRef.value;
  if (!chartContainer) return;

  const rect = chartContainer.getBoundingClientRect();
  const safeArea = 60;
  const clickY = event.clientY - rect.top;
  const containerHeight = rect.height;

  if (clickY > containerHeight - safeArea) {
    return;
  }

  emit("update:visible", true);
};

// 新的时间选择器处理函数
const handleModeChange = (newMode: "day" | "month") => {
  mode.value = newMode === "day" ? "date" : newMode;
  if (newMode === "day") {
    timeRange.value = "day";
  } else {
    timeRange.value = "month";
  }
  fetchData();
};

const handleDateChange = (date: string) => {
  dateValue.value = date;
  mode.value = "date";
  timeRange.value = "day";
  fetchData();
};

const handleMonthChange = (month: string) => {
  monthValue.value = month;
  mode.value = "month";
  timeRange.value = "month";
  fetchData();
};

const renderChart = (chartData: any[]) => {
  if (!chartRef.value) return;

  if (!chart) {
    chart = echarts.init(chartRef.value);
  }

  // 处理空数据情况
  if (!chartData || chartData.length === 0) {
    const emptyOption: EChartsOption = {
      title: {
        show: true,
        text: "暂无数据",
        left: "center",
        top: "center",
        textStyle: {
          color: "rgba(255, 255, 255, 0.8)",
          fontSize: 20, // 空数据状态提示文字字体大小
          fontWeight: "normal"
        }
      },
      tooltip: {
        show: false
      },
      legend: {
        show: false
      },
      grid: {
        show: false
      },
      xAxis: {
        show: false
      },
      yAxis: {
        show: false
      },
      series: []
    };

    chart.clear(); // 清除之前的图表
    chart.setOption(emptyOption, true); // 使用 true 参数完全覆盖之前的配置
    return;
  }

  const finalData = chartData.map(item => {
    return {
      name: item.display,
      totalViolations: item.count,
      totalPersuaded: item.persuaded_count,
      totalEffective: item.effective_count,
      overallPersuasionRate: item.persuasion_rate
    };
  });
  const xAxisNames = finalData.map(item => item.name);

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      backgroundColor: "rgba(0, 10, 30, 0.85)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      borderWidth: 1,
      padding: [6, 8], // 减小内边距，让tooltip更紧凑
      textStyle: {
        color: "#fff",
        fontSize: 12 // 减小字体大小，让tooltip更小巧
      },
      extraCssText:
        "box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); border-radius: 4px;",
      // 优化tooltip位置，防止被遮挡
      confine: true, // 限制tooltip在图表容器内
      position: function (point, params, dom, rect, size) {
        // 智能位置计算，防止tooltip超出边界
        const [mouseX, mouseY] = point;
        const { contentSize, viewSize } = size;
        const [tooltipWidth, tooltipHeight] = contentSize;
        const [containerWidth, containerHeight] = viewSize;

        let x = mouseX + 10; // 默认在鼠标右侧
        let y = mouseY - tooltipHeight / 2; // 垂直居中

        // 检查右边界，如果超出则显示在左侧
        if (x + tooltipWidth > containerWidth) {
          x = mouseX - tooltipWidth - 10;
        }

        // 检查上下边界
        if (y < 0) {
          y = 10;
        } else if (y + tooltipHeight > containerHeight) {
          y = containerHeight - tooltipHeight - 10;
        }

        return [x, y];
      },
      formatter: function (params: any) {
        const locationData = finalData[params[0].dataIndex];
        return `
          <div style="padding: 3px">
            <div style="margin-bottom: 4px;font-weight:bold;font-size:12px;color:#fff;border-bottom:1px solid rgba(255,255,255,0.1);padding-bottom:4px">
              ${locationData.name}
            </div>

            <div>
              <div style="display:flex;font-size:11px;justify-content:space-between;margin-bottom:2px;line-height:1.2">
                <span>违法总数：</span>
                <span style="font-weight:bold;color:#409EFF">${locationData.totalViolations}</span>
              </div>
              <div style="display:flex;font-size:11px;justify-content:space-between;margin-bottom:2px;line-height:1.2">
                <span>已劝导：</span>
                <span style="font-weight:bold;color:#67C23A">${locationData.totalPersuaded}</span>
              </div>
              <div style="display:flex;font-size:11px;justify-content:space-between;margin-bottom:2px;line-height:1.2">
                <span>有效劝导：</span>
                <span style="font-weight:bold;color:#67C23A">${locationData.totalEffective}</span>
              </div>
              <div style="display:flex;font-size:11px;justify-content:space-between;line-height:1.2">
                <span>劝导率：</span>
                <span style="font-weight:bold;color:#E6A23C">${locationData.overallPersuasionRate}%</span>
              </div>
            </div>
          </div>
        `;
      }
    },
    legend: {
      data: ["违法数量", "已劝导", "有效劝导", "劝导率"],
      textStyle: {
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 16 // 图表顶部图例(legend)文字字体大小
      },
      top: 10,
      itemWidth: 15,
      itemHeight: 15,
      itemGap: 30,
      padding: [0, 0, 5, 0],
      selectedMode: false
    },
    grid: {
      top: 60,
      bottom: 30,
      left: 10,
      right: 10,
      containLabel: true
    },
    dataZoom: [
      {
        type: "inside",
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false
      },
      {
        type: "slider",
        start: 0,
        end: 100,
        bottom: 10,
        height: 20,
        handleStyle: {
          color: "#409eff"
        },
        fillerColor: "rgba(64, 158, 255, 0.2)",
        backgroundColor: "rgba(0, 0, 0, 0.3)",
        borderColor: "rgba(64, 158, 255, 0.3)",
        textStyle: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 10 // 图表底部滑块(dataZoom)文字字体大小
        }
      }
    ],
    xAxis: {
      type: "category",
      data: xAxisNames,
      axisLabel: {
        interval: 0,
        rotate: 35,
        color: "rgba(255, 255, 255, 0.7)",
        fontSize: 14, // X轴底部地区名称标签字体大小
        width: 100,
        overflow: "truncate",
        formatter: function (value) {
          return value.length > 12 ? value.slice(0, 6) + "..." : value;
        }
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.2)"
        }
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: "rgba(255, 255, 255, 0.2)"
        }
      }
    },
    yAxis: [
      {
        type: "value",
        name: "数量",
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          padding: [0, 30, 0, 0]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "rgba(255, 255, 255, 0.5)" // 增加Y轴线的透明度，使其更明显
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: "rgba(255, 255, 255, 0.5)"
          }
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)"
          }
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 14, // Y轴左侧数值刻度标签字体大小
          fontFamily: "DINPro"
        }
      }
    ],
    series: [
      {
        name: "违法数量",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#F56C6C" },
            { offset: 1, color: "rgba(245, 108, 108, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        data: finalData.map(item => item.totalViolations),
        label: {
          show: true,
          position: "top",
          distance: 4,
          color: "#F56C6C",
          fontSize: 14, // 违法数量柱状图顶部数值标签字体大小
          fontWeight: 500
        }
      },
      {
        name: "已劝导",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#409EFF" },
            { offset: 1, color: "rgba(64, 158, 255, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        data: finalData.map(item => item.totalPersuaded),
        label: {
          show: true,
          position: "top",
          distance: 4,
          color: "#409EFF",
          fontSize: 14, // 已劝导柱状图顶部数值标签字体大小
          fontWeight: 500
        }
      },
      {
        name: "有效劝导",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#67C23A" },
            { offset: 1, color: "rgba(103, 194, 58, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        data: finalData.map(item => item.totalEffective),
        label: {
          show: true,
          position: "top",
          distance: 4,
          color: "#67C23A",
          fontSize: 14, // 有效劝导柱状图顶部数值标签字体大小
          fontWeight: 500
        }
      },
      {
        name: "劝导率",
        type: "bar",
        barWidth: "15%",
        barGap: "0%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#E6A23C" },
            { offset: 1, color: "rgba(230, 162, 60, 0.3)" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: "top",
          formatter: "{c}%",
          color: "#E6A23C",
          fontSize: 14, // 劝导率柱状图顶部数值标签字体大小
          distance: 4,
          fontWeight: 500
        },
        data: finalData.map(item => parseFloat(item.overallPersuasionRate))
      }
    ]
  };

  chart.clear(); // 清除之前的图表配置
  chart.setOption(option, true); // 使用 true 参数完全覆盖之前的配置，确保Y轴正确显示
};

const fetchData = async () => {
  try {
    const params = {
      ...useAppStoreHook().getLargeScreenArea,
      startDate: "",
      endDate: ""
    };

    if (timeRange.value === "day") {
      // 使用用户选择的日期
      params.startDate = dateValue.value;
      params.endDate = dateValue.value;
    } else {
      // 使用用户选择的月份，计算该月的开始和结束日期
      const monthStart = dayjs(monthValue.value)
        .startOf("month")
        .format("YYYY-MM-DD");
      const monthEnd = dayjs(monthValue.value)
        .endOf("month")
        .format("YYYY-MM-DD");
      params.startDate = monthStart;
      params.endDate = monthEnd;
    }

    const res = await getPersuasionStatsData(params);
    if (res.code === 200) {
      renderChart(res.data);
    }
  } catch (error) {
    console.error("获取违法劝导率数据失败:", error);
  }
};

watch(
  () => useAppStoreHook().largeScreenArea,
  () => {
    if (useAppStoreHook().largeScreenArea) {
      fetchData();
    }
  }
);

const handleResize = () => {
  chart?.resize();
};

window.addEventListener("resize", handleResize);

onMounted(() => {
  if (!chart) {
    chart = echarts.init(chartRef.value);
    chart.on("datazoom", function (params) {
      const option = chart!.getOption();
      const dataZoom = option.dataZoom![0];

      if (dataZoom.end! - dataZoom.start! < 10) {
        chart!.setOption({
          dataZoom: [
            {
              ...dataZoom,
              start: dataZoom.end! - 10,
              end: dataZoom.end
            }
          ]
        });
      }
    });
  }
  fetchData();

  // 添加定时任务，每60秒刷新一次数据
  pollingTimer = setInterval(() => {
    fetchData();
  }, 60000);
});

onUnmounted(() => {
  chart?.dispose();
  window.removeEventListener("resize", handleResize);
  // 清理定时器
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
});
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";
.persuasion-statistics {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;

  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .stat-title {
      position: relative;
      padding-left: 12px;
      @extend .fs-component-title; /* 组件标题"履职情况"字体大小 */
      font-weight: 500;
      color: #fff;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
        transform: translateX(2px);
      }

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: #409eff;
        border-radius: 2px;
        transition: all 0.3s;
        transform: translateY(-50%);
      }

      &:hover::before {
        height: 20px;
        background: #66b1ff;
      }
    }
  }

  .stat-chart {
    flex: 1;
    min-height: 0;
    cursor: pointer;
  }

  .time-picker-group {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-left: 20px;

    // picker-toggle 样式已移除，现在使用 TimeRangeSelector 组件
  }
}
</style>
