export interface TrendItem {
  date: string;
  value: number;
  details: ViolationType[];
}

export interface ViolationType {
  name: string;
  value: number;
  itemStyle: {
    color: string;
  };
}

export interface OverviewData {
  total: number;
  handled: number;
  rate: number;
  trend: TrendItem[];
}

/** 实时报警项接口 */
export interface AlarmItem {
  id: number;
  wfUuid?: string; // 报警唯一标识
  type: string; // 报警类型
  location: string; // 报警地点
  time: string; // 报警时间
  level: string; // 报警等级
  status?: string; // 处理状态（可选）
  details?: string; // 详细信息（可选）
}

/** 高发地段排名项接口 */
export interface RankItem {
  name: string;
  value: number;
  trend?: number[];
}

/** 统计数据接口 */
export interface StatisticsData {
  rate: number;
  trend: {
    dates: string[];
    values: number[];
  };
}

/** 组件 Props 类型定义 */
export interface AttendanceStatisticsProps {
  data: StatisticsData;
}

export interface PersuasionStatisticsProps {
  data: StatisticsData;
}

export interface HotspotRankProps {
  data: RankItem[];
}

// 手动报警项
export interface ManualAlarm {
  id: number;
  // 报警时间
  eventTime: string;
  // 报警内容
  content: string;
  // 报警等级(一般，轻微，严重)
  degree: string;
  // 报警类型
  targetType: string;
  // 是否已确认
  know: string;
  // 创建时间
  createTime: string;
}
