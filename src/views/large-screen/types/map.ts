export interface Device {
  id: number;
  name: string;
  type: string;
  status: number;
  streamKey: string;
  maintainerPhone: string;
  equipmentNumber: string;
}

export interface Shift {
  shiftName: string;
  startTime: string;
  endTime: string;
  status: string;
  statusText: string;
  leavePostDetails?: {
    startTime: string;
    endTime: string;
    duration: number;
    status: string;
    staffName: string;
  }[];
}

export interface Schedule {
  shifts: Shift[];
  userName: string;
  userId: number;
}

export interface Staff {
  deptName: string;
  site: string;
  user_id: number;
  phone: string;
  city: string;
  hamlet: string;
  name: string;
  county: string;
  township: string;
  status: string;
}

export interface StaffInfo {
  id: string;
  name: string;
  phone: string;
  deptName: string;
  status: string;
  currentShift?: {
    id: string;
    name: string;
    startTime: string;
    endTime: string;
  };
}

interface AttendanceSummary {
  sites: Array<{
    id: number;
    name: string;
    staff: Array<{
      id: number;
      name: string;
      phone: string;
      deptName: string;
      status: string;
      currentShift?: {
        id: string;
        name: string;
        startTime: string;
        endTime: string;
      };
    }>;
  }>;
}

export interface PointData {
  site: string;
  address: string;
  city: string;
  county: string;
  township: string;
  hamlet: string;
  devices: Device[];
  schedules: Schedule[];
  staff: Staff[];
  status: string;
  attendance?: {
    summaryAM: AttendanceSummary;
    summaryPM: AttendanceSummary;
  };
}

export interface Point {
  id: number;
  coordinates: [string, string];
  data: PointData;
}
