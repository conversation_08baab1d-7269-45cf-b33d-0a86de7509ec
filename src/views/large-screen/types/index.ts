import type { DefineComponent } from "vue";

// 实时报警数据类型
export interface AlarmItem {
  id: number;
  type: string;
  location: string;
  time: string;
  status: string;
  level: string;
}

// 违法概览数据类型
export interface OverviewData {
  total: number;
  handled: number;
  rate: number;
  trend: Array<{
    date: string;
    value: number;
  }>;
}

// 设备概览数据类型
export interface DeviceData {
  total: number;
  online: number;
  offline: number;
  types: Array<{
    name: string;
    value: number;
  }>;
}

// 违法类型分析数据类型
export interface TypeItem {
  type: string;
  value: number;
  percent: number;
}

// 高发地段排名数据类型
export interface RankItem {
  name: string;
  value: number;
  trend: number[];
}

// 预警处置统计数据类型
export interface HandleData {
  total: number;
  handled: number;
  pending: number;
  rate: number;
  trend: {
    warning: number[];
    handled: number[];
  };
}

// 定义每个组件的 Props 类型
export interface AlarmListProps {
  data: AlarmItem[];
}

export interface IllegalOverviewProps {
  data: OverviewData;
}

export interface DeviceOverviewProps {
  data: DeviceData;
}

export interface TypeAnalysisProps {
  data: TypeItem[];
}

export interface HotspotRankProps {
  data: RankItem[];
}

export interface HandleStatisticsProps {
  data: HandleData;
}

// 定义组件类型
export type PanelComponentType =
  | DefineComponent<AlarmListProps>
  | DefineComponent<IllegalOverviewProps>
  | DefineComponent<DeviceOverviewProps>
  | DefineComponent<TypeAnalysisProps>
  | DefineComponent<HotspotRankProps>
  | DefineComponent<HandleStatisticsProps>;

// 定义面板接口
export interface Panel {
  title: string;
  component: PanelComponentType;
  data:
    | AlarmItem[]
    | OverviewData
    | DeviceData
    | TypeItem[]
    | RankItem[]
    | HandleData;
}
