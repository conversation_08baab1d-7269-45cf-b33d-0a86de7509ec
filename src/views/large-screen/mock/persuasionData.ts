// 违法类型列表
const violationTypes = [
  "未佩戴头盔",
  "闯红灯",
  "违规停车",
  "逆行",
  "超速行驶",
  "违规载人",
  "违规超车",
  "不按导向行驶"
];

// 地区列表
const areas = [
  {
    city: "宜宾市",
    districts: [
      {
        name: "翠屏区",
        towns: [
          "南岸镇",
          "西郊镇",
          "北城街道",
          "东城街道",
          "李庄镇",
          "菜坝镇",
          "金坪镇",
          "宋家镇",
          "牟坪镇",
          "大观楼街道"
        ]
      },
      {
        name: "叙州区",
        towns: [
          "观音镇",
          "南广镇",
          "柏溪镇",
          "高场镇",
          "喜捷镇",
          "商州镇",
          "泥溪镇",
          "大树镇",
          "龙池镇",
          "蕨溪镇"
        ]
      },
      {
        name: "三江新区",
        towns: [
          "双城街道",
          "长江街道",
          "黄桷街道",
          "园区街道",
          "白沙湾街道",
          "三江街道",
          "临港街道"
        ]
      }
    ]
  }
];

// 生成随机数
const random = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1) + min);
};

// 生成随机地点
const generateLocation = () => {
  const area = areas[0];
  const district = area.districts[random(0, area.districts.length - 1)];
  const town = district.towns[random(0, district.towns.length - 1)];
  const community = `${town}${random(1, 10)}社区`;
  const site = `${community}劝导站`;

  return `${area.city}-${district.name}-${town}-${community}-${site}`;
};

// 生成随机违法类型数据
const generateDetails = () => {
  const count = random(3, 6); // 每个地点3-6种违法类型
  const types = [...violationTypes]
    .sort(() => Math.random() - 0.5)
    .slice(0, count);

  return types.map(type => {
    const totalCount = random(50, 500);
    const persuadedCount = random(0, totalCount);
    const persuasionRate = ((persuadedCount / totalCount) * 100).toFixed(2);

    return {
      type,
      totalCount,
      persuadedCount,
      persuasionRate
    };
  });
};

// 生成模拟数据
export const generateMockData = (count = 100) => {
  return Array.from({ length: count }, () => {
    const details = generateDetails();
    const totalViolations = details.reduce(
      (sum, item) => sum + item.totalCount,
      0
    );
    const totalPersuaded = details.reduce(
      (sum, item) => sum + item.persuadedCount,
      0
    );
    const overallPersuasionRate = (
      (totalPersuaded / totalViolations) *
      100
    ).toFixed(2);

    return {
      location: generateLocation(),
      totalViolations,
      totalPersuaded,
      overallPersuasionRate,
      details: details.map(detail => ({
        type: detail.type,
        totalCount: detail.totalCount,
        persuadedCount: detail.persuadedCount,
        persuasionRate: detail.persuasionRate
      }))
    };
  });
};

export const mockPersuasionData = generateMockData(1000);
export const mockPersuasionDayData = [
  {
    date: "2025-01-17",
    totalPersuaded: 0,
    site: "",
    city: "宜宾市",
    hamlet: "",
    county: "",
    totalViolations: 0,
    overallPersuasionRate: "0.00",
    details: [],
    township: ""
  },
  {
    date: "2025-01-18",
    totalPersuaded: 0,
    site: "",
    city: "宜宾市",
    hamlet: "",
    county: "",
    totalViolations: 12379,
    overallPersuasionRate: "0.00",
    details: [
      {
        persuasionRate: "0.00",
        persuadedCount: 0,
        type: "未佩戴头盔",
        totalCount: 3334
      },
      {
        persuasionRate: "0.00",
        persuadedCount: 0,
        type: "加装遮阳伞",
        totalCount: 6056
      },
      {
        persuasionRate: "0.00",
        persuadedCount: 0,
        type: "超员",
        totalCount: 2983
      },
      {
        persuasionRate: "0.00",
        persuadedCount: 0,
        type: "严重超员",
        totalCount: 5
      },
      {
        persuasionRate: "0.00",
        persuadedCount: 0,
        type: "超员+未加装遮阳伞",
        totalCount: 1
      }
    ],
    township: ""
  }
];
