export interface Device {
  id: number;
  name: string;
  status: number; // 改为数字类型，0表示离线，1表示在线
  type: string;
}

export interface ViolationType {
  type: string;
  count: number;
  handled: number;
}

export interface Point {
  id: number;
  coordinates: [string, string]; // 改为字符串数组，因为后端返回的是字符串
  data: {
    address: string;
    totalViolations: number; // 违法总数
    dispatchedCount: number; // 已下派数量
    devices: Device[];
    staff: {
      total: number; // 总人数
      onDuty: number; // 已上岗
      waiting: number; // 待上岗
      abnormal: number; // 异常人数
    };
    violationStats: ViolationType[]; // 添加违法类型统计
  };
}

export interface MapPoint extends Point {} // 为了保持兼容性

export const mockPoints: Point[] = [
  {
    id: 1,
    coordinates: ["104.62", "28.766"],
    data: {
      address: "翠屏区观音路与长春路交叉口",
      totalViolations: 156,
      dispatchedCount: 89,
      devices: [
        {
          id: 1,
          name: "球机1号",
          status: 0,
          type: "球机"
        },
        {
          id: 2,
          name: "枪机1号",
          status: 1,
          type: "枪机"
        }
      ],
      staff: {
        total: 8,
        onDuty: 5, // 已上岗
        waiting: 2, // 待上岗
        abnormal: 1 // 异常
      },
      violationStats: [
        {
          type: "超载",
          count: 56,
          handled: 32
        },
        {
          type: "闯红灯",
          count: 43,
          handled: 28
        },
        {
          type: "未戴头盔",
          count: 38,
          handled: 25
        },
        {
          type: "违停",
          count: 19,
          handled: 15
        }
      ]
    }
  },
  {
    id: 2,
    coordinates: ["104.541", "28.69"],
    data: {
      address: "叙州区南广路与蜀南大道交叉口",
      totalViolations: 98,
      dispatchedCount: 45,
      devices: [],
      staff: {
        total: 0,
        onDuty: 0,
        waiting: 0,
        abnormal: 0
      },
      violationStats: []
    }
  },
  {
    id: 3,
    coordinates: ["104.969", "28.845"],
    data: {
      address: "南溪区长江大道与滨江路交叉口",
      totalViolations: 78,
      dispatchedCount: 56,
      devices: [],
      staff: {
        total: 0,
        onDuty: 0,
        waiting: 0,
        abnormal: 0
      },
      violationStats: []
    }
  }
];
