import { onMounted, reactive, ref, type Ref } from "vue";
import { getAllEmployees } from "@/api/attendance";
import dayjs from "dayjs";
import { getPersuaders } from "@/api/schedule";

export function useAttendance(_tableRef: Ref) {
  const filterParams = reactive({
    userName: "",
    dateRange: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]
  });
  const dataList = ref([]);
  const loading = ref(false);
  const selectedIds = ref([]);

  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1
  });

  const columns = [
    { label: "员工姓名", prop: "userName", width: 120 },
    { label: "职位名称", prop: "deptName", width: 120 },
    { label: "班次名称", prop: "shiftName", width: 120 },
    { label: "排班日期", prop: "scheduleDate", width: 160 },
    { label: "上班时间", prop: "startTime", width: 100 },
    { label: "下班时间", prop: "endTime", width: 100 },
    { label: "工作状态", prop: "workStatus", width: 100 },
    { label: "签到时间", prop: "attendance.checkInTime", width: 160 },
    { label: "签退时间", prop: "attendance.checkOutTime", width: 160 },
    { label: "考勤状态", prop: "attendance.status", width: 100 },
    { label: "备注", prop: "attendance.remark", width: 200 }
  ];

  onMounted(() => {
    loadEmployees();
  });

  const loadEmployees = () => {
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize
    };

    getPersuaders(params).then(res => {
      if (res.code === 200) {
        dataList.value = res.data.records.map(item => {
          const siteFullName = [
            item.city,
            item.county,
            item.township,
            item.hamlet,
            item.site
          ]
            .filter(Boolean) // 过滤掉空值
            .join("-"); // 使用 '-' 连接
          return {
            ...item,
            siteFullName
          };
        });
        pagination.total = res.data.total;
      }
    });
  };

  const onSearch = () => {
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: filterParams.userName
    };
    getAllEmployees(params)
      .then(res => {
        if (res.code === 200) {
          dataList.value = res.data.records.map(item => {
            const siteFullName = [
              item.city,
              item.county,
              item.township,
              item.hamlet,
              item.site
            ]
              .filter(Boolean) // 过滤掉空值
              .join("-"); // 使用 '-' 连接
            return {
              ...item,
              siteFullName
            };
          });
          pagination.total = res.data.total;
        } else {
          dataList.value = [];
        }
      })
      .catch(() => {
        dataList.value = [];
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    filterParams.dateRange = [
      dayjs().format("YYYY-MM-DD"),
      dayjs().format("YYYY-MM-DD")
    ];
    onSearch();
  };

  const handleSelectionChange = selection => {
    selectedIds.value = selection.map(item => item.id);
  };

  const handleSizeChange = size => {
    pagination.pageSize = size;
    loadEmployees();
  };

  const handleCurrentChange = page => {
    pagination.currentPage = page;
    loadEmployees();
  };

  return {
    filterParams,
    loading,
    dataList,
    columns,
    selectedIds,
    pagination,
    onSearch,
    resetFilterForm,
    handleSelectionChange,
    handleSizeChange,
    handleCurrentChange
  };
}
