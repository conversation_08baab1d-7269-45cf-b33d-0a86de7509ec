<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import CalendarView from "./CalendarView.vue";
import QuickTimeRangeSelector from "@/components/QuickTimeRangeSelector.vue";
import { getPersuaders } from "@/api/schedule";
import { getAllEmployees } from "@/api/attendance";
import { useNodeTree } from "@/hooks/useNodeTree";
import axios from "axios";
import dayjs from "dayjs";

defineOptions({
  name: "AttendanceManagement"
});

// 表单引用
const filterRef = ref();

// 筛选参数
const filterParams = reactive({
  userName: ""
});

// 数据状态
const loading = ref(false);
const dataList = ref([]);

// 分页参数
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

// 导出相关状态
const exportVisible = ref(false);
const exportForm = reactive({
  type: "personal",
  timeRange: [
    dayjs().startOf("month").format("YYYY-MM-DD"),
    dayjs().endOf("month").format("YYYY-MM-DD")
  ] as [string, string],
  loading: false
});

// 地区选择
const { nodeTree, selectedNodes, handleNodeSelect } = useNodeTree();

// 监听nodeTree变化，设置默认选择宜宾市
watch(
  nodeTree,
  newNodeTree => {
    if (newNodeTree.length > 0 && !selectedNodes.value.city) {
      // 自动选择第一个城市（应该是宜宾市）
      const firstCity = newNodeTree[0];
      if (firstCity) {
        handleNodeSelect(firstCity);
      }
    }
  },
  { immediate: true }
);

// 格式化工作模式
const formatWorkPattern = (row: any) => {
  if (!row.workPattern) return "";
  const pattern = row.workPattern.split(",");
  const workDays = pattern.filter(day => day === "1").length;
  const restDays = pattern.filter(day => day === "0").length;
  return `上${workDays}休${restDays}`;
};

// 加载员工数据
const loadEmployees = async () => {
  try {
    loading.value = true;
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize
    };

    const res = await getPersuaders(params);
    if (res.code === 200) {
      dataList.value = res.data.records.map(item => ({
        ...item,
        siteFullName: [
          item.city,
          item.county,
          item.township,
          item.hamlet,
          item.site
        ]
          .filter(Boolean)
          .join("-")
      }));
      pagination.total = res.data.total;
    }
  } catch (error) {
    console.error("加载员工数据失败:", error);
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索功能
const onSearch = async () => {
  try {
    loading.value = true;
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: filterParams.userName
    };

    const res = await getAllEmployees(params);
    if (res.code === 200) {
      dataList.value = res.data.records.map(item => ({
        ...item,
        siteFullName: [
          item.city,
          item.county,
          item.township,
          item.hamlet,
          item.site
        ]
          .filter(Boolean)
          .join("-")
      }));
      pagination.total = res.data.total;
    } else {
      dataList.value = [];
    }
  } catch (error) {
    console.error("搜索失败:", error);
    dataList.value = [];
    ElMessage.error("搜索失败");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetFilterForm = () => {
  if (filterRef.value) {
    filterRef.value.resetFields();
  }
  filterParams.userName = "";
  onSearch();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  loadEmployees();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  loadEmployees();
};

// 导出功能
const openExportDialog = () => {
  exportForm.type = "personal";
  exportForm.timeRange = [
    dayjs().startOf("month").format("YYYY-MM-DD"),
    dayjs().endOf("month").format("YYYY-MM-DD")
  ];
  exportVisible.value = true;
};

// 处理时间范围变化
const handleTimeRangeChange = (range: [string, string]) => {
  exportForm.timeRange = range;
};

const handleExport = async () => {
  if (
    !exportForm.timeRange ||
    !exportForm.timeRange[0] ||
    !exportForm.timeRange[1]
  ) {
    ElMessage.warning("请选择导出时间范围");
    return;
  }

  try {
    exportForm.loading = true;

    const params = {
      city: selectedNodes.value.city || undefined,
      county: selectedNodes.value.county || undefined,
      township: selectedNodes.value.township || undefined,
      hamlet: selectedNodes.value.hamlet || undefined,
      site: selectedNodes.value.site || undefined,
      startDate: exportForm.timeRange[0],
      endDate: exportForm.timeRange[1]
    };

    const response = await axios.get(
      `${import.meta.env.VITE_API_URL}/attendance/export/${exportForm.type}`,
      {
        params,
        responseType: "blob",
        headers: {
          Accept:
            "application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        }
      }
    );

    const blob = response.data;
    const headers = response.headers;
    if (!(blob instanceof Blob)) {
      throw new Error("服务器返回的数据格式不正确");
    }

    // 从响应头中获取文件名
    let filename = `考勤数据_${exportForm.type}_${exportForm.timeRange[0]}_${exportForm.timeRange[1]}.xlsx`;
    const contentDisposition = headers["content-disposition"];
    if (contentDisposition) {
      // 匹配 filename=xxx.xlsx 格式
      const filenameMatch = contentDisposition.match(/filename=([^;]+)/);
      if (filenameMatch && filenameMatch[1]) {
        // 解码文件名
        filename = decodeURIComponent(filenameMatch[1]);
      }
    }

    if (blob.size === 0) {
      throw new Error("下载的文件为空");
    }
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success("文件导出成功");
    exportVisible.value = false;
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败，请稍后重试");
  } finally {
    exportForm.loading = false;
  }
};

// 页面初始化
onMounted(() => {
  loadEmployees();
});
</script>

<template>
  <div class="attendance-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form
        ref="filterRef"
        :inline="true"
        :model="filterParams"
        class="search-form"
      >
        <el-form-item label="员工姓名：" prop="userName">
          <el-input
            v-model="filterParams.userName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="resetFilterForm">重置</el-button>
          <el-button type="success" @click="openExportDialog">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table v-loading="loading" :data="dataList" style="width: 100%">
        <el-table-column type="expand">
          <template #default="props">
            <div style="padding: 20px">
              <CalendarView
                :user-id="props.row.userId"
                :user-name="props.row.name"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="电话" width="150" />
        <el-table-column prop="sex" label="性别" width="80" />
        <el-table-column prop="deptName" label="职位" width="120" />
        <el-table-column prop="groupName" label="班组名称" width="150" />
        <el-table-column prop="siteFullName" label="点位" />
        <el-table-column prop="startDate" label="生效日期" width="150" />
        <el-table-column
          prop="workPattern"
          label="工作模式"
          :formatter="formatWorkPattern"
          width="120"
        />
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 导出弹窗 -->
    <el-dialog
      v-model="exportVisible"
      title="导出考勤数据"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出类型">
          <el-radio-group v-model="exportForm.type">
            <el-radio label="personal">人员考勤明细</el-radio>
            <el-radio label="summary">考勤汇总数据</el-radio>
            <el-radio label="detail-summary">明细和汇总</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="时间范围">
          <QuickTimeRangeSelector
            v-model="exportForm.timeRange"
            :disable-future="true"
            @change="handleTimeRangeChange"
          />
        </el-form-item>

        <el-form-item label="地区选择">
          <div class="tree-container">
            <el-tree
              :data="nodeTree"
              :props="{
                label: 'label',
                children: 'childList'
              }"
              node-key="id"
              highlight-current
              @node-click="handleNodeSelect"
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="exportForm.loading"
          @click="handleExport"
        >
          确定导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.attendance-management {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
}

.search-section {
  padding: 16px;
  margin-bottom: 16px;
  background: white;
  border-radius: 4px;
}

.table-section {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  background: white;
  border-radius: 4px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 16px;
  margin-top: 16px;
  background: white;
  border-radius: 4px;
}

.tree-container {
  max-height: 200px;
  padding: 8px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>
