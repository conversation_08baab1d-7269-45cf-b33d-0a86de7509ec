<template>
  <el-calendar v-model="value">
    <template #date-cell="{ data }">
      <div class="day-content-class">
        <template v-if="validTime(data)">
          <div class="header-class">
            <span class="day-class">{{
              data.day.split("-").slice(1).join("-")
            }}</span>
          </div>
          <div class="no-work-class">
            <IconifyIconOffline :icon="AlarmClock" class="icon-class" />
            <span class="tips-class">未开始</span>
          </div>
        </template>
        <template v-else-if="currentDaySchedules(data).length > 0">
          <div class="header-class">
            <span class="day-class">{{
              data.day.split("-").slice(1).join("-")
            }}</span>
          </div>
          <div class="paiban-class">
            <div
              v-for="(item, i) in currentDaySchedules(data)"
              :key="i"
              :class="[
                'each-paiban-class',
                setWorkClass(item.attendance.statusCode, item.shiftName)
              ]"
            >
              <div class="paiban-info">
                <span class="paiban-icon-class">
                  <IconifyIconOffline
                    :icon="
                      getTimeOfDay(item.startTime) === '上午'
                        ? Sunrise
                        : getTimeOfDay(item.startTime) === '中午'
                          ? Sunny
                          : getTimeOfDay(item.startTime) === '下午'
                            ? Sunset
                            : Moon
                    "
                  />
                </span>
                <span class="paiban-name-class">{{ item.shiftName }}</span>
              </div>
              <span class="status-name">{{ item.attendance.status }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="header-class">
            <span class="day-class">{{
              data.day.split("-").slice(1).join("-")
            }}</span>
          </div>
          <div class="no-work-class">
            <IconifyIconOffline :icon="Calendar" class="icon-class" />
            <span class="tips-class">暂无打卡记录</span>
          </div>
        </template>
      </div>
    </template>
  </el-calendar>
</template>

<script lang="ts" setup>
import { computed, onMounted, defineExpose, ref, watch } from "vue";
import { ElCalendar } from "element-plus";
import Calendar from "@iconify-icons/ep/calendar";
import AlarmClock from "@iconify-icons/ep/alarm-clock";
import Sunrise from "@iconify-icons/ep/sunrise";
import Sunny from "@iconify-icons/ep/sunny";
import Sunset from "@iconify-icons/ep/sunset";
import Moon from "@iconify-icons/ep/moon";
import dayjs from "dayjs";
import { message } from "@/utils/message";
import { getScheduleForMonth } from "@/api/schedule";
import { getAllEmployeeAttendance } from "@/api/attendance";

const props = defineProps<{
  userId: number;
  open?: boolean;
  userName: string;
}>();

const value = ref(new Date());
const dataList = ref([]);

// 使用 watch 监听时间的变化
watch(value, (newVal, oldVal) => {
  loadScheduleForMonth(newVal);
});

watch(
  () => props.userId,
  () => {
    loadScheduleForMonth();
  }
);

onMounted(() => {
  loadScheduleForMonth();
});

// 获取当天排班情况
const currentDaySchedules = ({ date }) => {
  const day = dayjs(date).date();
  const month = dayjs(date).month() + 1;
  return dataList.value.filter(item => {
    return (
      day === dayjs(item.scheduleDate).date() &&
      month === dayjs(item.scheduleDate).month() + 1
    );
  });
};

const setIconClass = (time: string) => {
  const value = getTimeOfDay(time);
  let classValue = "el-icon-sunrise-1";
  switch (value) {
    case "上午":
      classValue = "el-icon-sunrise-1";
      break;
    case "中午":
      classValue = "el-icon-sunny";
      break;
    case "下午":
      classValue = "el-icon-moon";
      break;
    case "晚上":
      classValue = "el-icon-moon";
      break;
    default:
      break;
  }
  return classValue;
};

const setWorkClass = (code: string, shiftType?: string) => {
  // 先判断考勤状态
  if (code !== "success") {
    return "error-work-class";
  }

  // 根据班次类型返回对应的样式类
  switch (shiftType) {
    case "早班":
      return "morning-work-class";
    case "中班":
      return "noon-work-class";
    case "晚班":
      return "night-work-class";
    default:
      return "normal-work-class";
  }
};

// 判断时间是否在当前时间之后
const validTime = ({ date }) => {
  return dayjs(date).isAfter(dayjs(), "day");
};

// 判断时间范围
const getTimeOfDay = (time: string) => {
  // 将传入的时间字符串按冒号分割
  const [hours, minutes] = time.split(":").map(Number);

  // 判断时间段
  if (hours >= 0 && hours < 11) {
    return "上午";
  }
  if (hours >= 11 && hours < 13) {
    return "中午";
  } else if (hours >= 13 && hours < 18) {
    return "下午";
  } else {
    return "晚上";
  }
};

// 获取月份排班数据
const loadScheduleForMonth = (time: Date = new Date()) => {
  const startTime = dayjs(time).startOf("month").format("YYYY-MM-DD 00:00:00");
  const endTime = dayjs(time).endOf("month").format("YYYY-MM-DD HH:mm:ss");
  getAllEmployeeAttendance(props.userName, startTime, endTime).then(res => {
    if (res.code === 200) {
      dataList.value = res.data.filter(item => item.attendance);
    }
  });
};

defineExpose({
  loadScheduleForMonth
});
</script>

<style scoped>
.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.event-title {
  margin: 0;
  font-size: 12px;
  color: #409eff;
}

:deep(.current) {
  height: 120px;
}

:deep(.el-calendar-day) {
  height: 100%;
  height: 120px;
}

.calender-class {
  width: 100%;
  height: 100%;
}

.is-selected {
  color: #1989fa;
}

.el-calendar__body {
  height: 85vh;
}

.el-calendar-table {
  height: 100%;
}

.el-calendar-day {
  height: 100% !important;
}

.day-content-class {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 120px;
  padding: 8px;
  overflow: hidden;
}

.header-class {
  display: flex;
  align-items: center;
  height: 24px;
  margin-bottom: 8px;
  font-weight: 500;
}

.day-class {
  flex: 4;
}

.paiban-class {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 4px;
  max-height: 100%;
  padding: 4px;
  overflow-y: auto;
}

.paiban-class::-webkit-scrollbar {
  width: 4px;
}

.paiban-class::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 2px;
}

.paiban-class::-webkit-scrollbar-track {
  background-color: transparent;
}

.paiban-class {
  scrollbar-width: thin;

  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: #c0c4cc;
    }
  }
}

.paiban-icon-class {
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
}

.paiban-name-class {
  padding-top: 5px;
}

.each-paiban-class {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  text-align: center;
  border-radius: 4px;
}

/* 早班样式 */
.morning-work-class {
  color: #67c23a;
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

/* 中班样式 */
.noon-work-class {
  color: #e6a23c;
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
}

/* 晚班样式 */
.night-work-class {
  color: #909399;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

/* 默认样式 */
.normal-work-class {
  color: #409eff;
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
}

/* 异常状态样式 */
.error-work-class {
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}

.no-work-class {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #909399;
}

.icon-class {
  margin-bottom: 10px;
  font-size: 14px;
}

/* 侧边弹窗 */
.add-btn-class {
  float: right;
  margin: 10px;
}

.change-date-drawer-class .el-calendar__body {
  height: 45%;
}

.change-date-drawer-class .day-content-class {
  height: 30px;
}

.disabled-date-class {
  color: #ccc;
  pointer-events: none;
}

/* 添加日期样式 */
:deep(.el-calendar-day) {
  box-sizing: border-box;
  height: 100%;
  min-height: 120px;
  padding: 0;
}

/* 当天日期高亮 */
:deep(.is-today) {
  .day-class {
    font-weight: bold;
    color: var(--el-color-primary);
  }
}

/* 非本月日期样式 */
:deep(.next-month),
:deep(.prev-month) {
  .day-content-class {
    opacity: 0.5;
  }
}

.status-name {
  margin-left: auto;
  font-size: 12px;
}
</style>
