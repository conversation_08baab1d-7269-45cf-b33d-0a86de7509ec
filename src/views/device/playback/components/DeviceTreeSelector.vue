<template>
  <el-card shadow="never" class="device-tree-card">
    <template #header>
      <div class="card-header">
        <span>设备选择</span>
        <el-button size="small" text @click="refreshDeviceTree">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>
    <div v-loading="treeLoading" class="device-tree-container">
      <el-tree
        :data="deviceTreeData"
        :props="treeProps"
        :default-expand-all="true"
        :highlight-current="true"
        :expand-on-click-node="false"
        node-key="label"
        @node-click="handleDeviceSelect"
      >
        <template #default="{ data }">
          <div class="tree-node">
            <el-icon
              v-if="data.type === 'location'"
              class="node-icon location-icon"
            >
              <Folder />
            </el-icon>
            <el-icon
              v-else
              class="node-icon device-icon"
              :class="{
                'device-offline': data.deviceInfo?.state === 1
              }"
            >
              <VideoCamera />
            </el-icon>
            <span
              class="node-label"
              :class="{
                'device-offline': data.deviceInfo?.state === 1
              }"
            >
              {{ data.label }}
            </span>
            <el-tag
              v-if="data.type === 'device'"
              :type="data.deviceInfo?.state === 0 ? 'success' : 'danger'"
              size="small"
              class="device-status"
            >
              {{ data.deviceInfo?.state === 0 ? "在线" : "离线" }}
            </el-tag>
          </div>
        </template>
      </el-tree>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Refresh, Folder, VideoCamera } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getCameraTree, type DeviceTreeNode } from "@/api/device";

// Props
interface Props {
  selectedDevice?: DeviceTreeNode | null;
}

const props = withDefaults(defineProps<Props>(), {
  selectedDevice: null
});

// Emits
const emit = defineEmits<{
  deviceSelect: [device: DeviceTreeNode];
  refresh: [];
}>();

// 数据状态
const treeLoading = ref(false);
const deviceTreeData = ref<DeviceTreeNode[]>([]);

// 树形组件配置
const treeProps = {
  children: "children",
  label: "label"
};

// API调用函数
const fetchDeviceTree = async () => {
  treeLoading.value = true;
  try {
    const result = await getCameraTree();

    if (result.code === 200) {
      // 处理节点合并
      deviceTreeData.value = processTreeNodeMerging(result.data);
    } else {
      ElMessage.error(`获取设备节点树失败: ${result.message}`);
    }
  } catch (error) {
    console.error("获取设备节点树失败:", error);
    ElMessage.error("获取设备节点树失败，请检查网络连接");
  } finally {
    treeLoading.value = false;
  }
};

// 处理树节点合并逻辑
const processTreeNodeMerging = (nodes: DeviceTreeNode[]): DeviceTreeNode[] => {
  return nodes.map(node => mergeNode(node));
};

// 递归合并单子节点
const mergeNode = (node: DeviceTreeNode): DeviceTreeNode => {
  // 如果是设备节点，直接返回
  if (node.type === "device") {
    return node;
  }

  // 处理子节点
  let processedChildren = node.children.map(child => mergeNode(child));

  // 如果当前节点只有一个子节点，且子节点也不是设备节点，考虑合并
  if (
    processedChildren.length === 1 &&
    processedChildren[0].type === "location"
  ) {
    const child = processedChildren[0];

    // 合并标签显示
    const mergedLabel = `${node.label} → ${child.label}`;

    return {
      ...child,
      label: mergedLabel,
      children: child.children
    };
  }

  // 否则返回处理后的节点
  return {
    ...node,
    children: processedChildren
  };
};

// 方法
const handleDeviceSelect = (data: DeviceTreeNode) => {
  if (data.type === "device" && data.deviceInfo) {
    emit("deviceSelect", data);
    if (data.deviceInfo.state === 1) {
      ElMessage.info("已选择离线设备，仍可查看历史回放视频");
    }
  }
};

const refreshDeviceTree = () => {
  fetchDeviceTree();
  emit("refresh");
};

onMounted(() => {
  fetchDeviceTree();
});
</script>

<style scoped>
.device-tree-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-tree-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  min-height: 0;
  max-height: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  width: 100%;
}

.node-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.location-icon {
  color: var(--el-color-primary);
}

.device-icon {
  color: var(--el-color-success);
}

.device-icon.device-offline {
  color: var(--el-color-danger);
}

.node-label {
  font-size: 13px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-label.device-offline {
  color: var(--el-text-color-secondary);
}

.device-status {
  margin-left: auto;
  flex-shrink: 0;
}

/* 确保el-tree不会超出容器高度 */
.device-tree-container :deep(.el-tree) {
  height: 100%;
  overflow-y: auto;
}
</style>
