<template>
  <div class="recordings-timeline">
    <div class="timeline-header">
      <div class="timeline-title">
        <h4>录像时间轴 ({{ recordings?.length || 0 }}个录像文件)</h4>
        <span class="time-window-info"
          >当前窗口: {{ getTimeWindowInfo() }} (4小时)</span
        >
      </div>
      <div class="timeline-controls">
        <div class="zoom-controls">
          <el-button
            size="small"
            :disabled="timelineZoom <= 1"
            @click="handleTimelineZoom(-0.5)"
          >
            <el-icon><Minus /></el-icon>
          </el-button>
          <span class="zoom-level">{{ timelineZoom.toFixed(1) }}x</span>
          <el-button
            size="small"
            :disabled="timelineZoom >= 10"
            @click="handleTimelineZoom(0.5)"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
        <div class="timeline-info">
          <span class="info-item">🎨 不同颜色=不同录像</span>
          <span class="info-item">📏 长度=录像时长</span>
          <span class="info-item">⚪ 空白=无录像</span>
          <span class="info-item">👆 拖动查看其他时间段</span>
          <span class="info-item current-time"
            >📅 {{ getTimeWindowInfo() }}</span
          >
        </div>
      </div>
    </div>

    <!-- 时间轴滚动条指示器 -->
    <div v-if="timeRange && timeRange.length >= 2" class="timeline-scrollbar">
      <div class="scrollbar-thumb" :style="getScrollbarThumbStyle()" />
    </div>

    <div
      class="timeline-container"
      :class="{ dragging: isDragging }"
      :style="getTimelineBackgroundStyle()"
      @mousedown="handleTimelineMouseDown"
      @wheel="handleTimelineWheel"
    >
      <div class="timeline-scale">
        <div
          v-for="hour in timelineHours"
          :key="hour.label"
          class="scale-mark"
          :class="{ 'with-date': hour.showDate }"
          :style="{ left: hour.position + '%' }"
        >
          <div class="scale-line" />
          <div class="scale-label">
            <div v-if="hour.showDate" class="date-label">
              {{ hour.dateStr }}
            </div>
            <div class="time-label">{{ hour.timeStr }}</div>
          </div>
        </div>
      </div>

      <div class="timeline-tracks">
        <div
          v-for="(track, trackIndex) in calculateRecordingTracks()"
          :key="`track-${trackIndex}`"
          class="timeline-track"
          :style="{ zIndex: track?.length || 0 }"
        >
          <div
            v-for="recording in track || []"
            :key="`recording-${recording.id}`"
            class="timeline-segment"
            :class="{
              selected: selectedRecording?.id === recording.id
            }"
            :style="getSegmentStyle(recording)"
            :title="`📹 ${recording.fileName}\n⏰ ${formatDisplayTime(recording.startTime)} - ${formatDisplayTime(recording.endTime)}\n⏱️ 时长: ${formatDuration(recording.duration)}\n📁 大小: ${formatFileSize(recording.fileSize)}\n🔄 状态: ${getRecordingStatus(recording.status)}`"
            @click="selectRecording(recording)"
            @mousedown.stop
          >
            <div class="segment-content">
              <span class="segment-name">{{
                recording.fileName.replace(".mp4", "").replace("_temp", "")
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中录像的详细信息 -->
    <div v-if="selectedRecording" class="selected-recording-info">
      <div class="selected-info-content">
        <div class="selected-title">
          <el-icon><VideoCamera /></el-icon>
          <span>{{ selectedRecording.fileName }}</span>
          <el-tag v-if="isPlaying" size="small" type="success">正在播放</el-tag>
        </div>
        <div class="selected-details">
          <span
            >{{ formatDisplayTime(selectedRecording.startTime) }} -
            {{ formatDisplayTime(selectedRecording.endTime) }}</span
          >
          <span>{{ formatDuration(selectedRecording.duration) }}</span>
          <span>{{ formatFileSize(selectedRecording.fileSize) }}</span>
        </div>
      </div>
      <div class="selected-actions">
        <el-button
          type="primary"
          size="small"
          :loading="videoLoading"
          @click="playRecording(selectedRecording)"
        >
          <el-icon><VideoPlay /></el-icon>
          播放
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="downloadRecording(selectedRecording)"
        >
          <el-icon><Download /></el-icon>
          下载
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import {
  Plus,
  Minus,
  VideoCamera,
  VideoPlay,
  Download
} from "@element-plus/icons-vue";
import type { RecordingInfo } from "@/api/device";

// Props
interface Props {
  recordings?: RecordingInfo[];
  timeRange?: string[];
  selectedRecording?: RecordingInfo | null;
  videoLoading?: boolean;
  isPlaying?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  recordings: () => [],
  timeRange: () => [],
  selectedRecording: null,
  videoLoading: false,
  isPlaying: false
});

// Emits
const emit = defineEmits<{
  selectRecording: [recording: RecordingInfo];
  playRecording: [recording: RecordingInfo];
  downloadRecording: [recording: RecordingInfo];
}>();

// 时间轴状态
const timelineZoom = ref(1);
const timelineOffset = ref(0);
const isDragging = ref(false);
const dragStartX = ref(0);
const dragStartOffset = ref(0);

interface TimelineHour {
  label: string;
  position: number;
  showDate: boolean;
  dateStr: string;
  timeStr: string;
}

const timelineHours = ref<TimelineHour[]>([]);

// 时间窗口设置（固定4小时窗口）
const timeWindowHours = 4;

// 获取当前时间窗口的开始和结束时间
const getCurrentTimeWindow = () => {
  if (!props.timeRange || props.timeRange.length < 2) {
    return { start: new Date(), end: new Date() };
  }

  const fullStart = new Date(props.timeRange[0]);
  const fullEnd = new Date(props.timeRange[1]);
  const fullDuration = fullEnd.getTime() - fullStart.getTime();

  // 计算4小时窗口的毫秒数
  const windowDuration = timeWindowHours * 60 * 60 * 1000;

  // 修复：重新计算偏移量
  // maxOffset 应该是能够偏移的最大时间量，即 fullDuration - windowDuration
  const maxOffsetTime = Math.max(0, fullDuration - windowDuration);
  // actualOffset 是实际偏移的时间量
  const actualOffset = (timelineOffset.value / 100) * maxOffsetTime;

  const windowStart = new Date(fullStart.getTime() + actualOffset);

  // 简化：始终使用完整的6小时窗口，即使超出查询范围
  // 这样可以确保拖动逻辑的一致性
  const windowEnd = new Date(windowStart.getTime() + windowDuration);

  return { start: windowStart, end: windowEnd };
};

// 获取时间窗口信息文本
const getTimeWindowInfo = () => {
  const { start, end } = getCurrentTimeWindow();

  // 格式化日期和时间
  const formatDateTime = date => {
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${month}/${day} ${hours}:${minutes}`;
  };

  const startStr = formatDateTime(start);
  const endStr = formatDateTime(end);

  // 如果是同一天，只显示一次日期
  if (start.toDateString() === end.toDateString()) {
    const dateStr = `${(start.getMonth() + 1).toString().padStart(2, "0")}/${start.getDate().toString().padStart(2, "0")}`;
    const startTime = `${start.getHours().toString().padStart(2, "0")}:${start.getMinutes().toString().padStart(2, "0")}`;
    const endTime = `${end.getHours().toString().padStart(2, "0")}:${end.getMinutes().toString().padStart(2, "0")}`;
    return `${dateStr} ${startTime} - ${endTime}`;
  }

  return `${startStr} - ${endStr}`;
};

// 方法
const selectRecording = (recording: RecordingInfo) => {
  emit("selectRecording", recording);
};

const playRecording = (recording: RecordingInfo) => {
  emit("playRecording", recording);
};

const downloadRecording = (recording: RecordingInfo) => {
  emit("downloadRecording", recording);
};

// 时间轴缩放和拖动方法
const handleTimelineZoom = (delta: number) => {
  const newZoom = Math.max(1, Math.min(10, timelineZoom.value + delta));
  timelineZoom.value = newZoom;
  calculateTimelineHours();
};

const handleTimelineMouseDown = (event: MouseEvent) => {
  isDragging.value = true;
  dragStartX.value = event.clientX;
  dragStartOffset.value = timelineOffset.value;

  event.preventDefault();
  document.addEventListener("mousemove", handleTimelineMouseMove);
  document.addEventListener("mouseup", handleTimelineMouseUp);
};

// 节流函数，避免过于频繁的更新
let updateTimelineThrottle: NodeJS.Timeout | null = null;

const handleTimelineMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return;

  const deltaX = event.clientX - dragStartX.value;
  const timelineElement = document.querySelector(
    ".timeline-container"
  ) as HTMLElement;
  const timelineWidth = timelineElement?.offsetWidth || 800;

  // 计算拖动的百分比，增加灵敏度调节
  const sensitivity = 0.8; // 降低灵敏度，让拖动更平滑
  const dragPercent = (deltaX / timelineWidth) * 100 * sensitivity;

  // 更新偏移量（向右拖动是负偏移，向左拖动是正偏移）
  const newOffset = dragStartOffset.value - dragPercent;

  // 获取时间范围限制
  if (!props.timeRange || props.timeRange.length < 2) return;

  const fullStart = new Date(props.timeRange[0]);
  const fullEnd = new Date(props.timeRange[1]);
  const fullDuration = fullEnd.getTime() - fullStart.getTime();
  const windowDuration = timeWindowHours * 60 * 60 * 1000;

  // 修复：重新计算最大偏移量
  let maxOffsetPercent;
  if (fullDuration <= windowDuration) {
    // 如果查询范围小于等于窗口大小，不允许偏移
    maxOffsetPercent = 0;
  } else {
    // 关键修复：最大偏移量应该让窗口能够滑动到最后
    // 当 offset = 100% 时，窗口应该显示最后的 windowDuration 时间
    // 即：windowStart = fullEnd - windowDuration
    // 所以：actualOffset = (fullEnd - windowDuration) - fullStart = fullDuration - windowDuration
    // 但是我们希望 offset = 100% 对应这个状态
    // 所以：maxOffsetPercent = 100

    // 但实际上，我们需要的是：当拖动到最右边时，能看到查询范围的最后部分
    // 对于24小时查询和6小时窗口：
    // - offset = 0% → 0-6小时
    // - offset = 100% → 18-24小时
    // 所以最大偏移时间 = 18小时，对应的百分比应该基于可偏移的时间范围

    const maxOffsetTime = fullDuration - windowDuration; // 18小时
    // 这个 maxOffsetTime 对应 100% 的偏移
    maxOffsetPercent = 100;
  }

  // 限制偏移量在合理范围内
  const clampedOffset = Math.max(0, Math.min(maxOffsetPercent, newOffset));

  timelineOffset.value = clampedOffset;

  // 使用节流更新时间刻度，避免过于频繁的计算
  if (updateTimelineThrottle) {
    clearTimeout(updateTimelineThrottle);
  }

  updateTimelineThrottle = setTimeout(() => {
    calculateTimelineHours();
  }, 16); // 约60fps的更新频率
};

const handleTimelineMouseUp = () => {
  isDragging.value = false;
  document.removeEventListener("mousemove", handleTimelineMouseMove);
  document.removeEventListener("mouseup", handleTimelineMouseUp);
};

const handleTimelineWheel = (event: WheelEvent) => {
  event.preventDefault();

  // 改为左右滚动时间轴
  const scrollSensitivity = 5; // 滚动灵敏度，可以调整
  const deltaOffset = event.deltaY > 0 ? scrollSensitivity : -scrollSensitivity;

  // 计算新的偏移量
  const newOffset = timelineOffset.value + deltaOffset;

  // 获取最大偏移量限制
  if (!props.timeRange || props.timeRange.length < 2) return;

  const fullStart = new Date(props.timeRange[0]);
  const fullEnd = new Date(props.timeRange[1]);
  const fullDuration = fullEnd.getTime() - fullStart.getTime();
  const windowDuration = timeWindowHours * 60 * 60 * 1000;

  let maxOffsetPercent;
  if (fullDuration <= windowDuration) {
    maxOffsetPercent = 0;
  } else {
    maxOffsetPercent = 100;
  }

  // 限制偏移量在合理范围内
  const clampedOffset = Math.max(0, Math.min(maxOffsetPercent, newOffset));
  timelineOffset.value = clampedOffset;

  // 更新时间刻度
  calculateTimelineHours();
};

// 格式化函数
const formatDisplayTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
};

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const getRecordingStatus = (status: number) => {
  switch (status) {
    case 0:
      return "准备中";
    case 1:
      return "录制中";
    case 2:
      return "已停止";
    case 3:
      return "已删除";
    default:
      return "未知";
  }
};

// 监听时间范围变化，重新计算时间轴
watch(
  () => props.timeRange,
  () => {
    calculateTimelineHours();
  },
  { deep: true }
);

watch(
  () => props.recordings,
  () => {
    calculateTimelineHours();
  },
  { deep: true }
);

// 组件挂载时初始化时间轴
onMounted(() => {
  nextTick(() => {
    if (props.timeRange && props.timeRange.length >= 2) {
      calculateTimelineHours();
      // 初始化时间轴显示，设置到开始位置
      timelineOffset.value = 0;
    }
  });
});

// 获取滚动条指示器样式
const getScrollbarThumbStyle = () => {
  if (!props.timeRange || props.timeRange.length < 2) {
    return { left: "0%", width: "100%" };
  }

  const fullStart = new Date(props.timeRange[0]);
  const fullEnd = new Date(props.timeRange[1]);
  const fullDuration = fullEnd.getTime() - fullStart.getTime();
  const windowDuration = timeWindowHours * 60 * 60 * 1000;

  // 计算滚动条拇指的宽度（表示当前窗口占总时间的比例）
  const thumbWidth = Math.min(100, (windowDuration / fullDuration) * 100);

  // 计算滚动条拇指的位置
  const thumbLeft = (timelineOffset.value / 100) * (100 - thumbWidth);

  return {
    left: thumbLeft + "%",
    width: thumbWidth + "%"
  };
};

// 生成时间轴背景网格，显示时间刻度对应的背景
const getTimelineBackgroundStyle = () => {
  if (!props.timeRange || props.timeRange.length < 2) {
    return {};
  }

  // 创建背景渐变来显示时间网格
  const start = new Date(props.timeRange[0]);
  const end = new Date(props.timeRange[1]);
  const totalHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);

  let gradientStops = [];
  for (let i = 0; i <= totalHours; i++) {
    const position = (i / totalHours) * 100;
    const color = i % 2 === 0 ? "rgba(0,0,0,0.02)" : "rgba(0,0,0,0.05)";
    gradientStops.push(`${color} ${position}%`);
  }

  return {
    background: `linear-gradient(to right, ${gradientStops.join(", ")})`,
    backgroundSize: `${100 / timelineZoom.value}% 100%`,
    backgroundPosition: `${-timelineOffset.value * timelineZoom.value}% 0`
  };
};

const calculateTimelineHours = () => {
  if (!props.timeRange || props.timeRange.length < 2) {
    timelineHours.value = [];
    return;
  }

  const { start, end } = getCurrentTimeWindow();
  const windowDuration = end.getTime() - start.getTime();

  // 根据缩放级别决定刻度间隔
  let interval = 60 * 60 * 1000; // 默认1小时间隔
  if (timelineZoom.value >= 2) {
    interval = 30 * 60 * 1000; // 30分钟间隔
  }
  if (timelineZoom.value >= 4) {
    interval = 15 * 60 * 1000; // 15分钟间隔
  }
  if (timelineZoom.value >= 8) {
    interval = 5 * 60 * 1000; // 5分钟间隔
  }

  const hours = [];
  let current = new Date(start);

  // 对齐到合适的时间边界
  if (interval === 30 * 60 * 1000) {
    current.setMinutes(Math.floor(current.getMinutes() / 30) * 30);
    current.setSeconds(0);
    current.setMilliseconds(0);
  } else if (interval === 15 * 60 * 1000) {
    current.setMinutes(Math.floor(current.getMinutes() / 15) * 15);
    current.setSeconds(0);
    current.setMilliseconds(0);
  } else if (interval === 5 * 60 * 1000) {
    current.setMinutes(Math.floor(current.getMinutes() / 5) * 5);
    current.setSeconds(0);
    current.setMilliseconds(0);
  } else {
    current.setMinutes(0);
    current.setSeconds(0);
    current.setMilliseconds(0);
  }

  let lastDate = null;

  while (current <= end) {
    const position =
      ((current.getTime() - start.getTime()) / windowDuration) * 100;

    // 格式化时间标签
    const timeStr = `${current.getHours().toString().padStart(2, "0")}:${current.getMinutes().toString().padStart(2, "0")}`;

    // 检查是否需要显示日期
    const currentDateStr = `${(current.getMonth() + 1).toString().padStart(2, "0")}/${current.getDate().toString().padStart(2, "0")}`;
    let label = timeStr;
    let showDate = false;

    // 如果是新的一天，或者是第一个刻度，显示日期
    if (lastDate !== currentDateStr || hours.length === 0) {
      label = `${currentDateStr}\n${timeStr}`;
      showDate = true;
      lastDate = currentDateStr;
    }

    // 对于整点时间，也显示日期（如果跨天的话）
    if (current.getMinutes() === 0 && current.getHours() === 0) {
      label = `${currentDateStr}\n${timeStr}`;
      showDate = true;
    }

    hours.push({
      label,
      position,
      showDate,
      dateStr: currentDateStr,
      timeStr: timeStr
    });

    current = new Date(current.getTime() + interval);
  }

  timelineHours.value = hours;
};

// 计算录像的显示轨道（避免重叠）
const calculateRecordingTracks = () => {
  if (!props.recordings) return [];

  const recordings = [...props.recordings];

  // 按开始时间排序
  recordings.sort(
    (a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
  );

  const tracks = [];

  recordings.forEach(recording => {
    const recordStart = new Date(recording.startTime).getTime();
    const recordEnd = new Date(recording.endTime).getTime();

    // 找到第一个没有时间冲突的轨道
    let trackIndex = 0;

    while (trackIndex < tracks.length) {
      const track = tracks[trackIndex];
      let hasConflict = false;

      for (const existingRecording of track) {
        const existingStart = new Date(existingRecording.startTime).getTime();
        const existingEnd = new Date(existingRecording.endTime).getTime();

        // 检查时间是否重叠
        if (!(recordEnd <= existingStart || recordStart >= existingEnd)) {
          hasConflict = true;
          break;
        }
      }

      if (!hasConflict) {
        track.push(recording);
        break;
      }

      trackIndex++;
    }

    // 如果所有现有轨道都有冲突，创建新轨道
    if (trackIndex === tracks.length) {
      tracks.push([recording]);
    }
  });

  return tracks;
};

const getSegmentStyle = (recording: RecordingInfo) => {
  if (!props.timeRange || props.timeRange.length < 2) {
    return { display: "none" };
  }

  const { start: windowStart, end: windowEnd } = getCurrentTimeWindow();
  const windowDuration = windowEnd.getTime() - windowStart.getTime();

  const recordStart = new Date(recording.startTime).getTime();
  const recordEnd = new Date(recording.endTime).getTime();
  const recordDuration = recordEnd - recordStart;

  // 检查录像是否与当前时间窗口有重叠
  if (
    recordEnd <= windowStart.getTime() ||
    recordStart >= windowEnd.getTime()
  ) {
    return { display: "none" };
  }

  // 计算录像在时间窗口中的精确位置和宽度
  // 开始位置：录像开始时间相对于窗口开始时间的百分比
  const startPercent =
    ((recordStart - windowStart.getTime()) / windowDuration) * 100;

  // 宽度：录像实际时长相对于窗口总时长的百分比
  const widthPercent = (recordDuration / windowDuration) * 100;

  // 处理录像超出窗口边界的情况
  let adjustedStart = startPercent;
  let adjustedWidth = widthPercent;

  // 如果录像开始时间早于窗口开始时间
  if (recordStart < windowStart.getTime()) {
    adjustedStart = 0;
    // 调整宽度，只显示在窗口内的部分
    const visibleDuration = recordEnd - windowStart.getTime();
    adjustedWidth = (visibleDuration / windowDuration) * 100;
  }

  // 如果录像结束时间晚于窗口结束时间
  if (recordEnd > windowEnd.getTime()) {
    // 调整宽度，只显示在窗口内的部分
    const visibleStart = Math.max(recordStart, windowStart.getTime());
    const visibleDuration = windowEnd.getTime() - visibleStart;
    adjustedWidth = (visibleDuration / windowDuration) * 100;
  }

  // 应用缩放（只影响显示，不影响位置计算的精确性）
  const finalLeft = adjustedStart * timelineZoom.value;
  const finalWidth = adjustedWidth * timelineZoom.value;

  // 为不同录像生成不同颜色
  const colors = [
    "linear-gradient(135deg, #409eff, #67c23a)", // 蓝绿
    "linear-gradient(135deg, #e6a23c, #f56c6c)", // 橙红
    "linear-gradient(135deg, #9c27b0, #673ab7)", // 紫色
    "linear-gradient(135deg, #00bcd4, #4caf50)", // 青绿
    "linear-gradient(135deg, #ff9800, #ff5722)", // 橙色
    "linear-gradient(135deg, #607d8b, #9e9e9e)", // 灰蓝
    "linear-gradient(135deg, #795548, #8bc34a)", // 棕绿
    "linear-gradient(135deg, #3f51b5, #2196f3)" // 深蓝
  ];

  const colorIndex = recording.id % colors.length;
  const backgroundColor = colors[colorIndex];

  // 如果片段太小，设置最小宽度确保可见和可点击
  const minWidthPx = 8; // 最小8像素宽度
  const containerWidth = 800; // 假设时间轴容器宽度
  const minWidthPercent = (minWidthPx / containerWidth) * 100;
  const displayWidth = Math.max(finalWidth, minWidthPercent);

  return {
    left: Math.max(0, Math.min(100, finalLeft)) + "%",
    width:
      Math.max(
        minWidthPercent,
        Math.min(100 - Math.max(0, finalLeft), displayWidth)
      ) + "%",
    background: backgroundColor,
    opacity: recording.status === 2 ? 1 : 0.7,
    border:
      recording.status === 2 ? "none" : "1px dashed rgba(255,255,255,0.5)",
    // 显示录像时长信息
    minWidth: "8px", // CSS最小宽度
    // 如果是被裁切的录像，添加视觉提示
    borderLeft:
      recordStart < windowStart.getTime()
        ? "2px solid rgba(255,255,255,0.8)"
        : "none",
    borderRight:
      recordEnd > windowEnd.getTime()
        ? "2px solid rgba(255,255,255,0.8)"
        : "none"
  };
};
</script>

<style scoped>
/* 时间轴样式 */
.recordings-timeline {
  margin-top: 16px;
  background: var(--el-bg-color);
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
  overflow: hidden;
  height: 200px;
  flex-shrink: 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color);
}

.timeline-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.timeline-header h4 {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.time-window-info {
  font-size: 12px;
  color: var(--el-color-primary);
  font-weight: 500;
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: var(--el-fill-color);
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.zoom-level {
  font-size: 12px;
  color: var(--el-text-color-regular);
  min-width: 30px;
  text-align: center;
}

.timeline-info {
  display: flex;
  gap: 16px;
}

.info-item {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.info-item.current-time {
  color: var(--el-color-primary);
  font-weight: 500;
}

.timeline-scrollbar {
  height: 4px;
  background: var(--el-fill-color-darker);
  border-radius: 2px;
  margin: 8px 16px 4px 16px;
  position: relative;
}

.scrollbar-thumb {
  height: 100%;
  background: var(--el-color-primary);
  border-radius: 2px;
  position: absolute;
  transition: all 0.2s ease;
}

.timeline-container {
  position: relative;
  height: 80px;
  margin: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  overflow: hidden;
  cursor: grab;
  user-select: none;
  transition:
    background-color 0.2s ease,
    border-color 0.2s ease;
  border: 2px solid transparent;
}

.timeline-container:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-7);
}

.timeline-container:active,
.timeline-container.dragging {
  cursor: grabbing;
  background: var(--el-fill-color);
  border-color: var(--el-color-primary);
}

.timeline-scale {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: var(--el-fill-color-light);
  overflow: hidden;
}

.scale-mark {
  position: absolute;
  height: 100%;
}

.scale-line {
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  background: var(--el-border-color);
}

.scale-label {
  position: absolute;
  left: 4px;
  top: 2px;
  font-size: 10px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
  line-height: 1.2;
}

.scale-mark.with-date .scale-label {
  top: -8px;
}

.date-label {
  font-size: 9px;
  color: var(--el-color-primary);
  font-weight: 500;
}

.time-label {
  font-size: 10px;
  color: var(--el-text-color-secondary);
}

.timeline-tracks {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.timeline-track {
  position: absolute;
  width: 100%;
  height: 20px;
  top: 0;
}

.timeline-segment {
  position: absolute;
  height: 36px;
  top: 2px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.timeline-segment:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.timeline-segment.selected {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  border: 2px solid var(--el-color-primary);
  z-index: 20;
}

.segment-content {
  padding: 2px 4px;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.segment-name {
  font-size: 9px;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selected-recording-info {
  padding: 12px 16px;
  background: var(--el-fill-color-light);
  border-top: 1px solid var(--el-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-info-content {
  flex: 1;
}

.selected-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.selected-title span {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.selected-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.selected-actions {
  display: flex;
  gap: 8px;
}
</style>
