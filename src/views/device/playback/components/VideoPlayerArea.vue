<template>
  <el-card shadow="never" class="video-card">
    <template #header>
      <div class="card-header">
        <span>视频回放</span>
        <div class="header-controls">
          <span v-if="selectedDevice" class="device-info">
            当前设备：{{ selectedDevice.deviceInfo.deviceName }} ({{
              selectedDevice.deviceInfo.equipmentNumber
            }})
          </span>
          <el-button
            size="small"
            type="primary"
            :disabled="!selectedRecording"
            @click="handleDownload"
          >
            <el-icon><Download /></el-icon>
            下载视频
          </el-button>
        </div>
      </div>
    </template>
    <div class="video-container">
      <div v-if="!currentVideo" class="video-placeholder">
        <el-icon class="placeholder-icon" size="64">
          <VideoPlay />
        </el-icon>
        <p class="placeholder-text">请在左侧选择设备和时间进行查询</p>
      </div>
      <div v-else class="video-player">
        <!-- 视频播放器 -->
        <div class="player-wrapper">
          <VideoPlayer
            v-if="currentVideoUrl"
            :src="currentVideoUrl"
            :controls="false"
            :show-custom-controls="true"
            :autoplay="true"
            :muted="false"
            @loadstart="handleVideoLoadStart"
            @canplay="handleVideoCanPlay"
            @error="handleVideoError"
            @timeupdate="handleVideoTimeUpdate"
          />
          <div v-else class="player-placeholder">
            <el-icon size="48"><VideoPlay /></el-icon>
            <p>请在下方时间轴中选择录像文件进行播放</p>
            <div class="debug-info">
              <p><strong>设备信息:</strong></p>
              <p>设备ID: {{ selectedDevice.deviceInfo.deviceId }}</p>
              <p>设备名称: {{ selectedDevice.deviceInfo.deviceName }}</p>
              <p>设备编号: {{ selectedDevice.deviceInfo.equipmentNumber }}</p>
              <p>IP地址: {{ selectedDevice.deviceInfo.ip }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 录像时间轴 -->
      <RecordingTimeline
        v-if="recordings && recordings.length > 0"
        :recordings="recordings"
        :time-range="timeRange"
        :selected-recording="selectedRecording"
        :video-loading="videoLoading"
        :is-playing="isPlaying"
        @select-recording="handleSelectRecording"
        @play-recording="handlePlayRecording"
        @download-recording="handleDownloadRecording"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Download, VideoPlay } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import VideoPlayer from "@/components/VideoPlayer/index.vue";
import RecordingTimeline from "./RecordingTimeline.vue";
import {
  downloadVideoFile,
  type DeviceTreeNode,
  type RecordingInfo
} from "@/api/device";

// Props
interface Props {
  selectedDevice?: DeviceTreeNode | null;
  currentVideo?: any;
  selectedRecording?: RecordingInfo | null;
  currentVideoUrl?: string;
  videoLoading?: boolean;
  recordings?: RecordingInfo[];
  timeRange?: string[];
  isPlaying?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selectedDevice: null,
  currentVideo: null,
  selectedRecording: null,
  currentVideoUrl: "",
  videoLoading: false,
  recordings: () => [],
  timeRange: () => [],
  isPlaying: false
});

// Emits
const emit = defineEmits<{
  videoLoadStart: [];
  videoCanPlay: [];
  videoError: [error: string];
  videoTimeUpdate: [currentTime: number, duration: number];
  download: [];
  selectRecording: [recording: RecordingInfo];
  playRecording: [recording: RecordingInfo];
  downloadRecording: [recording: RecordingInfo];
}>();

// 视频播放器事件处理
const handleVideoLoadStart = () => {
  emit("videoLoadStart");
};

const handleVideoCanPlay = () => {
  emit("videoCanPlay");

  // 第一次播放时，尝试自动开始播放
  if (props.selectedRecording) {
  }
};

const handleVideoError = (error: string) => {
  emit("videoError", error);
  console.error("视频播放错误:", error);
  ElMessage.error(`视频播放失败: ${error}`);
};

const handleVideoTimeUpdate = (currentTime: number, duration: number) => {
  emit("videoTimeUpdate", currentTime, duration);
};

// 下载当前视频
const handleDownload = () => {
  if (!props.selectedRecording) {
    ElMessage.warning("请先选择要下载的录像");
    return;
  }

  try {
    const recording = props.selectedRecording;
    console.log("开始下载录像:", recording);

    // 使用录像的文件名作为下载文件名
    downloadVideoFile(recording.filePath, recording.fileName);

    ElMessage.success(`开始下载: ${recording.fileName}`);
    emit("download");
  } catch (error) {
    console.error("下载失败:", error);
    ElMessage.error("下载失败，请稍后重试");
  }
};

// 时间轴事件处理
const handleSelectRecording = (recording: RecordingInfo) => {
  emit("selectRecording", recording);
};

const handlePlayRecording = (recording: RecordingInfo) => {
  emit("playRecording", recording);
};

const handleDownloadRecording = (recording: RecordingInfo) => {
  emit("downloadRecording", recording);
};
</script>

<style scoped>
.video-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-right: 12px;
}

.video-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.video-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  border: 2px dashed var(--el-border-color);
}

.placeholder-icon {
  color: var(--el-color-info);
  margin-bottom: 16px;
}

.placeholder-text {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.video-player {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player-wrapper {
  flex: 1;
  background: #000;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  margin-bottom: 16px;
}

.player-placeholder {
  text-align: center;
  color: #fff;
}

.debug-info {
  margin-top: 16px;
  font-size: 12px;
  color: #888;
  line-height: 1.5;
  text-align: left;
  max-width: 500px;
}
</style>
