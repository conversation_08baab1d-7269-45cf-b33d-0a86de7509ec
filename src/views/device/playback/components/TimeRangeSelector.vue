<template>
  <el-card shadow="never" class="time-selector-card">
    <template #header>
      <div class="card-header">
        <span>时间选择</span>
      </div>
    </template>
    <div class="time-selector-container">
      <el-form label-width="80px">
        <el-form-item label="时间范围：">
          <el-date-picker
            v-model="dateTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            :shortcuts="dateTimeShortcuts"
            :default-value="defaultDateRange"
            @change="handleTimeRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="small"
            style="width: 100%"
            :disabled="!selectedDevice || !dateTimeRange"
            :loading="loading"
            @click="handleQuery"
          >
            查询回放
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

import type { DeviceTreeNode } from "@/api/device";

// Props
interface Props {
  selectedDevice?: DeviceTreeNode | null;
  loading?: boolean;
  modelValue?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  selectedDevice: null,
  loading: false,
  modelValue: () => []
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: string[]];
  query: [timeRange: string[]];
}>();

// 计算属性
const dateTimeRange = computed({
  get: () => props.modelValue,
  set: value => emit("update:modelValue", value || [])
});

// 日期时间选择器快捷选项
const dateTimeShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];

const defaultDateRange = [
  new Date(new Date().setDate(new Date().getDate() - 7)), // 当前日期减去7天
  new Date() // 当前日期
] as [Date, Date];

// 方法
const handleTimeRangeChange = (value: string[]) => {
  emit("update:modelValue", value);
};

const handleQuery = () => {
  if (dateTimeRange.value && dateTimeRange.value.length === 2) {
    emit("query", dateTimeRange.value);
  }
};
</script>

<style scoped>
.time-selector-card {
  height: 100%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-selector-container {
  padding: 8px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
