<template>
  <el-card shadow="never" class="recordings-card">
    <template #header>
      <div class="card-header">
        <span>录像列表</span>
        <span
          v-if="recordings && recordings.length > 0"
          class="recordings-count"
        >
          ({{ recordings.length }}个文件)
        </span>
      </div>
    </template>

    <div class="recordings-container">
      <!-- 选中录像的详细信息 -->
      <div v-if="selectedRecording" class="selected-recording-info">
        <div class="selected-info-content">
          <div class="selected-title">
            <el-icon><VideoCamera /></el-icon>
            <span>{{ selectedRecording.fileName }}</span>
            <el-tag v-if="isPlaying" size="small" type="success">播放中</el-tag>
          </div>
          <div class="selected-details">
            <div>
              {{ formatDisplayTime(selectedRecording.startTime) }} -
              {{ formatDisplayTime(selectedRecording.endTime) }}
            </div>
            <div>
              {{ formatDuration(selectedRecording.duration) }} |
              {{ formatFileSize(selectedRecording.fileSize) }}
            </div>
          </div>
        </div>
        <div class="selected-actions">
          <el-button
            type="primary"
            size="small"
            :loading="videoLoading"
            @click="playRecording(selectedRecording)"
          >
            <el-icon><VideoPlay /></el-icon>
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="downloadRecording(selectedRecording)"
          >
            <el-icon><Download /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 录像列表 -->
      <div
        v-if="recordings && recordings.length > 0"
        class="recordings-list-vertical"
      >
        <div class="list-actions">
          <el-button size="small" @click="downloadAllRecordings">
            <el-icon><Download /></el-icon>
            批量下载
          </el-button>
        </div>

        <div class="recordings-vertical-container">
          <div
            v-for="recording in recordings || []"
            :key="`vertical-${recording.id}`"
            class="recording-item-vertical"
            :class="{
              selected: selectedRecording?.id === recording.id,
              playing: isPlaying && selectedRecording?.id === recording.id
            }"
            @click="selectRecording(recording)"
          >
            <div class="recording-header-vertical">
              <div class="recording-title-vertical">
                <el-icon><VideoCamera /></el-icon>
                <span>{{ recording.fileName }}</span>
              </div>
              <div class="recording-actions-vertical">
                <el-button
                  size="small"
                  type="primary"
                  circle
                  :loading="
                    videoLoading && selectedRecording?.id === recording.id
                  "
                  @click.stop="playRecording(recording)"
                >
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  circle
                  @click.stop="downloadRecording(recording)"
                >
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="recording-info-vertical">
              <div class="info-row-vertical">
                <span class="info-time">
                  {{ formatDisplayTime(recording.startTime) }} -
                  {{ formatDisplayTime(recording.endTime) }}
                </span>
              </div>
              <div class="info-row-vertical">
                <span class="info-duration">{{
                  formatDuration(recording.duration)
                }}</span>
                <span class="info-size">{{
                  formatFileSize(recording.fileSize)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无录像提示 -->
      <div
        v-if="!recordings || recordings.length === 0"
        class="no-recordings-vertical"
      >
        <el-empty description="请选择设备和时间范围查询录像文件" />
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { VideoCamera, VideoPlay, Download } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { downloadVideoFile, type RecordingInfo } from "@/api/device";

// Props
interface Props {
  recordings?: RecordingInfo[];
  selectedRecording?: RecordingInfo | null;
  videoLoading?: boolean;
  isPlaying?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  recordings: () => [],
  selectedRecording: null,
  videoLoading: false,
  isPlaying: false
});

// Emits
const emit = defineEmits<{
  selectRecording: [recording: RecordingInfo];
  playRecording: [recording: RecordingInfo];
  downloadRecording: [recording: RecordingInfo];
  downloadAll: [];
}>();

// 方法
const selectRecording = (recording: RecordingInfo) => {
  emit("selectRecording", recording);
};

const playRecording = (recording: RecordingInfo) => {
  emit("playRecording", recording);
};

const downloadRecording = (recording: RecordingInfo) => {
  emit("downloadRecording", recording);
};

// 批量下载所有录像
const downloadAllRecordings = () => {
  if (!props.recordings || props.recordings.length === 0) {
    ElMessage.warning("暂无录像文件可下载");
    return;
  }

  ElMessageBox.confirm(
    `确定要下载所有 ${props.recordings.length} 个录像文件吗？`,
    "批量下载确认",
    {
      confirmButtonText: "确定下载",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      let downloadCount = 0;
      const totalCount = props.recordings.length;

      props.recordings.forEach((recording, index) => {
        // 延迟下载，避免同时发起太多请求
        setTimeout(() => {
          try {
            downloadVideoFile(recording.filePath, recording.fileName);
            downloadCount++;

            if (downloadCount === totalCount) {
              ElMessage.success(`已开始下载 ${totalCount} 个录像文件`);
            }
          } catch (error) {
            console.error(`下载 ${recording.fileName} 失败:`, error);
          }
        }, index * 500); // 每个文件间隔500ms
      });

      emit("downloadAll");
    })
    .catch(() => {
      console.log("用户取消批量下载");
    });
};

// 格式化函数
const formatDisplayTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
};

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
</script>

<style scoped>
.recordings-card {
  height: 100%;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.recordings-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recordings-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.recordings-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.selected-recording-info {
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  margin-bottom: 12px;
  border: 1px solid var(--el-border-color);
  flex-shrink: 0;
}

.selected-info-content {
  margin-bottom: 8px;
}

.selected-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.selected-title span {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selected-details {
  font-size: 11px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.selected-actions {
  display: flex;
  gap: 8px;
}

.recordings-list-vertical {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.list-actions {
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color);
  margin-bottom: 8px;
  flex-shrink: 0;
}

.recordings-vertical-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
  min-height: 0;
  max-height: calc(100vh - 400px);
}

.recording-item-vertical {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recording-item-vertical:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-7);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recording-item-vertical.selected {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.recording-item-vertical.playing {
  background: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);
}

.recording-header-vertical {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.recording-title-vertical {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  overflow: hidden;
}

.recording-title-vertical span {
  font-size: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recording-actions-vertical {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.recording-info-vertical {
  font-size: 11px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.info-row-vertical {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.info-row-vertical:last-child {
  margin-bottom: 0;
}

.info-time {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-duration,
.info-size {
  flex-shrink: 0;
  margin-left: 8px;
}

.no-recordings-vertical {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
