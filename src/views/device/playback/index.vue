<template>
  <div v-permission="'/device/playback/view'" class="device-playback-container">
    <!-- 主要内容区域 -->
    <div class="content-section">
      <el-row :gutter="16" style="height: 100%; overflow: hidden">
        <!-- 左侧：设备树和时间选择 -->
        <el-col :span="5" style="height: 100%; overflow: hidden">
          <div class="left-panel">
            <!-- 设备树选择器 -->
            <div class="device-tree-section">
              <DeviceTreeSelector
                :selected-device="selectedDevice"
                @device-select="handleDeviceSelect"
                @refresh="handleDeviceTreeRefresh"
              />
            </div>

            <!-- 时间范围选择器 -->
            <div class="time-selector-section">
              <TimeRangeSelector
                v-model="timeParams.dateTimeRange"
                :selected-device="selectedDevice"
                :loading="recordingsStore.loading.value"
                @query="handleQueryPlayback"
              />
            </div>
          </div>
        </el-col>

        <!-- 中间：视频播放区域 -->
        <el-col :span="13" style="height: 100%; overflow: hidden">
          <VideoPlayerArea
            :selected-device="selectedDevice"
            :current-video="recordingsStore.currentVideo.value"
            :selected-recording="recordingsStore.selectedRecording.value"
            :current-video-url="recordingsStore.currentVideoUrl.value"
            :video-loading="recordingsStore.videoLoading.value"
            :recordings="recordingsStore.recordingsList.value"
            :time-range="timeParams.dateTimeRange"
            :is-playing="recordingsStore.isPlaying.value"
            @video-load-start="recordingsStore.handleVideoLoadStart"
            @video-can-play="recordingsStore.handleVideoCanPlay"
            @video-error="recordingsStore.handleVideoError"
            @video-time-update="recordingsStore.handleVideoTimeUpdate"
            @download="handleDownloadVideo"
            @select-recording="recordingsStore.selectRecording"
            @play-recording="recordingsStore.playRecording"
            @download-recording="handleDownloadRecording"
          />
        </el-col>

        <!-- 右侧：录像列表 -->
        <el-col :span="6" style="height: 100%; overflow: hidden">
          <RecordingList
            :recordings="recordingsStore.recordingsList.value"
            :selected-recording="recordingsStore.selectedRecording.value"
            :video-loading="recordingsStore.videoLoading.value"
            :is-playing="recordingsStore.isPlaying.value"
            @select-recording="recordingsStore.selectRecording"
            @play-recording="recordingsStore.playRecording"
            @download-recording="handleDownloadRecording"
            @download-all="handleDownloadAllRecordings"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  downloadVideoFile,
  type DeviceTreeNode,
  type RecordingInfo
} from "@/api/device";

// 导入子组件
import DeviceTreeSelector from "./components/DeviceTreeSelector.vue";
import TimeRangeSelector from "./components/TimeRangeSelector.vue";
import VideoPlayerArea from "./components/VideoPlayerArea.vue";
import RecordingTimeline from "./components/RecordingTimeline.vue";
import RecordingList from "./components/RecordingList.vue";

// 导入composables
import { useRecordings } from "./composables/useRecordings";

defineOptions({
  name: "DevicePlayback"
});

// 使用composables
const recordingsStore = useRecordings();

// 本地状态
const selectedDevice = ref<DeviceTreeNode | null>(null);

// 格式化本地时间的辅助函数
const formatLocalDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 时间参数 - 设置默认时间范围（近一周）
const timeParams = reactive({
  dateTimeRange: [
    formatLocalDateTime(new Date(new Date().setDate(new Date().getDate() - 3))),
    formatLocalDateTime(new Date())
  ]
});

// 事件处理方法
const handleDeviceSelect = (device: DeviceTreeNode) => {
  selectedDevice.value = device;
};

const handleDeviceTreeRefresh = () => {};

const handleQueryPlayback = (timeRange: string[]) => {
  if (!selectedDevice.value || !timeRange || timeRange.length < 2) {
    return;
  }

  recordingsStore.queryRecordings(selectedDevice.value, timeRange);
};

const handleDownloadVideo = () => {
  console.log("下载当前视频");
};

const handleDownloadRecording = (recording: RecordingInfo) => {
  try {
    console.log("开始下载录像:", recording);
    downloadVideoFile(recording.filePath, recording.fileName);
    ElMessage.success(`开始下载: ${recording.fileName}`);
  } catch (error) {
    console.error("下载失败:", error);
    ElMessage.error("下载失败，请稍后重试");
  }
};

const handleDownloadAllRecordings = () => {
  console.log("批量下载所有录像");
};

onMounted(() => {});
</script>

<style scoped>
.device-playback-container {
  padding: 1%;
  height: 89vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
  overflow: hidden;
  box-sizing: border-box;
}

.content-section {
  flex: 1;
  min-height: 0;
  max-height: 100%;
  overflow: hidden;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  overflow: hidden;
}

.device-tree-section {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.time-selector-section {
  flex-shrink: 0;
  height: 180px;
}

.video-section {
  height: 98%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
