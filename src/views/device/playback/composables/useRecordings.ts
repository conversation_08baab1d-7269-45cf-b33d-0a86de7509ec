import { ref } from "vue";
import { ElMessage } from "element-plus";
import {
  getRecordings,
  generateStreamName,
  getVideoPlayUrl,
  type RecordingInfo,
  type DeviceTreeNode
} from "@/api/device";

export function useRecordings() {
  // 状态
  const loading = ref(false);
  const recordingsList = ref<RecordingInfo[]>([]);
  const selectedRecording = ref<RecordingInfo | null>(null);
  const currentVideo = ref<any>(null);

  // 视频播放相关状态
  const currentVideoUrl = ref<string>("");
  const videoLoading = ref(false);
  const videoProgress = ref(0);
  const currentTime = ref(0);
  const videoDuration = ref(3600);
  const isPlaying = ref(false);

  // 查询录像
  const queryRecordings = async (
    selectedDevice: DeviceTreeNode,
    timeRange: string[]
  ) => {
    if (!selectedDevice || !timeRange || timeRange.length < 2) {
      return;
    }

    loading.value = true;
    try {
      // 生成streamName（去除设备编号和IP中的冒号和点）
      const streamName = generateStreamName(
        selectedDevice.deviceInfo.equipmentNumber,
        selectedDevice.deviceInfo.ip
      );

      // 准备查询参数
      const queryParams = {
        streamName,
        startTime: timeRange[0],
        endTime: timeRange[1]
      };

      // 调用录像查询API
      const result = await getRecordings(queryParams);

      if (result.code === 200) {
        recordingsList.value = result.data || [];
        currentVideo.value = {
          device: selectedDevice,
          recordings: recordingsList.value,
          queryParams
        };

        ElMessage.success(`查询到 ${recordingsList.value.length} 个录像文件`);
      } else {
        ElMessage.error(
          `录像查询失败: ${(result as any).msg || result.message}`
        );
        recordingsList.value = [];
      }
    } catch (error) {
      console.error("录像查询失败:", error);
      ElMessage.error("录像查询失败，请检查网络连接");
      recordingsList.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 选择录像
  const selectRecording = (recording: RecordingInfo) => {
    selectedRecording.value = recording;
    console.log("选择录像:", recording);

    // 自动播放选中的录像
    playRecording(recording);
  };

  // 播放录像
  const playRecording = (recording: RecordingInfo) => {
    console.log("播放录像完整信息:", recording);
    console.log("文件名:", recording.fileName);
    console.log("文件路径:", recording.filePath);

    try {
      // 选中当前录像
      selectedRecording.value = recording;

      // 生成播放URL，直接使用filePath
      const playUrl = getVideoPlayUrl(recording.filePath);
      currentVideoUrl.value = playUrl;

      // 设置播放状态
      isPlaying.value = true;

      console.log("视频播放URL:", playUrl);
      ElMessage.success(`开始播放: ${recording.fileName}`);
    } catch (error) {
      console.error("生成播放URL失败:", error);
      ElMessage.error("播放失败，请检查录像文件");
      isPlaying.value = false;
    }
  };

  // 视频播放器事件处理
  const handleVideoLoadStart = () => {
    videoLoading.value = true;
    console.log("视频开始加载");
  };

  const handleVideoCanPlay = () => {
    videoLoading.value = false;
    console.log("视频可以播放");

    // 第一次播放时，尝试自动开始播放
    if (selectedRecording.value) {
      isPlaying.value = true;
      console.log("视频自动播放已启动");
    }
  };

  const handleVideoError = (error: string) => {
    videoLoading.value = false;
    isPlaying.value = false;
    console.error("视频播放错误:", error);
    ElMessage.error(`视频播放失败: ${error}`);
    // 清空当前视频URL，显示占位符
    currentVideoUrl.value = "";
  };

  const handleVideoTimeUpdate = (
    currentTimeValue: number,
    duration: number
  ) => {
    currentTime.value = currentTimeValue;
    videoDuration.value = duration;
    // 更新播放进度
    if (duration > 0) {
      videoProgress.value = (currentTimeValue / duration) * 100;
    }
  };

  // 重置状态
  const resetRecordings = () => {
    recordingsList.value = [];
    selectedRecording.value = null;
    currentVideo.value = null;
    currentVideoUrl.value = "";
    videoLoading.value = false;
    videoProgress.value = 0;
    currentTime.value = 0;
    isPlaying.value = false;
  };

  return {
    // 状态
    loading,
    recordingsList,
    selectedRecording,
    currentVideo,
    currentVideoUrl,
    videoLoading,
    videoProgress,
    currentTime,
    videoDuration,
    isPlaying,

    // 方法
    queryRecordings,
    selectRecording,
    playRecording,
    handleVideoLoadStart,
    handleVideoCanPlay,
    handleVideoError,
    handleVideoTimeUpdate,
    resetRecordings
  };
}
