<template>
  <el-tab-pane label="班组" name="group">
    <div class="filter-container">
      <el-button
        v-permission="'/schedule-management/shift/group/createWithShifts'"
        @click="openDialog()"
        >新增班组</el-button
      >
    </div>
    <el-table :data="groupList" style="width: 100%">
      <el-table-column prop="groupName" label="班组名称" />
      <el-table-column
        prop="workPattern"
        label="工作模式"
        :formatter="formatWorkPattern"
      />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="updateTime" label="更新时间" />
      <el-table-column label="班次">
        <template #default="{ row }">
          <div v-if="row.shifts && row.shifts.length">
            <div v-for="shift in row.shifts" :key="shift.id">
              {{ shift.shiftName }} ({{ shift.startTime }} -
              {{ shift.endTime }})
            </div>
          </div>
          <div v-else>无班次</div>
        </template>
      </el-table-column>
      <el-table-column
        v-all-permission="[
          '/schedule-management/shift/group/update',
          '/schedule-management/shift/group/Id'
        ]"
        label="操作"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            v-permission="'/schedule-management/shift/group/update'"
            class="reset-margin"
            link
            type="primary"
            :icon="useRenderIcon(EditPen)"
            @click="openDialog(row)"
            >编辑</el-button
          >
          <el-popconfirm
            :title="`确定要删除 ${row.groupName} 班组吗？`"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="deleteGroup(row.id)"
          >
            <template v-slot:reference>
              <el-button
                v-permission="'/schedule-management/shift/group/Id'"
                type="danger"
                class="reset-margin"
                link
                :icon="useRenderIcon(Delete)"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalGroups"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <el-dialog v-model="dialogVisible" :title="dialogTitle">
      <el-form :model="form" label-width="80px">
        <el-form-item label="班组名称">
          <el-input v-model="form.groupName" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工作天数">
              <el-input
                v-model.number="workDays"
                @input="generateWorkPattern"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="休息天数">
              <el-input
                v-model.number="restDays"
                @input="generateWorkPattern"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工作模式">
          <el-input v-model="form.workPattern" readonly />
        </el-form-item>
        <el-form-item label="选择班次">
          <el-select
            v-model="form.assignedShifts"
            multiple
            placeholder="请选择班次"
          >
            <el-option
              v-for="shift in shiftList"
              :key="shift.id"
              :label="`${shift.shiftName} (${shift.startTime} - ${shift.endTime})`"
              :value="shift.id"
            >
              <span>{{ shift.shiftName }}</span>
              <span style="float: right; font-size: 13px; color: #8492a6">
                {{ shift.startTime }} - {{ shift.endTime }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGroup">保存</el-button>
      </template>
    </el-dialog>
  </el-tab-pane>
</template>

<script setup lang="ts">
import {
  deleteGroup as apiDeleteGroup,
  createGroupWithShifts,
  getGroupList,
  getShiftList,
  updateGroup
} from "@/api/schedule";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";

const dialogVisible = ref(false);
const dialogTitle = ref("新增班组");
const form = reactive({
  id: null,
  groupName: "",
  description: "",
  workPattern: "",
  assignedShifts: []
});

const groupList = ref([]);
const shiftList = ref([]);
const workDays = ref(0);
const restDays = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const totalGroups = ref(0);

const openDialog = (group = null) => {
  if (group) {
    dialogTitle.value = "修改班组";
    Object.assign(form, group);
    const pattern = form.workPattern.split(",");
    workDays.value = pattern.filter(day => day === "1").length;
    restDays.value = pattern.filter(day => day === "0").length;
    form.assignedShifts = group.shifts.map(shift => shift.id);
  } else {
    dialogTitle.value = "新增班组";
    Object.assign(form, {
      id: null,
      groupName: "",
      description: "",
      workPattern: "",
      assignedShifts: []
    });
    workDays.value = 0;
    restDays.value = 0;
  }
  loadShifts();
  dialogVisible.value = true;
};

const generateWorkPattern = () => {
  const workPatternArray = [];
  for (let i = 0; i < workDays.value; i++) {
    workPatternArray.push("1");
  }
  for (let i = 0; i < restDays.value; i++) {
    workPatternArray.push("0");
  }
  form.workPattern = workPatternArray.join(",");
};

const saveGroup = () => {
  const requestData = {
    id: form.id,
    groupName: form.groupName,
    description: form.description,
    workPattern: form.workPattern,
    shiftIds: form.assignedShifts
  };

  const apiCall = form.id ? updateGroup : createGroupWithShifts;

  apiCall(requestData)
    .then(res => {
      if (res.code === 200) {
        ElMessage.success(
          form.id ? "班组更新成功" : "班组创建成功并已绑定班次"
        );
        dialogVisible.value = false;
        loadGroups();
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error(form.id ? "班组更新失败" : "班组创建失败");
    });
};

const loadGroups = () => {
  getGroupList(currentPage.value, pageSize.value)
    .then(res => {
      if (res.code === 200) {
        groupList.value = res.data.records;
        totalGroups.value = res.data.total;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取班组失败");
    });
};

const loadShifts = () => {
  getShiftList()
    .then(res => {
      if (res.code === 200) {
        shiftList.value = res.data;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取班次失败");
    });
};

const deleteGroup = id => {
  apiDeleteGroup(id)
    .then(res => {
      if (res.code === 200) {
        ElMessage.success("班组删除成功");
        loadGroups();
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("班组删除失败");
    });
};

const formatWorkPattern = row => {
  const pattern = row.workPattern.split(",");
  const workDays = pattern.filter(day => day === "1").length;
  const restDays = pattern.filter(day => day === "0").length;
  return `上${workDays}休${restDays}`;
};

const handlePageChange = page => {
  currentPage.value = page;
  loadGroups();
};

const handleSizeChange = size => {
  pageSize.value = size;
  loadGroups();
};

onMounted(() => {
  loadGroups();
  loadShifts();
});
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  background-color: #fff;
}
</style>
