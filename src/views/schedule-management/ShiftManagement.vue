<template>
  <el-tab-pane label="班次" name="shift">
    <div class="filter-container">
      <el-button
        v-permission="'/schedule-management/shift/add'"
        @click="openShiftDialog()"
        >新增班次</el-button
      >
    </div>
    <el-table :data="shiftList" style="width: 100%">
      <el-table-column prop="shiftName" label="班次名称" />
      <el-table-column prop="startTime" label="开始时间" />
      <el-table-column prop="endTime" label="结束时间" />
      <el-table-column prop="description" label="描述" />
      <el-table-column
        v-all-permission="[
          '/schedule-management/shift/update',
          '/schedule-management/shift/Id'
        ]"
        label="操作"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            v-permission="'/schedule-management/shift/update'"
            class="reset-margin"
            link
            type="primary"
            :icon="useRenderIcon(EditPen)"
            @click="openShiftDialog(row)"
            >编辑</el-button
          >
          <el-popconfirm
            :title="`确定要删除 ${row.shiftName} 班次吗？`"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="deleteShift(row.id)"
          >
            <template v-slot:reference>
              <el-button
                v-permission="'/schedule-management/shift/Id'"
                type="danger"
                class="reset-margin"
                link
                :icon="useRenderIcon(Delete)"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalShifts"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <el-dialog v-model="shiftDialogVisible" :title="shiftDialogTitle">
      <el-form :model="shiftForm">
        <el-form-item label="班次名称">
          <el-input v-model="shiftForm.shiftName" />
        </el-form-item>
        <el-form-item label="开始时间">
          <el-time-picker
            v-model="shiftForm.startTime"
            placeholder="选择开始时间"
            format="HH:mm"
            value-format="HH:mm"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-time-picker
            v-model="shiftForm.endTime"
            placeholder="选择结束时间"
            format="HH:mm"
            value-format="HH:mm"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="shiftForm.description" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="shiftDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveShift">保存</el-button>
      </template>
    </el-dialog>
  </el-tab-pane>
</template>

<script setup lang="ts">
import {
  addShift,
  deleteShift as apiDeleteShift,
  getShiftListPage,
  updateShift
} from "@/api/schedule";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";

const shiftDialogVisible = ref(false);
const shiftDialogTitle = ref("新增班次");
const shiftForm = reactive({
  id: null,
  shiftName: "",
  startTime: "",
  endTime: "",
  description: ""
});

const shiftList = ref([]);
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页条数
const totalShifts = ref(0); // 总班次数

const loadShifts = () => {
  getShiftListPage(currentPage.value, pageSize.value) // 使用新的接口
    .then(res => {
      if (res.code === 200) {
        shiftList.value = res.data.records; // 更新班次列表
        totalShifts.value = res.data.total; // 更新总班次数
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取班次失败");
    });
};

const handlePageChange = page => {
  currentPage.value = page; // 更新当前页码
  loadShifts(); // 重新加载班次数据
};

const handleSizeChange = size => {
  pageSize.value = size; // 更新每页条数
  loadShifts(); // 重新加载班次数据
};

const openShiftDialog = (shift = null) => {
  if (shift) {
    shiftDialogTitle.value = "修改班次";
    Object.assign(shiftForm, shift);
  } else {
    shiftDialogTitle.value = "新增班次";
    Object.assign(shiftForm, {
      id: null,
      shiftName: "",
      startTime: "",
      endTime: "",
      description: ""
    });
  }
  shiftDialogVisible.value = true;
};

const saveShift = () => {
  const shiftData = {
    id: shiftForm.id,
    shiftName: shiftForm.shiftName,
    startTime: shiftForm.startTime,
    endTime: shiftForm.endTime,
    description: shiftForm.description
  };

  const apiCall = shiftForm.id ? updateShift : addShift;

  apiCall(shiftData)
    .then(res => {
      if (res.code === 200) {
        ElMessage.success(shiftForm.id ? "班次更新成功" : "班次添加成功");
        shiftDialogVisible.value = false;
        loadShifts(); // 重新加载班次数据
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error(shiftForm.id ? "班次更新失败" : "班次添加失败");
    });
};

const deleteShift = id => {
  apiDeleteShift(id)
    .then(res => {
      if (res.code === 200) {
        ElMessage.success("班次删除成功");
        loadShifts(); // 重新加载班次数据
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("班次删除失败");
    });
};

onMounted(() => {
  loadShifts(); // 初始加载班次数据
});
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  background-color: #fff;
}
</style>
