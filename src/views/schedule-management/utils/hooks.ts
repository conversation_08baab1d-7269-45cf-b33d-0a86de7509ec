import { reactive, ref } from "vue";
import { getGroupList } from "@/api/schedule";
import { ElMessage } from "element-plus";

export function useSchedule() {
  const filterParams = reactive({
    userId: "",
    date: new Date(),
    groupName: ""
  });
  const dataList = ref([]);
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10
  });

  const onSearch = () => {
    loading.value = true;
    getGroupList(pagination.currentPage, pagination.pageSize)
      .then(res => {
        if (res.code === 200) {
          dataList.value = res.data;
        } else {
          ElMessage.error(res.message);
        }
      })
      .catch(() => {
        ElMessage.error("获取数据失败");
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    filterParams.userId = "";
    filterParams.date = new Date();
    filterParams.groupName = "";
    onSearch();
  };

  return {
    filterParams,
    loading,
    dataList,
    onSearch,
    resetFilterForm
  };
}
