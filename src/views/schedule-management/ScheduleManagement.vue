<template>
  <el-tab-pane label="排班" name="assign">
    <div class="schedule-container">
      <div class="left-panel">
        <tree
          ref="treeRef"
          :class="[
            'mr-2',
            'transition-all duration-300 ease-in-out overflow-hidden',
            'w-[300px] opacity-100'
          ]"
          @tree-select="onTreeSelect"
        />
      </div>

      <div class="right-panel">
        <div class="panel-header" />
        <el-table :data="employeeList" style="width: 100%">
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="phone" label="电话" width="150" />
          <el-table-column prop="sex" label="性别" width="80" />
          <el-table-column prop="deptName" label="职位" width="120" />
          <el-table-column prop="groupName" label="班组名称" width="150" />
          <el-table-column prop="site" label="点位" width="*" />
          <el-table-column prop="startDate" label="生效日期" width="150" />
          <el-table-column prop="endDate" label="结束日期" width="150" />
          <el-table-column
            prop="workPattern"
            label="工作模式"
            :formatter="formatWorkPattern"
            width="180"
          />
          <el-table-column
            v-all-permission="[
              '/schedule-management/schedule/employees/userId',
              '/schedule-management/user/shift-group/assign',
              '/schedule-management/schedule/userId'
            ]"
            label="操作"
            width="250"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                v-permission="'/schedule-management/schedule/employees/userId'"
                size="small"
                @click="openCalendarDialog(row)"
              >
                查看
              </el-button>
              <el-button
                v-permission="'/schedule-management/user/shift-group/assign'"
                size="small"
                @click="openAssignDialog(row)"
              >
                选班组
              </el-button>
              <el-button
                v-permission="'/schedule-management/schedule/userId'"
                type="danger"
                size="small"
                @click="handleCancelSchedule(row.userId)"
              >
                取消排班
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <el-dialog v-model="assignDialogVisible" title="选择排班信息">
      <el-form label-width="80px" :model="assignForm">
        <el-form-item label="班组">
          <el-select v-model="assignForm.groupId" placeholder="选择班组">
            <el-option
              v-for="group in groupList"
              :key="group.id"
              :label="group.groupName"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生效日期">
          <el-date-picker
            v-model="assignForm.startDate"
            type="date"
            placeholder="选择生效日期"
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="assignForm.endDate"
            type="date"
            placeholder="选择结束日期（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="assignEmployees">确认</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="calendarDialogVisible"
      width="80vw"
      height="80vh"
      title="查看排班"
    >
      <calendar-view
        v-if="calendarDialogVisible"
        ref="calendarRef"
        :userId="assignForm.userId"
      />
      <template #footer>
        <el-button @click="calendarDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </el-tab-pane>
</template>

<script setup lang="ts">
import {
  assignSchedule,
  cancelSchedule,
  getGroupList,
  getPersuaders,
  listAllGroups
} from "@/api/schedule";
import { handleFormField } from "@/utils/tree";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import CalendarView from "./CalendarView.vue";
import tree from "./tree.vue";
const employeeList = ref([]);
const groupList = ref([]);
const assignDialogVisible = ref(false);
const calendarDialogVisible = ref(false);
const calendarRef = ref();
const assignForm = reactive({
  groupId: null,
  userId: null,
  fixedShiftId: 1,
  startDate: null,
  endDate: null
});
const selectedLocation = ref(null);
const treeData = ref([]);

const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const openAssignDialog = row => {
  assignForm.userId = row.userId;
  assignDialogVisible.value = true;
  sendRequestForAssignment(row.userId);
};

const sendRequestForAssignment = async userId => {
  try {
    const response = await listAllGroups();
    if (response.code === 200) {
      groupList.value = response.data;
    } else {
      console.error("Failed to load groups:", response.message);
    }
  } catch (error) {
    console.error("Failed to send request:", error);
  }
};

const openCalendarDialog = row => {
  assignForm.userId = row.userId;
  calendarDialogVisible.value = true;
};

const loadEmployees = () => {
  const params = {
    curPage: pagination.currentPage,
    pageSize: pagination.pageSize,
    ...handleFormField(selectedLocation.value, treeData.value, "label")
  };

  getPersuaders(params)
    .then(res => {
      if (res.code === 200) {
        employeeList.value = res.data.records;
        pagination.total = res.data.total;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取员工列表失败");
    });
};

const loadGroups = () => {
  getGroupList(pagination.currentPage, pagination.pageSize)
    .then(res => {
      if (res.code === 200) {
        groupList.value = res.data;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取班组失败");
    });
};

const assignEmployees = () => {
  // 前端只选择日期，后端需要datetime格式
  // 生效日期使用当天的00:00:00
  const formattedDate =
    dayjs(assignForm.startDate).format("YYYY-MM-DD") + " 00:00:00";

  let formattedEndDate = null;
  if (assignForm.endDate) {
    formattedEndDate =
      dayjs(assignForm.endDate).format("YYYY-MM-DD") + " 00:00:00";
  }

  const payload = {
    ...assignForm,
    startDate: formattedDate,
    endDate: formattedEndDate
  };

  assignSchedule(payload)
    .then(res => {
      if (res.code === 200) {
        ElMessage.success("员工分配成功");
        assignDialogVisible.value = false;
        loadEmployees();
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {
      ElMessage.error("员工分配失败");
    });
};

const onTreeSelect = (node, dataList) => {
  treeData.value = dataList;
  selectedLocation.value = node;
  loadEmployees();
};

const formatWorkPattern = row => {
  if (!row.workPattern) return "";
  const pattern = row.workPattern.split(",");
  const workDays = pattern.filter(day => day === "1").length;
  const restDays = pattern.filter(day => day === "0").length;
  return `上${workDays}休${restDays}`;
};

const handleSizeChange = size => {
  pagination.pageSize = size;
  loadEmployees();
};

const handleCurrentChange = page => {
  pagination.currentPage = page;
  loadEmployees();
};

const handleCancelSchedule = async (id: number) => {
  try {
    const response = await cancelSchedule(id);
    if (response.code === 200) {
      ElMessage.success("排班已取消");
      loadEmployees();
    } else {
      ElMessage.error(`取消失败: ${response.message}`);
    }
  } catch (error) {
    ElMessage.error(`取消失败: ${error.message}`);
  }
};

onMounted(() => {
  loadEmployees();
  loadGroups();
});
</script>

<style lang="scss" scoped>
.schedule-container {
  display: flex;
  gap: 16px;
  height: 100%;

  .left-panel {
    display: flex;
    flex-direction: column;
    width: 300px;
    min-width: 300px;
    border-right: 1px solid var(--el-border-color-light);
  }

  .right-panel {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 16px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;

    :deep(.el-table) {
      flex: 1;
    }

    .pagination-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }
}
</style>
