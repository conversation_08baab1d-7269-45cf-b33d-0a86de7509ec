<template>
  <div class="schedule-management-container">
    <Tabs>
      <GroupManagement
        v-if="
          !notHasAllPermission([
            '/schedule-management/shift/group/createWithShifts',
            '/schedule-management/shift/group/Id',
            '/schedule-management/shift/group/update',
            '/schedule-management/shift/group/list'
          ])
        "
      />
      <ShiftManagement
        v-if="
          !notHasAllPermission([
            '/schedule-management/shift/add',
            '/schedule-management/shift/Id',
            '/schedule-management/shift/listPage',
            '/schedule-management/shift/update'
          ])
        "
      />
      <ScheduleManagement
        v-if="
          !notHasAllPermission([
            '/schedule-management/schedule/employees',
            '/schedule-management/schedule/employees/userId',
            '/schedule-management/user/shift-group/assign',
            '/schedule-management/schedule/userId'
          ])
        "
      />
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { notHasAllPermission } from "@/directives";
import GroupManagement from "./GroupManagement.vue";
import ScheduleManagement from "./ScheduleManagement.vue";
import ShiftManagement from "./ShiftManagement.vue";
import Tabs from "./Tabs.vue";
</script>

<style lang="scss" scoped>
.schedule-management-container {
  height: calc(100% - 50px);
}

:deep(.el-tabs) {
  height: 100%;
}

:deep(.el-tab-pane) {
  height: 100%;
}

:deep(.el-tab-pane) {
  display: flex;
  flex-direction: column;
}

:deep(.el-table--fit) {
  flex: 1;
}
</style>
