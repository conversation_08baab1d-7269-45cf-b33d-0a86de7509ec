<template>
  <el-calendar v-model="value">
    <template #date-cell="{ data }">
      <div class="day-content-class">
        <template v-if="currentDaySchedules(data).length > 0">
          <div class="header-class">
            <div class="day-class">
              {{ data.day.split("-").slice(1).join("-") }}
            </div>
          </div>
          <div class="paiban-class">
            <div
              v-for="(item, i) in currentDaySchedules(data)"
              :key="i"
              :class="['each-paiban-class', setWorkClass(item.startTime)]"
            >
              <el-tooltip
                :content="
                  formatTime(item.startTime) + '~' + formatTime(item.endTime)
                "
                placement="bottom"
                effect="light"
              >
                <div>
                  <div class="paiban-icon-class">
                    <IconifyIconOffline
                      :icon="
                        setIconClass(item.startTime) === '上午'
                          ? Sunrise
                          : setIconClass(item.startTime) === '中午'
                            ? Sunny
                            : setIconClass(item.startTime) === '下午'
                              ? Moon
                              : Sunset
                      "
                    />
                  </div>
                  <div class="paiban-name-class">
                    {{ item.shiftName }}
                  </div>
                </div>
              </el-tooltip>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="header-class">
            <div class="day-class">
              {{ data.day.split("-").slice(1).join("-") }}
            </div>
          </div>
          <div class="no-work-class">
            <div class="icon-class">
              <IconifyIconOffline :icon="Calendar" />
            </div>
            <div class="tips-class">暂无排班</div>
          </div>
        </template>
      </div>
    </template>
  </el-calendar>
</template>

<script lang="ts" setup>
import { getScheduleForMonth } from "@/api/schedule";
import { message } from "@/utils/message";
import Calendar from "@iconify-icons/ep/calendar";
import Moon from "@iconify-icons/ep/moon";
import Sunny from "@iconify-icons/ep/sunny";
import Sunrise from "@iconify-icons/ep/sunrise";
import Sunset from "@iconify-icons/ep/sunset";
import dayjs from "dayjs";
import { ElCalendar } from "element-plus";
import { defineExpose, onMounted, ref, watch } from "vue";

const props = defineProps<{
  userId: number;
}>();

const value = ref(new Date());
const dataList = ref([]);

// 使用 watch 监听时间的变化
watch(value, (newVal, oldVal) => {
  loadScheduleForMonth(newVal);
});

onMounted(() => {
  loadScheduleForMonth();
});

// 获取当天排班情况
const currentDaySchedules = ({ date }) => {
  const day = dayjs(date).date();
  const month = dayjs(date).month() + 1;
  return dataList.value.filter(item => {
    return (
      day === dayjs(item.scheduleDate).date() &&
      month === dayjs(item.scheduleDate).month() + 1
    );
  });
};

const setIconClass = (time: string) => {
  const value = getTimeOfDay(time);
  let classValue = "el-icon-sunrise-1";
  switch (value) {
    case "上午":
      classValue = "el-icon-sunrise-1";
      break;
    case "中午":
      classValue = "el-icon-sunny";
      break;
    case "下午":
      classValue = "el-icon-moon";
      break;
    case "晚上":
      classValue = "el-icon-moon";
      break;
    default:
      break;
  }
  return classValue;
};

const setWorkClass = (time: string | number[]) => {
  if (!time) {
    return "zao-work-class";
  }

  const value = getTimeOfDay(time);
  let classValue = "";
  switch (value) {
    case "上午":
      classValue = " zao-work-class";
      break;
    case "中午":
      classValue = "wan-work-class";
      break;
    case "下午":
      classValue = "ye-work-class";
      break;
    case "晚上":
      classValue = "ye-work-class";
      break;
  }
  return classValue;
};

// 判断时间范围
const getTimeOfDay = (time: string | number[]) => {
  if (!time) {
    return "上午";
  }

  // 处理 LocalTime 格式 [hours, minutes]
  let hours: number;
  if (Array.isArray(time)) {
    hours = time[0];
  } else if (typeof time === "string") {
    // 保留原有的字符串处理逻辑
    const [h] = time.split(":").map(Number);
    hours = h;
  } else {
    return "上午";
  }

  // 判断时间段
  if (hours >= 0 && hours < 11) {
    return "上午";
  }
  if (hours >= 11 && hours < 13) {
    return "中午";
  } else if (hours >= 13 && hours < 18) {
    return "下午";
  } else {
    return "晚上";
  }
};

// 格式化时间数组为字符串
const formatTime = (time: number[]) => {
  if (!Array.isArray(time)) return "";
  const [hours, minutes] = time;
  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
};

// 获取月份排班数据
const loadScheduleForMonth = (time: Date = new Date()) => {
  const startTime = dayjs(time).startOf("month").format("YYYY-MM-DD 00:00:00");
  const endTime = dayjs(time).endOf("month").format("YYYY-MM-DD 23:59:59");
  getScheduleForMonth(props.userId, startTime, endTime)
    .then(res => {
      if (res.code === 200) {
        dataList.value = res.data;
      }
    })
    .catch(() => {
      message(`获取排班信息失败`, {
        type: "error"
      });
    });
};

defineExpose({
  loadScheduleForMonth
});
</script>

<style scoped>
.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.event-title {
  margin: 0;
  font-size: 12px;
  color: #409eff;
}

:deep(.current) {
  height: 120px;
}

:deep(.el-calendar-day) {
  height: 100%;
  height: 120px;
}

.calender-class {
  width: 100%;
  height: 100%;
}

.is-selected {
  color: #1989fa;
}

.el-calendar__body {
  height: 85vh;
}

.el-calendar-table {
  height: 100%;
}

.el-calendar-day {
  height: 100% !important;
}

.day-content-class {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.header-class {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 28px;
}

.day-class {
  flex: 4;
}

.paiban-class {
  display: flex;
  flex: 4;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
}

.paiban-icon-class {
  display: flex;
  justify-content: center;
  margin: 8px 0 10px;
  font-size: 18px;
}

.paiban-name-class {
  padding-top: 10px;
  font-size: 12px;
}

.each-paiban-class {
  flex: 1;
  max-width: 50px;
  padding: 0 0 5px;
  margin: 5px 5px 0;
  text-align: center;
  border-radius: 5px;
}

.zao-work-class {
  color: #11be11;
  background-color: #d9ffd9;
}

.wan-work-class {
  color: #fccb2c;
  background-color: #fff0bd;
}

.ye-work-class {
  color: #2dabff;
  background-color: #ddeffb;
}

.no-work-class {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #c1c0c0;
  text-align: center;
}

.icon-class {
  margin-bottom: 10px;
  font-size: 14px;
}

/* 侧边弹窗 */
.add-btn-class {
  float: right;
  margin: 10px;
}

.change-date-drawer-class .el-calendar__body {
  height: 45%;
}

.change-date-drawer-class .day-content-class {
  height: 30px;
}

.disabled-date-class {
  color: #ccc;
  pointer-events: none;
}
</style>
