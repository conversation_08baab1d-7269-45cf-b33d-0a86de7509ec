<template>
  <el-tabs v-model="activeTab" @tab-change="handleTabChange">
    <slot />
  </el-tabs>
</template>

<script setup lang="ts">
import { notHasAllPermission } from "@/directives";
import { onBeforeMount, ref } from "vue";

const activeTab = ref("assign");

onBeforeMount(() => {
  // 判断默认的面板类型
  const unexistGroupPanel = notHasAllPermission([
    "/schedule-management/shift/group/createWithShifts",
    "/schedule-management/shift/group/Id",
    "/schedule-management/shift/group/update",
    "/schedule-management/shift/group/list"
  ]);
  const unexistShiftPanel = notHasAllPermission([
    "/schedule-management/shift/add",
    "/schedule-management/shift/Id",
    "/schedule-management/shift/update",
    "/schedule-management/shift/listPage"
  ]);
  if (!unexistGroupPanel) {
    activeTab.value = "group";
  } else if (!unexistShiftPanel) {
    activeTab.value = "shift";
  }
});

const handleTabChange = activeName => {};
</script>

<style lang="scss" scoped></style>
