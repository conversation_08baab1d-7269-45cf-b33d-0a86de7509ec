<script setup lang="ts">
import { ref, defineProps } from "vue";
import { FormProps } from "../utils/types";
import { formRules } from "../utils/rule";

const props = withDefaults(defineProps<FormProps>(), {
  formInLine: () => ({
    permissionIds: [],
    roleName: "",
    loading: false
  }),
  permissionOptions: () => []
});

function getRef() {
  return ruleFormRef.value;
}
const ruleFormRef = ref();
const newFormInline = ref(props.formInLine);
const defaultTreeProps = {
  children: "children",
  label: "permissionName",
  key: "permissionId"
};

const nodeCheckChange = (currentNode, instance) => {
  newFormInline.value.permissionIds = instance.checkedKeys.filter(
    id => typeof id === "number"
  );
};

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    v-loading="props.loading"
    :model="newFormInline"
    label-width="82px"
    :rules="formRules"
  >
    <el-form-item label="角色名称" prop="roleName">
      <el-input
        v-model="newFormInline.roleName"
        clearable
        placeholder="请输入角色名称"
      />
    </el-form-item>
    <div>
      <label class="el-form-item__label w-[80px]">选择权限</label>
    </div>
    <el-tree
      style="max-width: 600px"
      :data="props.permissionOptions"
      :default-checked-keys="newFormInline.permissionIds"
      show-checkbox
      node-key="permissionId"
      :props="defaultTreeProps"
      @check="nodeCheckChange"
    />
  </el-form>
</template>

<style lang="scss" scoped></style>
