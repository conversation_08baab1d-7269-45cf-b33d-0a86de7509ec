export interface FilterParams {
  roleName?: string;
}

export interface Role {
  permissionList: Permission[];
  roleCode: string;
  roleId: number;
  roleName: string;
}

export interface Permission {
  permissionCode: string;
  permissionId: number;
  plate: string;
  remarks: string;
}

export interface RoleTreeNode {
  permissionCode: string;
  permissionId: string | number;
  permissionName: string;
  children: RoleTreeNode[];
}

export interface RoleFormItemProps {
  roleName: string;
  permissionIds: number[];
}

export interface FormProps {
  formInLine: RoleFormItemProps;
  permissionOptions: RoleTreeNode[];
  loading: boolean;
}
