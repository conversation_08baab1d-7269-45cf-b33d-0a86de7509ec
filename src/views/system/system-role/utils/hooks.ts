import { addRole, deleteRole, getRoleList, updateRole } from "@/api/role";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { ElTag } from "element-plus";
import { computed, h, onMounted, reactive, ref } from "vue";
import type { FilterParams, Role, RoleFormItemProps } from "./types";
// import { addDialog } from "@/components/ReDialog";
import { getPermissionList } from "@/api/permission";
import { addDrawer } from "@/components/ReDrawer";
import { notHasAllPermission } from "@/directives/auth";
import { handlePermissionTree } from "@/utils/tree";
import type { PaginationProps } from "@pureadmin/table";
import editForm from "../form/index.vue";

export function useRole(_tableRef?: unknown) {
  const filterParams = reactive<FilterParams>({});
  const dataList = ref([]);
  const permissionList = ref([]);
  const loading = ref(false);
  const formLoading = ref(false);
  const formRef = ref();
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns = ref<TableColumnList>([
    {
      label: "角色名称",
      prop: "roleName",
      align: "left",
      width: 260
    },
    {
      label: "角色编码",
      prop: "roleCode",
      width: 180
    },
    {
      label: "权限",
      prop: "permissionList", // 修改为 permissionList
      formatter: ({ permissionList }) => {
        let list = permissionList || [];
        return h(
          "div",
          list.map(item => {
            return h(
              ElTag,
              {
                key: item.permissionId,
                type: "primary",
                effect: "plain",
                style: { marginRight: "5px", marginBottom: "5px" }
              },
              { default: () => item.remarks }
            );
          })
        );
      }
    },
    {
      label: "创建时间",
      width: 190,
      prop: "createTime",
      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ]);

  const buttonClass = computed(() => {
    return [
      "!h-[20px]",
      "reset-margin",
      "!text-gray-500",
      "dark:!text-white",
      "dark:hover:!text-primary"
    ];
  });

  onMounted(() => {
    // 校验是否存在操作列表的按钮
    const isPermissionHandle = notHasAllPermission([
      "/system/system-role/role/updateRole",
      "/system/system-role/role/deleteRole",
      "/system/system-role/role/addRole"
    ]);
    if (isPermissionHandle) {
      columns.value = columns.value.filter(item => item.slot !== "operation");
    }
    onSearch();
    getPermissionList().then(res => {
      if (res.code === 200) {
        permissionList.value = handlePermissionTree(res.data);
      }
    });
  });

  const onSearch = () => {
    loading.value = true;
    getRoleList({
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...filterParams
    })
      .then(res => {
        if (res.code === 200) {
          dataList.value = res.data.records;
          pagination.total = res.data.total;
          pagination.pageSize = res.data.size;
          pagination.currentPage = res.data.current;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    onSearch();
  };

  function openDialog(title = "新增", row?: Role) {
    // 根据 row 是否存在初始化表单数据，并确保 permissionIds 是 number[]
    const initialFormData: RoleFormItemProps = row
      ? {
          permissionIds: (row.permissionList || []).map(item =>
            Number(item.permissionId)
          ), // 将 permissionIds 转换为 number[]
          roleName: row.roleName || ""
        }
      : {
          permissionIds: [],
          roleName: ""
        };
    addDrawer({
      title: `${title}角色`,
      props: {
        formInLine: initialFormData
      },
      sureBtnLoading: true,
      contentRenderer: () =>
        h(editForm, {
          ref: formRef,
          formInLine: initialFormData, // 确保传递正确的初始数据
          permissionOptions: permissionList.value,
          loading: formLoading.value
        }),
      beforeSure: (done, { options, closeLoading }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInLine as RoleFormItemProps;
        function chores() {
          message(`您${title}了角色：${curData.roleName}`, {
            type: "success"
          });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(valid => {
          if (valid) {
            formLoading.value = true;
            // 新增
            if (title === "新增") {
              addRole({
                roleName: curData.roleName,
                permissions: curData.permissionIds
              })
                .then(res => {
                  if (res.code === 200) {
                    chores();
                  }
                })
                .finally(() => {
                  closeLoading();
                  formLoading.value = false;
                });
            }
            // 编辑
            else {
              // 假设有一个 editRole API 方法用于编辑角色
              updateRole({
                roleId: row?.roleId, // 确保传递正确的 role ID
                permissions: curData.permissionIds
              })
                .then(res => {
                  if (res.code === 200) {
                    chores();
                  }
                })
                .finally(() => {
                  closeLoading();
                  formLoading.value = false;
                });
            }
          } else {
            closeLoading();
          }
        });
      }
    });
  }

  function handleDelete(row) {
    deleteRole(row.roleId)
      .then(res => {
        if (res.code === 200) {
          message(`您成功删除了角色: ${row.roleName}`, { type: "success" });
        } else {
          message(`删除角色失败: ${res.message || "未知错误"}`, {
            type: "error"
          });
        }
        onSearch();
      })
      .catch(err => {
        message(`删除角色时发生错误: ${err.message || "未知错误"}`, {
          type: "error"
        });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  return {
    loading,
    filterParams,
    dataList,
    pagination,
    columns,
    buttonClass,
    onSearch,
    resetFilterForm,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange
  };
}
