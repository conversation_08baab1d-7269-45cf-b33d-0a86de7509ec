<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";

import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import Refresh from "@iconify-icons/ep/refresh";
import AddFill from "@iconify-icons/ri/add-circle-line";
import { ref } from "vue";
import { useRole } from "./utils/hooks";

defineOptions({
  name: "SystemRole"
});

const filterRef = ref();
const tableRef = ref();
const {
  filterParams,
  loading,
  dataList,
  columns,
  pagination,
  onSearch,
  openDialog,
  handleDelete,
  resetFilterForm,
  handleSizeChange,
  handleCurrentChange
} = useRole(tableRef);
</script>

<template>
  <div>
    <el-form
      ref="filterRef"
      :inline="true"
      :model="filterParams"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="角色名称：" prop="roleName">
        <el-input
          v-model="filterParams.roleName"
          placeholder="请输入角色名称"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon(Refresh)"
          @click="resetFilterForm(filterRef)"
        >
          重置
        </el-button>
        <el-button
          v-permission="'/system/system-role/role/addRole'"
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog()"
        >
          新增角色
        </el-button>
      </el-form-item>
    </el-form>
    <PureTableBar title=" " :columns="columns" @refresh="onSearch">
      <template v-slot="{ size }">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              v-permission="'/system/system-role/role/updateRole'"
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="openDialog('编辑', row)"
            >
              编辑
            </el-button>
            <el-popconfirm
              :title="`是否确认删除角色:${row.roleName}?`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  v-permission="'/system/system-role/role/deleteRole'"
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon(Delete)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>
