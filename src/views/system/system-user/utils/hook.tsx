import { getRoleOptions } from "@/api/role";
import {
  addTreeNode,
  addUser,
  assignRole,
  deleteRoleList,
  deleteTreeNode,
  deleteUser,
  editUser,
  frozenUser,
  getAreaTree,
  getUserList,
  resetPassword,
  updateTreeNode
} from "@/api/system";
import userAvatar from "@/assets/user.jpg";
import ReCropperPreview from "@/components/ReCropperPreview";
import { addDialog } from "@/components/ReDialog";
import { notHasAllPermission } from "@/directives/auth";
import { message } from "@/utils/message";
import { handleFormField, handleTree } from "@/utils/tree";
import type { PaginationProps } from "@pureadmin/table";
import { deviceDetection, getKeyList, isAllEmpty } from "@pureadmin/utils";
import { zxcvbn } from "@zxcvbn-ts/core";
import dayjs from "dayjs";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElLoading,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rogress,
  ElTag
} from "element-plus";
import {
  type Ref,
  computed,
  h,
  onMounted,
  reactive,
  ref,
  watch,
  Fragment
} from "vue";
import { usePublicHooks } from "../../hooks";
import type { Role } from "../../system-role/utils/types";
import editForm from "../form/index.vue";
import roleForm from "../form/role.vue";
import NodeForm from "../NodeForm.vue";
import type {
  FormItemProps,
  RoleFormItemProps,
  TreeNode,
  User
} from "../utils/types";
import "./reset.css";
export function useUser(tableRef: Ref) {
  const form = reactive({
    // 左侧部门树的id
    name: undefined,
    phone: undefined,
    state: undefined
  });
  const formRef = ref();
  const ruleFormRef = ref();
  const dataList = ref<User[]>([]);
  const loading = ref(true);
  // 上传头像信息
  const avatarInfo = ref();
  const switchLoadMap = ref({});
  const { switchStyle } = usePublicHooks();
  const higherDeptOptions = ref();
  const treeData = ref<TreeNode[]>([]);
  const selectedTreeNode = ref<TreeNode | null>(null);
  const treeLoading = ref(true);
  const selectedNum = ref(0);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const roleOptions = ref<Role[]>([]);
  const columns = ref<TableColumnList>([
    {
      label: "用户名",
      prop: "name",
      width: 130
    },
    {
      label: "手机号",
      prop: "phone",
      minWidth: 130
    },
    {
      label: "性别",
      prop: "sex",
      width: 90
    },
    {
      label: "职位",
      prop: "deptName",
      minWidth: 90
    },
    {
      label: "角色",
      prop: "roles",
      formatter: ({ roles }) => {
        let list = roles || [];
        return h(
          "div",
          list.map(item => {
            return h(
              ElTag,
              {
                key: item.roleId,
                type: "primary",
                effect: "plain",
                style: { marginRight: "5px", marginBottom: "5px" }
              },
              { default: () => item.roleName }
            );
          })
        );
      }
    },
    {
      label: "市",
      prop: "city",
      minWidth: 90
    },
    {
      label: "县",
      prop: "county",
      minWidth: 90
    },
    {
      label: "乡镇",
      prop: "township",
      minWidth: 90
    },
    {
      label: "村",
      prop: "hamlet",
      minWidth: 90
    },
    {
      label: "点位",
      prop: "site",
      minWidth: 90
    },
    {
      label: "状态",
      prop: "state",
      minWidth: 90,
      cellRenderer: scope => (
        <el-switch
          disabled={notHasAllPermission(["/system/user/user/frozenUser"])}
          size={scope.props.size === "small" ? "small" : "default"}
          loading={switchLoadMap.value[scope.index]?.loading}
          v-model={scope.row.state}
          active-value={0}
          inactive-value={1}
          active-text="已启用"
          inactive-text="已停用"
          inline-prompt
          style={switchStyle.value}
          onChange={() => onChangeUserStatus(scope as any)}
        />
      )
    },
    {
      label: "创建时间",
      minWidth: 90,
      prop: "createTime",
      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ]);
  const buttonClass = computed(() => {
    return [
      "!h-[20px]",
      "reset-margin",
      "!text-gray-500",
      "dark:!text-white",
      "dark:hover:!text-primary"
    ];
  });
  // 重置的新密码
  const pwdForm = reactive({
    newPwd: ""
  });
  const pwdProgress = [
    { color: "#e74242", text: "非常弱" },
    { color: "#EFBD47", text: "弱" },
    { color: "#ffa500", text: "一般" },
    { color: "#1bbf1b", text: "强" },
    { color: "#008000", text: "非常强" }
  ];
  // 当前密码强度（0-4）
  const curScore = ref();

  function onChangeUserStatus({ row }) {
    frozenUser(row.userId, row.state).then(res => {
      if (res.code === 200) {
        message("已成功修改用户状态", {
          type: "success"
        });
      }
    });
  }

  function handleUpdate(row) {
    console.log(row);
  }

  function handleDelete(row) {
    deleteUser(row.userId).then(res => {
      if (res.code === 200) {
        message(`您成功删除了用户: ${row.name}`, { type: "success" });
        onSearch();
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  /** 当CheckBox选择项发生变化时会触发该事件 */
  function handleSelectionChange(val) {
    selectedNum.value = val.length;
    // 重置表格高度
    tableRef.value.setAdaptive();
  }

  /** 取消选择 */
  function onSelectionCancel() {
    selectedNum.value = 0;
    // 用于多选表格，清空用户的选择
    tableRef.value.getTableRef().clearSelection();
  }

  /** 批量删除 */
  function onbatchDel() {
    // 返回当前选中的行
    const curSelected = tableRef.value.getTableRef().getSelectionRows();
    const userIDs = curSelected.map(row => row.userId);
    // 接下来根据实际业务，通过选中行的某项数据，比如下面的id，调用接口进行批量删除
    deleteRoleList(userIDs).then(res => {
      if (res.code === 200) {
        message(
          `已删除用户编号为 ${getKeyList(curSelected, "userId")} 的数据`,
          {
            type: "success"
          }
        );
        tableRef.value.getTableRef().clearSelection();
        onSearch();
      }
    });
  }

  async function onSearch() {
    loading.value = true;
    const nodeAreaField = handleFormField(
      selectedTreeNode.value,
      treeData.value,
      "label"
    );
    getUserList({
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...nodeAreaField,
      ...form
    }).then(res => {
      if (res.code === 200) {
        dataList.value = res.data.records;
        pagination.total = res.data.total;
        pagination.pageSize = res.data.size;
        pagination.currentPage = res.data.current;
      }
    });
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = filterRef => {
    if (!filterRef) return;
    filterRef.resetFields();
    Object.keys(form).forEach(key => {
      form[key] = undefined;
    });
    onSearch();
  };

  function onTreeSelect(node) {
    selectedTreeNode.value = node;
    onSearch();
  }

  // function formatHigherDeptOptions(treeList) {
  //   // 根据返回数据的status字段值判断追加是否禁用disabled字段，返回处理后的树结构，用于上级部门级联选择器的展示（实际开发中也是如此，不可能前端需要的每个字段后端都会返回，这时需要前端自行根据后端返回的某些字段做逻辑处理）
  //   if (!treeList || !treeList.length) return;
  //   const newTreeList = [];
  //   for (let i = 0; i < treeList.length; i++) {
  //     treeList[i].disabled = treeList[i].status === 0 ? true : false;
  //     formatHigherDeptOptions(treeList[i].children);
  //     newTreeList.push(treeList[i]);
  //   }
  //   return newTreeList;
  // }

  function openDialog(title = "新增用户", row?: FormItemProps) {
    addDialog({
      title: `${title}`,
      props: {
        formInline: {
          userId: row?.userId ?? "",
          name: row?.name ?? "",
          phone: row?.phone ?? "",
          sex: row?.sex ?? "男",
          password: row?.password ?? "",
          deptName: row?.deptName ?? "",
          role: row?.role ?? [],
          city: row?.city ?? "",
          county: row?.county ?? "",
          township: row?.township ?? "",
          hamlet: row?.hamlet ?? "",
          site: row?.site ?? ""
        },
        roleOptions: roleOptions.value
      },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      sureBtnLoading: true,
      contentRenderer: () =>
        h(editForm, {
          ref: formRef,
          formInline: null,
          roleOptions: roleOptions.value
        }),
      beforeSure: (done, { options, closeLoading }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;
        function chores() {
          message(`您${title}了用户名称为${curData.name}的这条数据`, {
            type: "success"
          });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(valid => {
          if (valid) {
            // 新增
            if (title === "新增用户") {
              // 实际开发先调用新增接口，再进行下面操作
              const nodeAreaField = handleFormField(
                selectedTreeNode.value,
                treeData.value,
                "label"
              );
              delete curData.userId;
              addUser({ ...curData, ...nodeAreaField })
                .then(res => {
                  if (res.code === 200) {
                    chores();
                  }
                })
                .finally(() => {
                  closeLoading();
                });
            }
            // 编辑
            else {
              // 实际开发先调用修改接口，再进行下面操作
              const editPayload = { ...curData };
              delete editPayload.role;
              delete editPayload.password;
              editUser(editPayload)
                .then(res => {
                  if (res.code === 200) {
                    chores();
                  }
                })
                .finally(() => {
                  closeLoading();
                });
            }
          } else {
            closeLoading();
          }
        });
      },
      close({ options }) {
        options.sureBtnLoading = false;
      }
    });
  }

  const cropRef = ref();
  /** 上传头像 */
  function handleUpload(row) {
    addDialog({
      title: "裁剪、上传头像",
      width: "40%",
      closeOnClickModal: false,
      fullscreen: deviceDetection(),
      contentRenderer: () =>
        h(ReCropperPreview, {
          ref: cropRef,
          imgSrc: row.avatar || userAvatar,
          onCropper: info => (avatarInfo.value = info)
        }),
      beforeSure: done => {
        // 根据实际业务使用avatarInfo.value和row里的某些字段去调用上传头像接口即可
        done(); // 关闭弹框
        onSearch(); // 刷新表格数据
      },
      closeCallBack: () => cropRef.value.hidePopover()
    });
  }

  watch(
    pwdForm,
    ({ newPwd }) =>
      (curScore.value = isAllEmpty(newPwd) ? -1 : zxcvbn(newPwd).score)
  );

  /** 重置密码 */
  function handleReset(row) {
    addDialog({
      title: `重置用户: [${row.name}] 的密码`,
      width: "30%",
      draggable: true,
      closeOnClickModal: false,
      sureBtnLoading: true,
      fullscreen: deviceDetection(),
      contentRenderer: () => (
        <Fragment>
          <ElForm ref={ruleFormRef} model={pwdForm}>
            <ElFormItem
              prop="newPwd"
              rules={[
                {
                  required: true,
                  message: "请输入新密码",
                  trigger: "blur"
                }
              ]}
            >
              <ElInput
                clearable
                show-password
                type="password"
                v-model={pwdForm.newPwd}
                placeholder="请输入新密码"
              />
            </ElFormItem>
          </ElForm>
          <div class="mt-4 flex">
            {pwdProgress.map(({ color, text }, idx) => (
              <div
                class="w-[19vw]"
                style={{ marginLeft: idx !== 0 ? "4px" : 0 }}
              >
                <ElProgress
                  striped
                  striped-flow
                  duration={curScore.value === idx ? 6 : 0}
                  percentage={curScore.value >= idx ? 100 : 0}
                  color={color}
                  stroke-width={10}
                  show-text={false}
                />
                <p
                  class="text-center"
                  style={{ color: curScore.value === idx ? color : "" }}
                >
                  {text}
                </p>
              </div>
            ))}
          </div>
        </Fragment>
      ),
      closeCallBack: () => (pwdForm.newPwd = ""),
      beforeSure: (done, { closeLoading }) => {
        ruleFormRef.value.validate(valid => {
          if (valid) {
            // 表单规则校验通过
            resetPassword(row.userId, pwdForm.newPwd)
              .then(res => {
                if (res.code === 200) {
                  message(`已成功重置 ${row.name} 用户的密码`, {
                    type: "success"
                  });
                  done(); // 关闭弹框
                  onSearch(); // 刷新表格数据
                }
              })
              .finally(() => {
                closeLoading();
              });
          }
        });
      }
    });
  }

  /** 分配角色 */
  async function handleRole(row) {
    // 选中的角色列表
    addDialog({
      title: `分配 ${row.name} 用户的角色`,
      props: {
        formInline: {
          name: row?.name ?? "",
          roles: row.roles.map(item => item.roleId)
        },
        roleOptions: roleOptions.value
      },
      width: "400px",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(roleForm),
      beforeSure: (done, { options }) => {
        const curData = options.props.formInline as RoleFormItemProps;
        assignRole(row.userId, curData.roles).then(res => {
          if (res.code === 200) {
            message(`已成功为 ${row.name} 分配角色`, {
              type: "success"
            });
            onSearch();
          }
        });
        // 根据实际业务使用curData.ids和row里的某些字段去调用修改角色接口即可
        done(); // 关闭弹框
      }
    });
  }

  /** 删除节点 */
  const onTreeContextMenu = (item, nodeData) => {
    if (item.cmd === "DELETE") {
      ElMessageBox({
        type: "warning",
        title: "删除节点",
        message: `删除后【${nodeData.label}】下添加的用户将全部被删除，确定要删除节点吗？`,
        confirmButtonText: "确定删除",
        confirmButtonLoadingIcon: "el-icon-loading",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            const loading = ElLoading.service({
              text: "正在删除...",
              background: "rgba(0, 0, 0, 0.1)" // 自定义背景
            });
            deleteTreeNode(nodeData.id)
              .then(res => {
                if (res.code === 200) {
                  message(`节点【${nodeData.label}】删除成功`, {
                    type: "success"
                  });
                  loading.close();
                  selectedTreeNode.value = null;
                  fetchAreaTree();
                  done();
                }
              })
              .finally(() => {
                loading.close();
              });
          } else {
            done();
          }
        }
      });
    } else if (item.cmd === "ADD_NODE") {
      // 新增节点
      addDialog({
        title: "新增节点",
        contentRenderer: () =>
          h(NodeForm, {
            ref: formRef,
            formInline: {
              label: ""
            },
            parentLabel: nodeData?.label || "",
            labelId: nodeData?.id || "",
            name: "父节点名称",
            designation: "新增节点名称"
          }),
        beforeSure: done => {
          const formInstance = formRef.value.formRef;
          if (formInstance) {
            formInstance.validate(valid => {
              if (valid) {
                const formData = {
                  ...formRef.value.formInline,
                  parentId: nodeData?.id || 0
                };
                addTreeNode(formData).then(res => {
                  if (res.code === 200) {
                    message(`节点【${formData.label}】添加成功`, {
                      type: "success"
                    });
                    fetchAreaTree();
                    // 动态更新显示内容
                    if (nodeData?.label) nodeData.label = formData.label; // 更新当前节点的标签
                    done();
                  }
                });
              }
            });
          } else {
            console.error("表单实例未找到，无法验证");
          }
        }
      });
    } else if (item.cmd === "EDIT_NODE") {
      // 修改节点
      addDialog({
        title: "修改节点",
        contentRenderer: () =>
          h(NodeForm, {
            ref: formRef,
            formInline: {
              label: nodeData.label // 现有节点的标签
            },
            parentLabel: nodeData.label,
            labelId: nodeData.id,
            name: "当前节点名称",
            designation: "修改后节点名称"
          }),
        beforeSure: done => {
          const formInstance = formRef.value.formRef;
          if (formInstance) {
            formInstance.validate(valid => {
              if (valid) {
                const formData = {
                  ...formRef.value.formInline,
                  id: nodeData.id
                };
                updateTreeNode(formData).then(res => {
                  if (res.code === 200) {
                    message(`节点【${formData.label}】修改成功`, {
                      type: "success"
                    });
                    fetchAreaTree();
                    // 动态更新显示内容
                    nodeData.label = formData.label; // 更新当前节点的标签
                    done();
                  }
                });
              }
            });
          } else {
            console.error("表单实例未找到，无法验证");
          }
        }
      });
    }
  };

  /** 获取地区树 */
  const fetchAreaTree = () => {
    getAreaTree()
      .then(res => {
        if (res.code === 200) {
          treeData.value = res.data;
        }
      })
      .finally(() => {
        treeLoading.value = false;
      });
  };

  onMounted(async () => {
    treeLoading.value = true;
    onSearch();
    // 校验是否存在操作列表的按钮
    const isPermissionHandle = notHasAllPermission([
      "/system/user/user/updateUsername",
      "/system/user/user/deleteUser",
      "/system/user/user/resetPassword",
      "/system/system-role/role/addRole"
    ]);
    if (isPermissionHandle) {
      columns.value = columns.value.filter(item => item.slot !== "operation");
    }
    // 权限列表
    getRoleOptions().then(res => {
      if (res.code === 200) {
        roleOptions.value = res.data;
      }
    });

    // 归属部门
    const data = [];
    higherDeptOptions.value = handleTree(data);

    fetchAreaTree();
    // 角色列表
    roleOptions.value = [];
  });

  return {
    form,
    loading,
    columns,
    dataList,
    treeData,
    treeLoading,
    selectedTreeNode,
    selectedNum,
    pagination,
    buttonClass,
    deviceDetection,
    onSearch,
    resetForm,
    onbatchDel,
    openDialog,
    onTreeSelect,
    onTreeContextMenu,
    handleUpdate,
    handleDelete,
    handleUpload,
    handleReset,
    handleRole,
    handleSizeChange,
    onSelectionCancel,
    handleCurrentChange,
    handleSelectionChange
  };
}
