import Delete from "@iconify-icons/ep/delete";
import Add from "@iconify-icons/ep/add-location";
import Edit from "@iconify-icons/ep/edit";

export enum TreeNodeType {
  /**
   * 城市
   */
  CITY = 0,
  /**
   * 县级
   */
  COUNTY = 1,
  /**
   * 乡镇
   */
  TOWN_SHIP = 2,
  /**
   * 村
   */
  HAMLET = 3,
  /**
   * 点位
   */
  SITE = 4
}

export const TreeNodeMenu = [
  {
    name: "新增节点",
    cmd: "ADD_NODE",
    icon: Add
  },
  {
    name: "编辑节点",
    cmd: "EDIT_NODE",
    icon: Edit
  },
  {
    name: "删除节点",
    cmd: "DELETE",
    icon: Delete
  }
];
