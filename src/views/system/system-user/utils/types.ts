import type { Role } from "../../system-role/utils/types";
import type { TreeNodeType } from "./enum";

interface FormItemProps {
  userId?: string;
  /** 用于判断是`新增`还是`修改` */
  /**
   * 姓名
   */
  name: string;
  /**
   * 电话
   */
  phone: string;
  /**
   * 性别
   */
  sex: number;
  /**
   * 密码
   */
  password: string;
  /**
   * 职位
   */
  deptName: string;
  /**
   * 权限
   */
  role: string[];
  /**
   * 市
   */
  city: string;
  /**
   * 县
   */
  county: string;
  /**
   * 镇
   */
  township: string;
  /**
   * 村
   */
  hamlet: string;
  /**
   * 点位
   */
  site: string;
}
interface FormProps {
  formInline: FormItemProps;
  roleOptions: Role[];
}

interface RoleFormItemProps {
  name: string;
  roles: number[];
}
interface RoleFormProps {
  formInline: RoleFormItemProps;
  roleOptions: Role[];
}

interface User extends Area {
  /**
   * 用户id
   */
  userId: string;
  /**
   * 角色信息
   */
  roles: Role[];
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 职位
   */
  deptName: string;
  /**
   * 姓名
   */
  name: string;
  /**
   * 密码
   */
  password: string;
  /**
   * 手机号
   */
  phone: string;
  /**
   * 性别
   */
  sex: string;
  /**
   * 状态
   */
  state: number;
}

interface TreeNode {
  /**
   * 主键id
   */
  id: number;
  /**
   * 节点名称
   */
  label: string;
  /**
   * 节点类型
   */
  level: TreeNodeType;
  /**
   * 父节点id
   */
  parentId: number;
  /**
   * 子集节点
   */
  childList?: TreeNode[];
  /**
   * 更新时间
   */
  updateTime: string | null;
  /**
   * 创建时间
   */
  createTime: string | null;
}

export interface Area {
  /** 市 */
  city?: string | null;
  /** 县 */
  county?: string | null;
  /** 乡镇 */
  township?: string | null;
  /** 村 */
  hamlet?: string | null;
  /** 点位 */
  site?: string | null;
}

export type {
  FormItemProps,
  FormProps,
  RoleFormItemProps,
  RoleFormProps,
  TreeNode,
  User
};
