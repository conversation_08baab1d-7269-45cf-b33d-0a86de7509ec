<script setup lang="ts">
import ReCol from "@/components/ReCol";
import { ref } from "vue";
import { formRules } from "../utils/rule";
import { FormProps } from "../utils/types";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    userId: "",
    name: "",
    phone: "",
    sex: 1,
    password: "",
    deptName: "",
    role: [],
    city: "",
    county: "",
    township: "",
    hamlet: "",
    site: ""
  }),
  roleOptions: () => []
});

const sexOptions = [
  {
    value: "男",
    label: "男"
  },
  {
    value: "女",
    label: "女"
  }
];

const jobTypeOptions = [
  {
    value: "全职",
    label: "全职"
  },
  {
    value: "兼职",
    label: "兼职"
  }
];

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <!-- 新增 -->
  <el-form
    v-if="!newFormInline.userId"
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="82px"
  >
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="用户名" prop="name">
          <el-input
            v-model="newFormInline.name"
            clearable
            placeholder="请输入用户名"
          />
        </el-form-item>
      </re-col>
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="newFormInline.password"
            clearable
            placeholder="请输入登录密码"
          />
        </el-form-item>
      </re-col>
    </el-row>

    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="性别" prop="sex">
          <el-select
            v-model="newFormInline.sex"
            placeholder="请选择用户性别"
            class="w-full"
            clearable
          >
            <el-option
              v-for="(item, index) in sexOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </re-col>
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="newFormInline.phone"
            clearable
            placeholder="请输入手机号"
          />
        </el-form-item>
      </re-col>
    </el-row>

    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属职位" prop="deptName">
          <el-select
            v-model="newFormInline.deptName"
            placeholder="请选择职位类型"
            class="w-full"
            clearable
          >
            <el-option
              v-for="(item, index) in jobTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </re-col>

      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="角色" prop="ids">
          <el-select
            v-model="newFormInline.role"
            placeholder="请选择"
            class="w-full"
            clearable
            multiple
          >
            <el-option
              v-for="(item, index) in roleOptions"
              :key="index"
              :value="item.roleId"
              :label="item.roleName"
            >
              {{ item.roleName }}
            </el-option>
          </el-select>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
  <!-- 编辑 -->
  <el-form
    v-else
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="82px"
  >
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="用户名" prop="name">
          <el-input
            v-model="newFormInline.name"
            clearable
            placeholder="请输入用户名"
          />
        </el-form-item>
      </re-col>
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="性别" prop="sex">
          <el-select
            v-model="newFormInline.sex"
            placeholder="请选择用户性别"
            class="w-full"
            clearable
          >
            <el-option
              v-for="(item, index) in sexOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </re-col>
    </el-row>
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="newFormInline.phone"
            clearable
            placeholder="请输入手机号"
          />
        </el-form-item>
      </re-col>
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="所属职位" prop="deptName">
          <el-select
            v-model="newFormInline.deptName"
            placeholder="请选择职位类型"
            class="w-full"
            clearable
          >
            <el-option
              v-for="(item, index) in jobTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </re-col>
    </el-row>
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="市" prop="city">
          <el-input
            v-model="newFormInline.city"
            clearable
            placeholder="请输入所属市"
          />
        </el-form-item>
      </re-col>
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="县" prop="county">
          <el-input
            v-model="newFormInline.county"
            clearable
            placeholder="请输入所属县"
          />
        </el-form-item>
      </re-col>
    </el-row>
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="镇" prop="township">
          <el-input
            v-model="newFormInline.township"
            clearable
            placeholder="请输入所属镇"
          />
        </el-form-item>
      </re-col>
    </el-row>
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="村" prop="hamlet">
          <el-input
            v-model="newFormInline.hamlet"
            clearable
            placeholder="请输入所属村"
          />
        </el-form-item>
      </re-col>
    </el-row>
    <el-row :gutter="30">
      <re-col :value="24">
        <el-form-item label="点" prop="site">
          <el-input
            v-model="newFormInline.site"
            clearable
            placeholder="请输入所属点"
          />
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>
