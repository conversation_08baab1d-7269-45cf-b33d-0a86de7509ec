<template>
  <el-form ref="formRef" :model="formInline" :rules="rules">
    <el-form-item v-if="parentLabel" :label="`${name}`" label-width="120px">
      <strong>{{ parentLabel }}</strong>
    </el-form-item>
    <el-form-item :label="`${designation}`" prop="label" label-width="120px">
      <el-input v-model="formInline.label" placeholder="请输入节点名称" />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, defineExpose, onMounted, toRefs } from "vue";

const props = defineProps({
  parentLabel: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  designation: {
    type: String,
    required: true
  }
});

const formInline = ref({
  label: ""
});

const rules = {
  label: [{ required: true, message: "请输入节点名称", trigger: "blur" }]
};

const formRef = ref(null);

defineExpose({ formRef, formInline });

onMounted(() => {
  if (!formRef.value) {
    console.error("表单引用未定义或组件未正确渲染");
  }
});

function validateForm() {
  if (formRef.value) {
    formRef.value
      .validate(valid => {
        if (valid) {
          // 进行后续操作
        } else {
          console.error("表单验证失败");
        }
      })
      .catch(error => {
        console.error("验证过程中发生错误:", error);
      });
  } else {
    console.error("表单引用未定义或组件未正确渲染");
  }
}
</script>
