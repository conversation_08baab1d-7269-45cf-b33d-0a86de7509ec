export interface FilterParams {
  operName?: string; // 操作人员
  businessType?: string; // 业务类型
  title?: string; // 模块标题
  dateRange?: [string, string]; // 时间范围
}

export interface LogItem {
  id: number;
  title: string; // 操作标题
  businessType: string; // 业务类型
  method: string; // 方法名称
  requestMethod: string; // 请求方法
  userId: number; // 用户ID
  operName: string; // 操作人名称
  operUrl: string; // 请求URL
  operIp: string; // 操作IP
  status: number; // 操作状态 0-成功 1-失败
  errorMsg: string; // 错误消息
  operTime: string; // 操作时间
  executeTime: number; // 执行时长(毫秒)
}
