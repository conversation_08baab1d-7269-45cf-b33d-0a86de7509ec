import { deleteLog, deleteLogs, getLogList } from "@/api/log"; // 需要创建对应的API
import { notHasAllPermission } from "@/directives/auth";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import dayjs from "dayjs";
import { h, onMounted, reactive, ref, type Ref } from "vue";
import type { FilterParams, LogItem } from "./types";

interface ExtendedPaginationProps extends PaginationProps {
  align?: "left" | "center" | "right";
  small?: boolean;
}

export function useLog(_tableRef: Ref) {
  const filterParams = reactive<FilterParams>({});
  const dataList = ref<LogItem[]>([]);
  const loading = ref(false);
  const selectedIds = ref<number[]>([]);

  const pagination = reactive<ExtendedPaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    align: "center",
    small: false
  });

  const getColumns = (columnList = []) => {
    // 校验是否存在操作列表的按钮
    const notDeletePermissionHandle = notHasAllPermission([
      "/system/system-log/log/deleteLog"
    ]);
    const notBatchDeletePermissionHandle = notHasAllPermission([
      "/system/system-log/log/deleteLogs"
    ]);

    let applyColumns = columnList;
    if (notDeletePermissionHandle || notBatchDeletePermissionHandle) {
      applyColumns = columnList.filter(item => {
        if (notDeletePermissionHandle && item.slot == "operation") return false;
        if (notBatchDeletePermissionHandle && item.type == "selection")
          return false;
        return true;
      });
    }
    return applyColumns;
  };

  const columns = ref<TableColumnList>([
    {
      type: "selection",
      width: 55
    },
    {
      label: "操作标题",
      prop: "title",
      width: 120
    },
    {
      label: "操作人",
      prop: "operName",
      width: 100
    },
    {
      label: "业务类型",
      prop: "businessType",
      width: 100,
      formatter: ({ businessType }) => {
        const typeMap = {
          SELECT: "查询",
          INSERT: "新增",
          UPDATE: "修改",
          DELETE: "删除"
        };
        return typeMap[businessType] || businessType;
      }
    },
    {
      label: "请求方法",
      prop: "requestMethod",
      width: 80
    },
    {
      label: "请求地址",
      prop: "operUrl",
      minWidth: 180
    },
    {
      label: "IP地址",
      prop: "operIp",
      width: 130
    },
    {
      label: "执行时长",
      prop: "executeTime",
      width: 100,
      formatter: ({ executeTime }) => `${executeTime}ms`
    },
    {
      label: "状态",
      prop: "status",
      width: 80,
      formatter: ({ status }) => {
        return h(
          "span",
          {
            style: {
              padding: "2px 6px",
              borderRadius: "2px",
              fontSize: "12px",
              border: "1px solid",
              color: status === 0 ? "#00FFFF" : "#FF0000",
              borderColor: status === 0 ? "#00FFFF" : "#FF0000",
              backgroundColor: "transparent"
            }
          },
          status === 0 ? "成功" : "失败"
        );
      }
    },
    {
      label: "操作时间",
      width: 160,
      prop: "operTime",
      formatter: ({ operTime }) => dayjs(operTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      width: 100,
      fixed: "right",
      slot: "operation"
    }
  ]);

  onMounted(() => {
    columns.value = getColumns(columns.value);
    onSearch();
  });

  const onSearch = () => {
    loading.value = true;
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      operName: filterParams.operName,
      businessType: filterParams.businessType,
      title: filterParams.title,
      startTime: filterParams.dateRange?.[0]
        ? `${filterParams.dateRange[0]} 00:00:00`
        : undefined,
      endTime: filterParams.dateRange?.[1]
        ? `${filterParams.dateRange[1]} 23:59:59`
        : undefined
    };

    getLogList(params)
      .then(res => {
        if (res.code === 200) {
          if (res.data && Array.isArray(res.data.records)) {
            dataList.value = res.data.records;
            pagination.total = Number(res.data.total) || 0;
            pagination.currentPage = Number(res.data.curPage) || 1;
            pagination.pageSize = Number(res.data.pageSize) || 10;
          } else if (Array.isArray(res.data)) {
            dataList.value = res.data;
          } else {
            dataList.value = [];
          }
        } else {
          dataList.value = [];
        }
      })
      .catch(() => {
        dataList.value = [];
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    onSearch();
  };

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1; // 切换每页数量时重置为第一页
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSelectionChange(selection) {
    selectedIds.value = selection.map(item => item.id);
  }

  async function handleDelete(row) {
    try {
      await deleteLog(row.id);
      message(`删除成功`, { type: "success" });
      onSearch(); // 刷新数据
    } catch (error) {
      message(`删除失败: ${error.message}`, { type: "error" });
    }
  }

  async function handleBatchDelete() {
    if (selectedIds.value.length === 0) {
      message(`请选择要删除的数据`, { type: "warning" });
      return;
    }
    try {
      await deleteLogs(selectedIds.value);
      message(`批量删除成功`, { type: "success" });
      selectedIds.value = []; // 清空选择
      onSearch(); // 刷新数据
    } catch (error) {
      message(`批量删除失败: ${error.message}`, { type: "error" });
    }
  }

  return {
    loading,
    filterParams,
    dataList,
    pagination,
    columns,
    onSearch,
    resetFilterForm,
    handleSizeChange,
    handleCurrentChange,
    selectedIds,
    handleSelectionChange,
    handleDelete,
    handleBatchDelete
  };
}
