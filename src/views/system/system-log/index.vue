<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import Delete from "@iconify-icons/ep/delete";
import Refresh from "@iconify-icons/ep/refresh";
import { ref } from "vue";
import { useLog } from "./utils/hooks";

defineOptions({
  name: "SystemLog"
});

const filterRef = ref();
const tableRef = ref();
const {
  filterParams,
  loading,
  dataList,
  columns,
  pagination,
  selectedIds,
  onSearch,
  resetFilterForm,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  handleDelete,
  handleBatchDelete
} = useLog(tableRef);
</script>

<template>
  <div>
    <el-form
      ref="filterRef"
      :inline="true"
      :model="filterParams"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="操作人员：" prop="operName">
        <el-input
          v-model="filterParams.operName"
          placeholder="请输入操作人员"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="业务类型：" prop="businessType">
        <el-select
          v-model="filterParams.businessType"
          placeholder="请选择业务类型"
          clearable
          class="!w-[180px]"
        >
          <el-option label="查询" value="SELECT" />
          <el-option label="新增" value="INSERT" />
          <el-option label="修改" value="UPDATE" />
          <el-option label="删除" value="DELETE" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作标题：" prop="title">
        <el-input
          v-model="filterParams.title"
          placeholder="请输入操作标题"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="filterParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          class="!w-[380px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon(Refresh)"
          @click="resetFilterForm(filterRef)"
        >
          重置
        </el-button>
        <el-button
          v-permission="'/system/system-log/log/deleteLogs'"
          type="danger"
          :icon="useRenderIcon(Delete)"
          :disabled="!selectedIds.length"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar title=" " :columns="columns" @refresh="onSearch">
      <template v-slot="{ size }">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="columns"
          :pagination="{
            total: pagination.total,
            pageSize: pagination.pageSize,
            currentPage: pagination.currentPage,
            background: true,
            align: 'center'
          }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-popconfirm
              title="是否确认删除此日志?"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  v-permission="'/system/system-log/log/deleteLog'"
                  link
                  type="danger"
                  :size="size"
                  :icon="useRenderIcon(Delete)"
                  >删除</el-button
                >
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>
