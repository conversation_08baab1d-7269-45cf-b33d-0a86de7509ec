<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  fetchSystemConfig,
  saveSystemConfig,
  type SystemConfig,
  convertToSystemConfig
} from "@/api/systemConfig";

defineOptions({
  name: "SystemSetting"
});

const config = ref<SystemConfig>({
  id: 1,
  overloadThreshold: 5,
  logCleanupDays: 30,
  absentThresholdMinutes: 15,
  deviceOfflineMinutes: 5,
  enableAutoCall: false,
  validVideoSeconds: 30
});

// 保存配置
const handleSave = async () => {
  try {
    await saveSystemConfig(config.value);
    ElMessage.success("保存成功");
  } catch (error) {
    ElMessage.error("保存失败");
  }
};

// 获取配置
const getConfig = async () => {
  try {
    const res = await fetchSystemConfig();
    if (res.data) {
      config.value = convertToSystemConfig(res.data);
    } else {
      ElMessage.warning("获取配置数据为空");
    }
  } catch (error) {
    ElMessage.error("获取配置失败");
  }
};

onMounted(() => {
  getConfig();
});
</script>

<template>
  <div class="system-setting">
    <el-card class="setting-card">
      <template #header>
        <div class="card-header">
          <span>系统参数设置</span>
        </div>
      </template>

      <el-form :model="config" label-width="220px">
        <el-form-item label="超载人数阈值">
          <el-input-number
            v-model="config.overloadThreshold"
            :min="1"
            :max="100"
          />
          <span class="form-tip">人，超过该人数将触发下派</span>
        </el-form-item>

        <el-form-item label="日志清理时间">
          <el-input-number
            v-model="config.logCleanupDays"
            :min="1"
            :max="365"
          />
          <span class="form-tip">天，系统将自动清理超过该天数的日志</span>
        </el-form-item>

        <el-form-item label="劝导员脱岗判定时间">
          <el-input-number
            v-model="config.absentThresholdMinutes"
            :min="1"
            :max="120"
          />
          <span class="form-tip">分钟，超过该时间将判定为脱岗</span>
        </el-form-item>

        <el-form-item label="设备离线判定时间">
          <el-input-number
            v-model="config.deviceOfflineMinutes"
            :min="1"
            :max="60"
          />
          <span class="form-tip">分钟，超过该时间未上报状态将判定为离线</span>
        </el-form-item>

        <el-form-item label="自动打电话功能">
          <el-switch v-model="config.enableAutoCall" />
          <span class="form-tip">启用后系统将自动拨打电话</span>
        </el-form-item>

        <el-form-item label="有效劝导视频时长">
          <el-input-number
            v-model="config.validVideoSeconds"
            :min="1"
            :max="3600"
          />
          <span class="form-tip">秒，超过该时长的视频将被认定为有效劝导</span>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSave">保存配置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.system-setting {
  padding: 20px;
}

.setting-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.form-tip {
  margin-left: 10px;
  font-size: 14px;
  color: #909399;
}
</style>
