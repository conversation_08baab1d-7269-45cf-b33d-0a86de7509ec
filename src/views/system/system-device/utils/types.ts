/**
 * 设备信息接口定义
 * 包含设备基本信息和配置信息
 */
export interface DeviceItem {
  // 基本信息
  id?: number; // 设备ID，可选
  deviceName: string; // 设备名称
  equipmentNumber: string; // 设备编号
  ip: string; // IP地址
  deviceType: string; // 设备类型（电脑/摄像机）
  status: number; // 设备状态
  updateTime: string; // 更新时间
  createTime: string; // 创建时间
  maintainerPhone: string; // 维护人员电话

  // 位置信息
  city: string; // 市
  county: string; // 县（区）
  township: string; // 乡镇
  hamlet: string; // 村
  site: string; // 具体地点
  longitude: string; // 经度
  latitude: string; // 纬度

  // 视频相关配置
  streamKey: string; // 视频流密钥
  devMode: boolean; // 开发模式
  addressText: string; // 设备地址文本
  cameraUrl1: string; // 来向摄像头RTSP地址
  cameraUrl2: string; // 去向摄像头RTSP地址
  clipRect1: number[]; // 来向区域裁剪框 [x, y, width, height]
  clipRect2: number[]; // 去向区域裁剪框 [x, y, width, height]
  line1: number[]; // 来向碰撞线段 [x1, y1, x2, y2]
  line2: number[]; // 去向碰撞线段 [x1, y1, x2, y2]
  videoFps: number; // 视频帧率

  // 识别阈值配置
  nohelmetThreshold: number; // 头盔识别阈值
  weatherShieldThreshold: number; // 雨棚识别阈值
  faceRecogDistanceThreshold: number; // 人脸识别距离阈值

  // 时间配置
  persuadTimeDuration: number; // 劝导有效期(秒)
  advicerFaceRecogDuration: number; // 人脸识别间隔(秒)
  loadParamInterval: number; // 参数加载间隔(秒)
  loadFaceDatabaseInterval: number; // 人脸库加载间隔(秒)
  bikeTrackidLife: number; // 车辆跟踪时长(秒)
  startEndTime: number; // 违法视频前后时长(秒)

  // 播报配置
  announceTimes: Record<string, number> | string; // 允许对象或字符串类型
  maxAnnounceTimes: number; // 最大连续播报次数
  announceTimeout: number; // 播报间隔(秒)
  announceVolume: string; // 播报音量配置 JSON字符串 {"小时": 音量}
  unmuteStart: number; // 播报开始时间
  unmuteEnd: number; // 播报结束时间

  // 其他配置
  saveViolateVideo: boolean; // 是否保存违法视频
  bikePlatesQuantity: number; // 车牌图像数量
  retryIntervals: number[]; // 重试间隔时间数组
  onlineStatusInterval: number; // 在线状态检查间隔
  backwardPriority: boolean; // 违法车辆检测是否以背面碰线优先

  // 模型文件配置
  modelFile: string; // 模型配置 JSON字符串 {"helmetUrl": "", "helmetVersion": 0, ...}
  urgent?: boolean; // 添加 urgent 字段，设为可选
}
