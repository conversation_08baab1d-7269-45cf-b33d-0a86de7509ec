import { deleteDevice, getDeviceList, updateDevice } from "@/api/device";
import {
  addTreeNode,
  deleteTreeNode,
  getAreaTree,
  updateTreeNode
} from "@/api/system";
import { addDialog } from "@/components/ReDialog";
import { notHasAllPermission } from "@/directives/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { handleFormField } from "@/utils/tree";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import { h, onMounted, reactive, ref } from "vue";
import NodeForm from "../NodeForm.vue";
import type { DeviceItem } from "./types";

export function useDevice() {
  const filterParams = reactive({
    deviceName: "",
    equipmentNumber: "",
    ip: "",
    maintainerPhone: ""
  });
  const formRef = ref();
  const dataList = ref([]);
  const loading = ref(false);
  const selectedIds = ref<number[]>([]);
  const treeData = ref([]);
  const treeLoading = ref(false);
  const selectedTreeNode = ref(null);
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    align: "center"
  });

  const columns = ref<TableColumnList>([
    {
      label: "点位",
      prop: "site",
      width: 150
    },
    {
      label: "设备名称",
      prop: "deviceName",
      width: 150
    },
    {
      label: "设备编号",
      prop: "equipmentNumber",
      width: "*"
    },
    {
      label: "IP地址",
      prop: "ip",
      width: 120
    },
    {
      label: "流密钥",
      prop: "streamKey",
      width: 120,
      formatter: ({ deviceType, streamKey }) => {
        if (deviceType !== "摄像机") return "-";
        // 如果包含http://，截取后面的部分
        if (streamKey && streamKey.includes("http://")) {
          const fullUrl = streamKey.split("http://")[1];
          // 从URL中提取最后一个/后面的部分
          const lastPart = fullUrl.split("/").pop();
          // 如果有扩展名（包含.），则去掉扩展名
          const key = lastPart?.includes(".")
            ? lastPart.split(".")[0]
            : lastPart;
          return key || streamKey;
        }
        return streamKey;
      }
    },
    {
      label: "经度",
      prop: "longitude",
      width: 120
    },
    {
      label: "纬度",
      prop: "latitude",
      width: 120
    },
    {
      label: "维护人员电话",
      prop: "maintainerPhone",
      width: 120
    },
    {
      label: "状态",
      prop: "state",
      width: 80,
      formatter: ({ state }) => {
        return h(
          "span",
          {
            style: {
              padding: "2px 6px",
              borderRadius: "2px",
              fontSize: "12px",
              border: "1px solid",
              color: state === 0 ? "#00FFFF" : "#FF0000",
              borderColor: state === 0 ? "#00FFFF" : "#FF0000",
              backgroundColor: "transparent"
            }
          },
          state === 0 ? "在线" : "离线"
        );
      }
    },
    {
      label: "修改时间",
      prop: "updateTime",
      width: 160
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: 160
    },
    {
      label: "操作",
      width: 260,
      fixed: "right",
      slot: "operation"
    }
  ]);

  const userStore = useUserStoreHook();

  onMounted(() => {
    // 校验是否存在操作列表的按钮
    const isPermissionHandle = notHasAllPermission([
      "/device/list/device/addDevice",
      "/device/list/device/deleteDevice",
      "/device/list/device/updateDevice"
    ]);
    if (isPermissionHandle) {
      columns.value = columns.value.filter(item => item.slot !== "operation");
    }
    onSearch();
    fetchAreaTree();
  });

  const onSearch = () => {
    loading.value = true;
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      deviceName: filterParams.deviceName,
      ip: filterParams.ip,
      equipmentNumber: filterParams.equipmentNumber,
      ...userStore.getUserLocationPath,
      ...handleFormField(selectedTreeNode.value, treeData.value, "label")
    };
    getDeviceList(params)
      .then(res => {
        if (res.code === 200) {
          dataList.value = res.data.records || [];
          pagination.total = res.data.total || 0;
        } else {
          dataList.value = [];
        }
      })
      .catch(() => {
        dataList.value = [];
      })
      .finally(() => {
        loading.value = false;
      });
  };

  /** 获取地区树 */
  const fetchAreaTree = () => {
    treeLoading.value = true;
    getAreaTree()
      .then(res => {
        if (res.code === 200) {
          treeData.value = res.data;
        }
      })
      .finally(() => {
        treeLoading.value = false;
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    onSearch();
  };

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSelectionChange(selection) {
    selectedIds.value = selection.map(item => item.id);
  }

  async function handleDelete(row: DeviceItem) {
    try {
      await deleteDevice(row.id);
      ElMessage.success("删除成功");
      onSearch();
    } catch (error) {
      ElMessage.error(`删除失败: ${error.message}`);
    }
  }

  // function openDialog() {
  //   // 打开新增设备对话框的逻辑
  //   const newDevice = {
  //     name: "新设备",
  //     type: "类型",
  //     status: 0,
  //     operTime: new Date().toISOString()
  //   };
  //   addDevice(newDevice).then(response => {
  //     if (response.code === 200) {
  //       ElMessage.success("设备新增成功");
  //       onSearch(); // 刷新设备列表
  //     } else {
  //       ElMessage.error("设备新增失败");
  //     }
  //   });
  // }

  function editDevice(row: DeviceItem) {
    // 打开编辑设备对话框的逻辑
    updateDevice(row).then(res => {
      if (res.code === 200) {
        ElMessage.success("编辑设备成功");
        onSearch();
      }
    });
  }

  function onTreeSelect(node) {
    selectedTreeNode.value = node;
    onSearch();
  }

  /** 删除节点 */
  const onTreeContextMenu = (item, nodeData) => {
    if (item.cmd === "DELETE") {
      ElMessageBox({
        type: "warning",
        title: "删除节点",
        message: `删除后【${nodeData.label}】下添加的用户将全部被删除，确定要删除节点吗？`,
        confirmButtonText: "确定删除",
        confirmButtonLoadingIcon: "el-icon-loading",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            const loading = ElLoading.service({
              text: "正在删除...",
              background: "rgba(0, 0, 0, 0.1)" // 自定义背景
            });
            deleteTreeNode(nodeData.id)
              .then(res => {
                if (res.code === 200) {
                  ElMessage.success(`节点【${nodeData.label}】删除成功`);
                  loading.close();
                  selectedTreeNode.value = null;
                  fetchAreaTree();
                  done();
                }
              })
              .finally(() => {
                loading.close();
              });
          } else {
            done();
          }
        }
      });
    } else if (item.cmd === "ADD_NODE") {
      // 新增节点
      addDialog({
        title: "新增节点",
        contentRenderer: () =>
          h(NodeForm, {
            ref: formRef,
            formInline: {
              label: ""
            },
            parentLabel: nodeData?.label || "",
            labelId: nodeData?.id || "",
            name: "父节点名称",
            designation: "新增节点名称"
          }),
        beforeSure: done => {
          const formInstance = formRef.value.formRef;
          if (formInstance) {
            formInstance.validate(valid => {
              if (valid) {
                const formData = {
                  ...formRef.value.formInline,
                  parentId: nodeData?.id || 0
                };
                addTreeNode(formData).then(res => {
                  if (res.code === 200) {
                    ElMessage.success(`节点【${formData.label}】添加成功`);
                    fetchAreaTree();
                    // 动态更新显示内容
                    if (nodeData?.label) nodeData.label = formData.label; // 更新当前节点的标签
                    done();
                  }
                });
              }
            });
          } else {
            console.error("表单实例未找到，无法验证");
          }
        }
      });
    } else if (item.cmd === "EDIT_NODE") {
      // 修改节点
      addDialog({
        title: "修改节点",
        contentRenderer: () =>
          h(NodeForm, {
            ref: formRef,
            formInline: {
              label: nodeData.label // 现有节点的标签
            },
            parentLabel: nodeData.label,
            labelId: nodeData.id,
            name: "当前节点名称",
            designation: "修改后节点名称"
          }),
        beforeSure: done => {
          const formInstance = formRef.value.formRef;
          if (formInstance) {
            formInstance.validate(valid => {
              if (valid) {
                const formData = {
                  ...formRef.value.formInline,
                  id: nodeData.id
                };
                updateTreeNode(formData).then(res => {
                  if (res.code === 200) {
                    ElMessage.success(`节点【${formData.label}】修改成功`);
                    fetchAreaTree();
                    // 动态更新显示内容
                    nodeData.label = formData.label; // 更新当前节点的标签
                    done();
                  }
                });
              }
            });
          } else {
            console.error("表单实例未找到，无法验证");
          }
        }
      });
    }
  };

  return {
    formRef,
    selectedTreeNode,
    filterParams,
    loading,
    dataList,
    pagination,
    columns,
    onSearch,
    resetFilterForm,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    handleDelete,
    editDevice,
    treeData,
    treeLoading,
    onTreeSelect,
    onTreeContextMenu
  };
}
