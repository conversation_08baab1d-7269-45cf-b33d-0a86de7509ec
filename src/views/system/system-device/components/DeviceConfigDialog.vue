<template>
  <el-dialog
    v-model="visible"
    :title="`${deviceType === '电脑' ? '电脑' : '摄像机'}配置`"
    width="90vw"
    class="device-config-dialog"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="180px"
      class="device-config-form"
    >
      <el-tabs v-model="activeTab" class="config-tabs">
        <!-- 电脑配置 -->
        <template v-if="deviceType === '电脑'">
          <el-tab-pane label="基础配置" name="basic">
            <el-form-item label="开发模式" prop="devMode">
              <el-switch v-model="form.devMode" />
            </el-form-item>
            <el-form-item label="设备地址" prop="addressText">
              <el-input v-model="form.addressText" />
            </el-form-item>
            <!-- 摄像头配置 -->
            <el-form-item label="来向摄像头RTSP地址" prop="cameraUrl1">
              <el-input v-model="form.cameraUrl1" />
            </el-form-item>
            <el-form-item label="去向摄像头RTSP地址" prop="cameraUrl2">
              <el-input v-model="form.cameraUrl2" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="区域配置" name="area">
            <CameraMark
              :form="form"
              :deviceInfo="props.deviceInfo"
              @updateForm="updateFormData"
            />
          </el-tab-pane>

          <el-tab-pane label="识别配置" name="recognition">
            <el-form-item label="头盔识别阈值" prop="nohelmetThreshold">
              <el-input-number
                v-model="form.nohelmetThreshold"
                :min="0"
                :max="1"
                :step="0.01"
              />
            </el-form-item>
            <el-form-item label="雨棚识别阈值" prop="weatherShieldThreshold">
              <el-input-number
                v-model="form.weatherShieldThreshold"
                :min="0"
                :max="1"
                :step="0.01"
              />
            </el-form-item>
            <el-form-item
              label="人脸识别阈值"
              prop="faceRecogDistanceThreshold"
            >
              <el-input-number
                v-model="form.faceRecogDistanceThreshold"
                :min="0"
                :max="1"
                :step="0.01"
              />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="时间配置" name="timing">
            <el-form-item label="劝导有效期(秒)" prop="persuadTimeDuration">
              <el-input-number v-model="form.persuadTimeDuration" :min="0" />
            </el-form-item>
            <el-form-item
              label="人脸识别间隔(秒)"
              prop="advicerFaceRecogDuration"
            >
              <el-input-number
                v-model="form.advicerFaceRecogDuration"
                :min="0"
              />
            </el-form-item>
            <el-form-item label="参数加载间隔(秒)" prop="loadParamInterval">
              <el-input-number v-model="form.loadParamInterval" :min="0" />
            </el-form-item>
            <el-form-item
              label="人脸库加载间隔(秒)"
              prop="loadFaceDatabaseInterval"
            >
              <el-input-number
                v-model="form.loadFaceDatabaseInterval"
                :min="0"
              />
            </el-form-item>
            <el-form-item label="车辆跟踪时长(秒)" prop="bikeTrackidLife">
              <el-input-number v-model="form.bikeTrackidLife" :min="0" />
            </el-form-item>
            <el-form-item label="违法视频前后时长(秒)" prop="startEndTime">
              <el-input-number v-model="form.startEndTime" :min="0" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="视频配置" name="video">
            <el-form-item label="保存违法视频" prop="saveViolateVideo">
              <el-switch v-model="form.saveViolateVideo" />
            </el-form-item>
            <el-form-item label="视频帧率" prop="videoFps">
              <el-input-number v-model="form.videoFps" :min="1" />
            </el-form-item>
            <el-form-item label="车牌图像数量" prop="bikePlatesQuantity">
              <el-input-number v-model="form.bikePlatesQuantity" :min="1" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="播报配置" name="announce">
            <el-form-item label="播报次数" prop="announceTimes">
              <el-row v-for="(value, key) in form.announceTimes" :key="key">
                <el-col :span="24" class="config-item">
                  <div class="config-item-content">
                    <span
                      >检测到
                      <el-input-number
                        v-model="vehicleCount[key]"
                        :min="1"
                        :controls="false"
                        style="width: 80px; margin: 0 8px"
                        @change="val => handleVehicleCountChange(key, val)"
                      />
                      辆时播报</span
                    >
                    <div class="config-item-controls">
                      <el-input-number
                        :model-value="Number(form.announceTimes[key])"
                        :min="0"
                        :placeholder="'播报次数'"
                        @update:model-value="
                          val => updateAnnounceTime(key, val)
                        "
                      />
                      <el-button
                        type="danger"
                        size="small"
                        class="ml-2"
                        @click="removeAnnounceTime(key)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <div class="add-button-container">
                <el-button
                  type="primary"
                  circle
                  size="small"
                  @click="addAnnounceTime"
                >
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="最大连续播报次数" prop="maxAnnounceTimes">
              <el-input-number v-model="form.maxAnnounceTimes" :min="1" />
            </el-form-item>
            <el-form-item label="播报间隔(秒)" prop="announceTimeout">
              <el-input-number v-model="form.announceTimeout" :min="0" />
            </el-form-item>
            <el-form-item label="播报音量" prop="announceVolume">
              <el-row
                v-for="(value, hour) in volumeConfig"
                :key="hour"
                class="config-item"
              >
                <el-col :span="24">
                  <div class="config-item-content">
                    <div class="config-item-controls">
                      <el-select
                        v-model="volumeTimes[hour]"
                        style="width: 120px"
                        @change="val => handleTimeChange(hour, val)"
                      >
                        <el-option
                          v-for="h in 24"
                          :key="h - 1"
                          :label="`${String(h - 1).padStart(2, '0')}:00`"
                          :value="String(h - 1)"
                        />
                      </el-select>
                      <el-input-number
                        v-model="volumeConfig[hour]"
                        :min="0"
                        :max="1"
                        :step="0.1"
                        @change="handleVolumeChange"
                      />
                      <el-button
                        type="danger"
                        size="small"
                        class="ml-2"
                        @click="removeVolumeTime(hour)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <div class="add-button-container">
                <el-button
                  type="primary"
                  circle
                  size="small"
                  @click="addVolumeTime"
                >
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="播报时间段">
              <el-select
                v-model="form.unmuteStart"
                class="mr-2"
                style="width: 120px"
              >
                <el-option
                  v-for="h in 24"
                  :key="h"
                  :label="`${String(h - 1).padStart(2, '0')}:00`"
                  :value="h - 1"
                />
              </el-select>
              <span class="mx-2">至</span>
              <el-select v-model="form.unmuteEnd" style="width: 120px">
                <el-option
                  v-for="h in 24"
                  :key="h"
                  :label="`${String(h - 1).padStart(2, '0')}:00`"
                  :value="h - 1"
                />
              </el-select>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="其他配置" name="other">
            <el-form-item label="重试间隔(秒)" prop="retryIntervals">
              <el-row v-for="(val, index) in form.retryIntervals" :key="index">
                <el-col :span="24" class="config-item">
                  <div class="config-item-content">
                    <span>第 {{ index + 1 }} 次重试</span>
                    <div class="config-item-controls">
                      <el-input-number
                        v-model="form.retryIntervals[index]"
                        :min="0"
                        :placeholder="'间隔时间'"
                      />
                      <el-button
                        type="danger"
                        size="small"
                        class="ml-2"
                        @click="removeRetryInterval(index)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <div class="add-button-container">
                <el-button
                  type="primary"
                  circle
                  size="small"
                  @click="addRetryInterval"
                >
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="在线状态间隔(秒)" prop="onlineStatusInterval">
              <el-input-number v-model="form.onlineStatusInterval" :min="0" />
            </el-form-item>
            <el-form-item label="背面碰线优先" prop="backwardPriority">
              <el-switch v-model="form.backwardPriority" />
            </el-form-item>
            <el-form-item label="立即更新设备参数" prop="urgent">
              <el-switch v-model="form.urgent" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="模型配置" name="model">
            <el-form-item label="头盔模型地址" prop="modelFile.helmetUrl">
              <el-input v-model="modelConfig.helmetUrl" />
            </el-form-item>
            <el-form-item label="头盔模型版本" prop="modelFile.helmetVersion">
              <el-input-number v-model="modelConfig.helmetVersion" :min="1" />
            </el-form-item>
            <el-form-item label="车牌模型地址" prop="modelFile.plateUrl">
              <el-input v-model="modelConfig.plateUrl" />
            </el-form-item>
            <el-form-item label="车牌模型版本" prop="modelFile.plateVersion">
              <el-input-number v-model="modelConfig.plateVersion" :min="1" />
            </el-form-item>
          </el-tab-pane>
        </template>

        <!-- 摄像机配置 -->
        <template v-else>
          <el-tab-pane label="基础配置" name="basic">
            <el-form-item label="设备地址" prop="addressText">
              <el-input v-model="form.addressText" />
            </el-form-item>
          </el-tab-pane>
        </template>
      </el-tabs>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import flvjs from "flv.js";
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
  withDefaults
} from "vue";
import CameraMark from "./CameraMark.vue";
// import "vue-cropper/dist/index.css";
import type { DeviceItem } from "../utils/types";

const props = defineProps<{
  config: DeviceItem;
  deviceInfo: DeviceItem;
  visible: boolean;
  deviceType: string;
}>();

const emit = defineEmits<{
  "update:config": [value: DeviceItem];
  "update:visible": [value: boolean];
}>();

const activeTab = ref("basic");
const formRef = ref<FormInstance>();

// 视频相关的 refs
const video1Ref = ref<HTMLVideoElement>();
const video2Ref = ref<HTMLVideoElement>();
const video1LineRef = ref<HTMLVideoElement>();
const video2LineRef = ref<HTMLVideoElement>();
const showClipRect1 = ref(false);
const showClipRect2 = ref(false);
const isDragging = ref(false);
const isResizing = ref(false);
const currentVideo = ref(1);
const currentResizeDirection = ref("");
const isPlaying1 = ref(false);
const isPlaying2 = ref(false);
const flvPlayer1 = ref<flvjs.Player | null>(null);
const flvPlayer2 = ref<flvjs.Player | null>(null);
const flvPlayer1Line = ref<flvjs.Player | null>(null);
const flvPlayer2Line = ref<flvjs.Player | null>(null);

const cropper1 = ref();
const cropper2 = ref();
const cropInfo1 = ref({ x1: 0, y1: 0, x2: 1920, y2: 1080 });
const cropInfo2 = ref({ x1: 0, y1: 0, x2: 1920, y2: 1080 });
const videoSrc1 = ref("");
const videoSrc2 = ref("");

// 修改帧更新间隔引用的类型
const frameUpdateInterval1 = ref<ReturnType<typeof setInterval> | null>(null);
const frameUpdateInterval2 = ref<ReturnType<typeof setInterval> | null>(null);

// 拖拽相关状态
const dragStartPos = ref<{ x: number; y: number }>({ x: 0, y: 0 });
const dragStartRect = ref<{
  x: number;
  y: number;
  width: number;
  height: number;
}>({
  x: 0,
  y: 0,
  width: 0,
  height: 0
});

// 添加视频尺寸的响应式变量
const video1Size = ref({ width: 1920, height: 1080 });
const video2Size = ref({ width: 1920, height: 1080 });

// 添加线段相关的状态
const isDrawingFirstPoint = ref(false);
const firstPoint = ref<{ x: number; y: number } | null>(null);
const isDrawingLine = ref(false);
const isDraggingPoint = ref(false);
const currentLine = ref(1);
const currentPoint = ref<"start" | "end" | null>(null);

// 添加画线区域的播放状态
const isPlaying1Line = ref(false);
const isPlaying2Line = ref(false);

// 添加深拷贝函数
const deepClone = <T,>(obj: T): T => {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch (e) {
    console.error("Deep clone failed:", e);
    return obj;
  }
};

// 添加默认配置
const defaultAnnounceVolume = {
  "0": 0.3,
  "6": 0.5,
  "8": 0.8,
  "12": 1.0,
  "18": 0.8,
  "22": 0.3
};

const defaultModelFile = {
  helmetUrl: "",
  helmetVersion: 0,
  plateUrl: "",
  plateVersion: 0
};

// 添加响应式对象
const volumeConfig = ref<Record<string, number>>(defaultAnnounceVolume);
const modelConfig = ref(defaultModelFile);

// 添加一个新的响应式对象来存储车辆数量
const vehicleCount = ref<Record<string, number>>({});

// 修改 parseAnnounceTimes 函数，确保返回对象格式
const parseAnnounceTimes = (
  value: Record<string, number> | string | undefined
): Record<string, number> => {
  if (!value) return {};
  if (typeof value === "string") {
    try {
      return JSON.parse(value);
    } catch (e) {
      console.error("解析 announceTimes 失败:", e);
      return {};
    }
  }
  return value;
};

// 添加一个工具函数来转换数组
const convertArrayToNumbers = (arr: (string | number)[]): number[] => {
  return arr.map(item => (typeof item === "string" ? parseInt(item) : item));
};

// 添加一个工具函数来处理时间字符串转数字
const parseTimeToNumber = (time: string | number | undefined): number => {
  if (typeof time === "number") return time;
  if (!time) return 0;

  // 如果是 "HH:mm" 格式的字符串，提取小时数
  const match = String(time).match(/^(\d{1,2}):00$/);
  if (match) {
    return Number(match[1]);
  }
  return 0;
};

// 修改表单初始化
const form = ref<DeviceItem>({
  ...deepClone(props.config),
  announceVolume: JSON.stringify(defaultAnnounceVolume),
  modelFile: JSON.stringify(defaultModelFile),
  announceTimes: {},
  // 修改这里：新增和编辑都使用数字类型
  unmuteStart: 8, // 默认值设为 8（对应 "08:00"）
  unmuteEnd: 21, // 默认值设为 21（对应 "21:00"）
  clipRect1: convertArrayToNumbers(props.config.clipRect1),
  clipRect2: convertArrayToNumbers(props.config.clipRect2),
  line1: convertArrayToNumbers(props.config.line1),
  line2: convertArrayToNumbers(props.config.line2),
  urgent: false
});

// 用于存储时间选择器的值
const volumeTimes = ref<Record<string, string>>({});

// 修改初始化时间选择器的值函数
const initVolumeTimes = () => {
  try {
    // 尝试解析 JSON 字符串
    const parsedVolume = form.value.announceVolume
      ? JSON.parse(form.value.announceVolume)
      : defaultAnnounceVolume;

    volumeConfig.value = parsedVolume;

    // 初始化时间选择器的值
    Object.keys(parsedVolume).forEach(hour => {
      volumeTimes.value[hour] = hour;
    });
  } catch (e) {
    console.error("解析音量配置失败:", e);
    volumeConfig.value = defaultAnnounceVolume;
    Object.keys(defaultAnnounceVolume).forEach(hour => {
      volumeTimes.value[hour] = hour;
    });
  }
};

// 修改添加时间点函数
const addVolumeTime = () => {
  // 获取当前所有时间点
  const existingHours = Object.keys(volumeConfig.value)
    .map(hour => parseInt(hour))
    .sort((a, b) => a - b);

  // 找到第一个未使用的小时数（0-23）
  let newHour = 0;
  while (existingHours.includes(newHour) && newHour < 24) {
    newHour++;
  }

  // 如果所有时间点都已使用，则不添加
  if (newHour >= 24) {
    ElMessage.warning("已达到最大时间点数量（24个）");
    return;
  }

  // 格式化为两位数字的字符串
  const newHourStr = String(newHour).padStart(2, "0");

  // 直接添加新的时间点到现有配置
  volumeConfig.value = {
    ...volumeConfig.value,
    [newHourStr]: 0.5
  };

  // 更新时间选择器
  volumeTimes.value[newHourStr] = newHourStr;

  // 更新表单中的 JSON 字符串
  form.value.announceVolume = JSON.stringify(volumeConfig.value);
};

// 修改删除时间点函数
const removeVolumeTime = (hour: string) => {
  delete volumeConfig.value[hour];
  delete volumeTimes.value[hour];
  // 更新表单中的 JSON 字符串
  form.value.announceVolume = JSON.stringify(volumeConfig.value);
};

// 修改时间变化处理函数
const handleTimeChange = (oldHour: string, newHour: string) => {
  if (oldHour !== newHour) {
    const volume = volumeConfig.value[oldHour];

    // 创建新的配置对象，保持其他配置不变
    const newVolumeConfig = { ...volumeConfig.value };
    delete newVolumeConfig[oldHour];
    newVolumeConfig[newHour] = volume;

    // 更新配置
    volumeConfig.value = newVolumeConfig;
    delete volumeTimes.value[oldHour];
    volumeTimes.value[newHour] = newHour;

    // 更新表单中的 JSON 字符串
    form.value.announceVolume = JSON.stringify(newVolumeConfig);
  }
};

// 修改音量变化处理函数
const handleVolumeChange = () => {
  // 直接更新表单中的 JSON 字符串
  form.value.announceVolume = JSON.stringify(volumeConfig.value);
};

// 修改添加播报规则的方法
const addAnnounceTime = () => {
  // 获取当前所有车辆数量
  const existingCounts = Object.keys(vehicleCount.value)
    .map(k => vehicleCount.value[k])
    .sort((a, b) => a - b);

  // 找到第一个未使用的车辆数量
  let newCount = 1;
  while (existingCounts.includes(newCount)) {
    newCount++;
  }

  // 确保 form.value.announceTimes 是对象类型
  if (typeof form.value.announceTimes === "string") {
    form.value.announceTimes = JSON.parse(form.value.announceTimes);
  }

  // 使用类型断言确保 TypeScript 知道这是一个对象
  const currentAnnounceTimes = form.value.announceTimes as Record<
    string,
    number
  >;

  // 更新配置
  form.value.announceTimes = {
    ...currentAnnounceTimes,
    [newCount]: 1
  };

  // 更新车辆数量映射
  vehicleCount.value[newCount] = newCount;
};

// 修改车辆数量变化处理方法
const handleVehicleCountChange = (key: string, newCount: number) => {
  if (typeof form.value.announceTimes === "string") {
    form.value.announceTimes = JSON.parse(form.value.announceTimes);
  }

  const currentAnnounceTimes = form.value.announceTimes as Record<
    string,
    number
  >;
  const oldValue = currentAnnounceTimes[key];

  // 删除旧的键值对
  delete currentAnnounceTimes[key];

  // 使用新的车辆数量作为键
  const newAnnounceTimes = {
    ...currentAnnounceTimes,
    [newCount]: oldValue
  };

  // 按照车辆数量排序
  const sortedEntries = Object.entries(newAnnounceTimes).sort(
    ([a], [b]) => parseInt(a) - parseInt(b)
  );

  // 重建有序对象，并显式指定类型
  form.value.announceTimes = Object.fromEntries(sortedEntries) as Record<
    string,
    number
  >;
};

// 修改更新播报次数的方法
const updateAnnounceTime = (key: string, value: number) => {
  if (typeof form.value.announceTimes === "string") {
    form.value.announceTimes = JSON.parse(form.value.announceTimes);
  }

  const currentAnnounceTimes = form.value.announceTimes as Record<
    string,
    number
  >;
  const vehicleCount = parseInt(key);

  // 使用车辆数量作为键，更新播报次数
  const newAnnounceTimes = {
    ...currentAnnounceTimes,
    [vehicleCount]: value
  };

  // 按照车辆数量排序
  const sortedEntries = Object.entries(newAnnounceTimes).sort(
    ([a], [b]) => parseInt(a) - parseInt(b)
  );

  // 重建有序对象，并显式指定类型
  form.value.announceTimes = Object.fromEntries(sortedEntries) as Record<
    string,
    number
  >;
};

// 修改删除播报规则的方法
const removeAnnounceTime = (key: string) => {
  if (typeof form.value.announceTimes === "string") {
    form.value.announceTimes = JSON.parse(form.value.announceTimes);
  }

  const currentAnnounceTimes = form.value.announceTimes as Record<
    string,
    number
  >;

  // 删除对应的配置
  delete currentAnnounceTimes[key];
  delete vehicleCount.value[key];

  // 按照车辆数量排序
  const sortedEntries = Object.entries(currentAnnounceTimes).sort(
    ([a], [b]) => parseInt(a) - parseInt(b)
  );

  // 重建有序对象
  form.value.announceTimes = Object.fromEntries(sortedEntries) as Record<
    string,
    number
  >;
};

// 修改添加重试间隔的函数
const addRetryInterval = () => {
  // 确保 retryIntervals 是数组且初始化为空数组
  if (!form.value.retryIntervals) {
    form.value.retryIntervals = [];
  }

  // 获取最后一个间隔值并确保是整数
  const lastInterval =
    form.value.retryIntervals.length > 0
      ? Math.floor(
          form.value.retryIntervals[form.value.retryIntervals.length - 1]
        )
      : 0;

  // 计算新的间隔值：如果有上一个值则翻倍，否则默认30秒
  const newInterval = Math.floor(lastInterval > 0 ? lastInterval * 2 : 30);

  // 添加新的间隔值
  form.value.retryIntervals = [...form.value.retryIntervals, newInterval];
};

// 修改删除重试间隔的函数
const removeRetryInterval = (index: number) => {
  if (Array.isArray(form.value.retryIntervals)) {
    form.value.retryIntervals = form.value.retryIntervals.filter(
      (_, i) => i !== index
    );
  }
};

// 修改视频加载完成的处理函数
const onVideoLoaded = (videoNum: number, isLine = false) => {
  const video = isLine
    ? videoNum === 1
      ? video1LineRef.value
      : video2LineRef.value
    : videoNum === 1
      ? video1Ref.value
      : video2Ref.value;
  if (!video) return;

  // 更新视频尺寸
  if (videoNum === 1) {
    video1Size.value = {
      width: video.videoWidth || 1920,
      height: video.videoHeight || 1080
    };
  } else {
    video2Size.value = {
      width: video.videoWidth || 1920,
      height: video.videoHeight || 1080
    };
  }

  // 初始化播放器
  initFlvPlayer(videoNum, isLine);
};

// 修改初始化播放器函数
const initFlvPlayer = (videoNum: number, isLine = false) => {
  try {
    const video = isLine
      ? videoNum === 1
        ? video1LineRef.value
        : video2LineRef.value
      : videoNum === 1
        ? video1Ref.value
        : video2Ref.value;
    const url = videoNum === 1 ? form.value.cameraUrl1 : form.value.cameraUrl2;

    if (!video || !url) return;

    // 确保销毁之前的播放器
    destroyFlvPlayer(videoNum, isLine);

    if (flvjs.isSupported()) {
      const player = flvjs.createPlayer({
        type: "flv",
        url: url,
        isLive: true
      });

      player.attachMediaElement(video);
      player.load();

      if (videoNum === 1) {
        if (isLine) {
          flvPlayer1Line.value = player;
        } else {
          flvPlayer1.value = player;
        }
        isPlaying1.value = true;
      } else {
        if (isLine) {
          flvPlayer2Line.value = player;
        } else {
          flvPlayer2.value = player;
        }
        isPlaying2.value = true;
      }

      // 自动播放
      const playResult = player.play();
      if (playResult instanceof Promise) {
        playResult.catch(error => {
          console.warn("Auto play failed:", error);
        });
      }
    }
  } catch (error) {
    console.error(`Failed to initialize player ${videoNum}:`, error);
  }
};

// 停止帧更新
const stopFrameUpdate = (videoNum: number) => {
  if (videoNum === 1 && frameUpdateInterval1.value) {
    clearInterval(frameUpdateInterval1.value);
    frameUpdateInterval1.value = null;
  } else if (videoNum === 2 && frameUpdateInterval2.value) {
    clearInterval(frameUpdateInterval2.value);
    frameUpdateInterval2.value = null;
  }
};

// 销毁播放器
const destroyFlvPlayer = (videoNum: number, isLine = false) => {
  const player = isLine
    ? videoNum === 1
      ? flvPlayer1Line.value
      : flvPlayer2Line.value
    : videoNum === 1
      ? flvPlayer1.value
      : flvPlayer2.value;
  if (player) {
    player.pause();
    player.unload();
    player.detachMediaElement();
    player.destroy();
    if (videoNum === 1) {
      if (isLine) {
        flvPlayer1Line.value = null;
      } else {
        flvPlayer1.value = null;
      }
      isPlaying1.value = false;
      stopFrameUpdate(1);
    } else {
      if (isLine) {
        flvPlayer2Line.value = null;
      } else {
        flvPlayer2.value = null;
      }
      isPlaying2.value = false;
      stopFrameUpdate(2);
    }
  }
};

const onVideoEnded = (videoNum: number, isLine = false) => {
  if (videoNum === 1) {
    if (isLine) {
      isPlaying1Line.value = false;
    } else {
      isPlaying1.value = false;
    }
  } else {
    if (isLine) {
      isPlaying2Line.value = false;
    } else {
      isPlaying2.value = false;
    }
  }
};

const startDrag = (e: MouseEvent, videoNum: number) => {
  if (
    e.target instanceof HTMLElement &&
    e.target.classList.contains("resize-handle")
  ) {
    return;
  }
  isDragging.value = true;
  currentVideo.value = videoNum;
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  dragStartPos.value = { x, y };

  // 保存裁剪框的初始位置和大小
  const clipRect = videoNum === 1 ? form.value.clipRect1 : form.value.clipRect2;
  dragStartRect.value = {
    x: clipRect[0],
    y: clipRect[1],
    width: clipRect[2],
    height: clipRect[3]
  };

  document.addEventListener("mousemove", onDrag);
  document.addEventListener("mouseup", stopDrag);
};

const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;
  const video = currentVideo.value === 1 ? video1Ref.value : video2Ref.value;
  const rect = video?.getBoundingClientRect();
  if (!rect || !video) return;

  const scaleX = video1Size.value.width / rect.width;
  const scaleY = video1Size.value.height / rect.height;

  // 计算移动距离
  const deltaX = (e.clientX - rect.left - dragStartPos.value.x) * scaleX;
  const deltaY = (e.clientY - rect.top - dragStartPos.value.y) * scaleY;

  // 应用新位置，确保不超出边界
  const newX = Math.max(
    0,
    Math.min(
      dragStartRect.value.x + deltaX,
      video1Size.value.width - dragStartRect.value.width
    )
  );
  const newY = Math.max(
    0,
    Math.min(
      dragStartRect.value.y + deltaY,
      video1Size.value.height - dragStartRect.value.height
    )
  );

  if (currentVideo.value === 1) {
    form.value.clipRect1[0] = newX;
    form.value.clipRect1[1] = newY;
    // 更新裁剪信息显示
    cropInfo1.value = {
      x1: Math.round(newX),
      y1: Math.round(newY),
      x2: Math.round(newX + dragStartRect.value.width),
      y2: Math.round(newY + dragStartRect.value.height)
    };
  } else {
    form.value.clipRect2[0] = newX;
    form.value.clipRect2[1] = newY;
    // 更新裁剪信息显示
    cropInfo2.value = {
      x1: Math.round(newX),
      y1: Math.round(newY),
      x2: Math.round(newX + dragStartRect.value.width),
      y2: Math.round(newY + dragStartRect.value.height)
    };
  }
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
};

const startResize = (e: MouseEvent, videoNum: number, direction: string) => {
  e.stopPropagation();
  isResizing.value = true;
  currentVideo.value = videoNum;
  currentResizeDirection.value = direction;
  document.addEventListener("mousemove", onResize);
  document.addEventListener("mouseup", stopResize);
};

const onResize = (e: MouseEvent) => {
  if (!isResizing.value) return;
  const video = currentVideo.value === 1 ? video1Ref.value : video2Ref.value;
  const rect = video?.getBoundingClientRect();
  if (!rect || !video) return;

  const scaleX = video1Size.value.width / rect.width;
  const scaleY = video1Size.value.height / rect.height;

  // 根据不同方向处理调整逻辑
  const mouseX = (e.clientX - rect.left) * scaleX;
  const mouseY = (e.clientY - rect.top) * scaleY;
  const clipRect =
    currentVideo.value === 1 ? form.value.clipRect1 : form.value.clipRect2;

  // 保存原始值以便计算
  const originalX = clipRect[0];
  const originalY = clipRect[1];
  const originalWidth = clipRect[2];
  const originalHeight = clipRect[3];

  switch (currentResizeDirection.value) {
    case "tl": // top-left
      clipRect[0] = Math.max(
        0,
        Math.min(mouseX, originalX + originalWidth - 50)
      );
      clipRect[1] = Math.max(
        0,
        Math.min(mouseY, originalY + originalHeight - 50)
      );
      clipRect[2] = Math.max(50, originalX + originalWidth - clipRect[0]);
      clipRect[3] = Math.max(50, originalY + originalHeight - clipRect[1]);
      break;

    case "tr": // top-right
      clipRect[2] = Math.max(
        50,
        Math.min(mouseX - originalX, video1Size.value.width - originalX)
      );
      clipRect[1] = Math.max(
        0,
        Math.min(mouseY, originalY + originalHeight - 50)
      );
      clipRect[3] = Math.max(50, originalY + originalHeight - clipRect[1]);
      break;

    case "bl": // bottom-left
      clipRect[0] = Math.max(
        0,
        Math.min(mouseX, originalX + originalWidth - 50)
      );
      clipRect[2] = Math.max(50, originalX + originalWidth - clipRect[0]);
      clipRect[3] = Math.max(
        50,
        Math.min(mouseY - originalY, video1Size.value.height - originalY)
      );
      break;

    case "br": // bottom-right
      clipRect[2] = Math.max(
        50,
        Math.min(mouseX - originalX, video1Size.value.width - originalX)
      );
      clipRect[3] = Math.max(
        50,
        Math.min(mouseY - originalY, video1Size.value.height - originalY)
      );
      break;

    case "t": // top
      clipRect[1] = Math.max(
        0,
        Math.min(mouseY, originalY + originalHeight - 50)
      );
      clipRect[3] = Math.max(50, originalY + originalHeight - clipRect[1]);
      break;

    case "r": // right
      clipRect[2] = Math.max(
        50,
        Math.min(mouseX - originalX, video1Size.value.width - originalX)
      );
      break;

    case "b": // bottom
      clipRect[3] = Math.max(
        50,
        Math.min(mouseY - originalY, video1Size.value.height - originalY)
      );
      break;

    case "l": // left
      clipRect[0] = Math.max(
        0,
        Math.min(mouseX, originalX + originalWidth - 50)
      );
      clipRect[2] = Math.max(50, originalX + originalWidth - clipRect[0]);
      break;
  }

  // 更新裁剪信息显示
  if (currentVideo.value === 1) {
    cropInfo1.value = {
      x1: Math.round(clipRect[0]),
      y1: Math.round(clipRect[1]),
      x2: Math.round(clipRect[0] + clipRect[2]),
      y2: Math.round(clipRect[1] + clipRect[3])
    };
  } else {
    cropInfo2.value = {
      x1: Math.round(clipRect[0]),
      y1: Math.round(clipRect[1]),
      x2: Math.round(clipRect[0] + clipRect[2]),
      y2: Math.round(clipRect[1] + clipRect[3])
    };
  }
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener("mousemove", onResize);
  document.removeEventListener("mouseup", stopResize);
};

// 修改 watch
watch(
  () => props.config,
  newConfig => {
    let parsedAnnounceVolume = defaultAnnounceVolume;
    let parsedModelFile = defaultModelFile;
    let parsedAnnounceTimes = {};

    // 尝试解析 announceVolume
    if (newConfig.announceVolume) {
      try {
        parsedAnnounceVolume = JSON.parse(newConfig.announceVolume);
      } catch (e) {
        console.error("解析音量配置失败:", e);
      }
    }

    // 尝试解析 modelFile
    if (newConfig.modelFile) {
      try {
        parsedModelFile = JSON.parse(newConfig.modelFile);
      } catch (e) {
        console.error("解析模型配置失败:", e);
      }
    }

    // 尝试解析 announceTimes
    if (newConfig.announceTimes) {
      try {
        parsedAnnounceTimes = parseAnnounceTimes(newConfig.announceTimes);
      } catch (e) {
        console.error("解析播报次数配置失败:", e);
      }
    }

    // 确保 retryIntervals 被正确初始化为整数数组
    const retryIntervals = Array.isArray(newConfig.retryIntervals)
      ? newConfig.retryIntervals.map(val => Math.floor(Number(val)))
      : [];

    // 更新 form 的值
    form.value = {
      ...deepClone(newConfig),
      announceVolume: JSON.stringify(parsedAnnounceVolume),
      modelFile: JSON.stringify(parsedModelFile),
      announceTimes: parsedAnnounceTimes,
      retryIntervals,
      // 修改这里：使用新的解析函数
      unmuteStart: parseTimeToNumber(newConfig.unmuteStart),
      unmuteEnd: parseTimeToNumber(newConfig.unmuteEnd),
      clipRect1: convertArrayToNumbers(newConfig.clipRect1),
      clipRect2: convertArrayToNumbers(newConfig.clipRect2),
      line1: convertArrayToNumbers(newConfig.line1),
      line2: convertArrayToNumbers(newConfig.line2),
      urgent: newConfig.urgent ?? false
    };

    // 更新配置
    volumeConfig.value = parsedAnnounceVolume;
    modelConfig.value = parsedModelFile;

    // 初始化时间选择器
    initVolumeTimes();

    // 初始化 vehicleCount
    vehicleCount.value = {};
    if (parsedAnnounceTimes) {
      Object.keys(parsedAnnounceTimes).forEach((key, index) => {
        vehicleCount.value[key] = index + 1;
      });
    }
  },
  { immediate: true, deep: true }
);

// 修改提交处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    // 创建一个新对象来存储提交的数据
    const submitData = {
      ...form.value,
      announceVolume: JSON.stringify(volumeConfig.value),
      modelFile: JSON.stringify(modelConfig.value),
      announceTimes: "[]", // 修改为空数组字符串
      clipRect1: convertArrayToNumbers(form.value.clipRect1),
      clipRect2: convertArrayToNumbers(form.value.clipRect2),
      line1: convertArrayToNumbers(form.value.line1),
      line2: convertArrayToNumbers(form.value.line2)
    };

    emit("update:config", submitData);
    emit("update:visible", false);
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 对话框关闭处理
const handleClose = () => {
  destroyFlvPlayer(1);
  destroyFlvPlayer(2);
  emit("update:visible", false);
};

// 取消按钮点击处理
const handleCancel = () => {
  emit("update:visible", false);
};

// 更新表单数据的方法
const updateFormData = (newData: DeviceItem) => {
  form.value = {
    ...form.value,
    ...newData
  };
  emit("update:config", form.value);
};

// 计算视频容器的缩放比例
const videoContainerStyle = computed(() => {
  // 获取对话框内容区域的宽度（考虑padding和边距）
  const dialogWidth = window.innerWidth * 0.95 - 40; // 95% 的窗口宽度减去padding
  const videoWidth = 1920;
  const videoHeight = 1080;

  // 计算合适的缩放比例，保持宽高比
  let scale = Math.min(
    (dialogWidth - 200) / videoWidth, // 减去左侧label的宽度
    (window.innerHeight * 0.7) / videoHeight // 限制高度不超过窗口高度的70%
  );

  // 确保缩放比例在合理范围内
  scale = Math.min(Math.max(scale, 0.2), 1);

  return {
    transform: `scale(${scale})`,
    transformOrigin: "top center",
    marginBottom: `${(1 - scale) * videoHeight}px` // 补偿缩放导致的空间压缩
  };
});

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  destroyFlvPlayer(1);
  destroyFlvPlayer(2);
  destroyFlvPlayer(1, true);
  destroyFlvPlayer(2, true);
  stopFrameUpdate(1);
  stopFrameUpdate(2);
});

const handleResize = () => {
  // 触发重新计算缩放比例
  nextTick(() => {
    videoContainerStyle.value;
  });
};

// 视频帧捕获
const captureVideoFrame = (video: HTMLVideoElement): string => {
  const canvas = document.createElement("canvas");
  canvas.width = 1920; // 使用固定分辨率
  canvas.height = 1080;
  const ctx = canvas.getContext("2d");
  if (!ctx) return "";

  // 清除之前的内容
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 绘制视频帧
  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  return canvas.toDataURL("image/jpeg", 0.8); // 使用较高的质量
};

// 定时更新视频帧
const updateVideoFrame = (videoNum: number, isLine = false) => {
  const video = isLine
    ? videoNum === 1
      ? video1LineRef.value
      : video2LineRef.value
    : videoNum === 1
      ? video1Ref.value
      : video2Ref.value;
  if (!video || video.readyState < 2) return; // 确保视频已经加载

  try {
    const frameData = captureVideoFrame(video);
    if (frameData) {
      if (videoNum === 1) {
        videoSrc1.value = frameData;
      } else {
        videoSrc2.value = frameData;
      }
    }
  } catch (error) {
    console.error("Failed to update video frame:", error);
  }
};

// 重置裁剪
const resetClip = (videoNum: number) => {
  const videoSize = videoNum === 1 ? video1Size.value : video2Size.value;
  if (videoNum === 1) {
    form.value.clipRect1 = [0, 0, videoSize.width, videoSize.height];
  } else {
    form.value.clipRect2 = [0, 0, videoSize.width, videoSize.height];
  }
};

// 重置裁剪区域
const resetCropArea = (videoNum: number) => {
  const videoSize = videoNum === 1 ? video1Size.value : video2Size.value;
  if (videoNum === 1) {
    form.value.clipRect1 = [0, 0, videoSize.width, videoSize.height];
    cropInfo1.value = {
      x1: 0,
      y1: 0,
      x2: videoSize.width,
      y2: videoSize.height
    };
  } else {
    form.value.clipRect2 = [0, 0, videoSize.width, videoSize.height];
    cropInfo2.value = {
      x1: 0,
      y1: 0,
      x2: videoSize.width,
      y2: videoSize.height
    };
  }
};

// 拖动端点过程
const onDragPoint = (e: MouseEvent) => {
  if (!isDraggingPoint.value) return;

  const lineNum = currentLine.value;
  const element = document.querySelector(`.line-overlay`);
  if (!element) return;

  const rect = element.getBoundingClientRect();
  const x = Math.round((e.clientX - rect.left) * (1920 / rect.width));
  const y = Math.round((e.clientY - rect.top) * (1080 / rect.height));

  const index = currentPoint.value === "start" ? 0 : 2;
  form.value[`line${lineNum}`][index] = Math.max(0, Math.min(x, 1920));
  form.value[`line${lineNum}`][index + 1] = Math.max(0, Math.min(y, 1080));
};

// 停止拖动端点
const stopDragPoint = () => {
  isDraggingPoint.value = false;
  currentPoint.value = null;
  document.removeEventListener("mousemove", onDragPoint);
  document.removeEventListener("mouseup", stopDragPoint);
};

// 重置线段
const resetLine = (lineNum: number) => {
  form.value[`line${lineNum}`] = [];
};

// 获取线段样式
const getLineStyle = (line: number[]) => {
  if (line.length !== 4) return {};

  const [x1, y1, x2, y2] = line;
  const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  const angle = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;

  return {
    width: `${length}px`,
    transform: `rotate(${angle}deg)`,
    left: `${x1}px`,
    top: `${y1}px`,
    transformOrigin: "left center"
  };
};

// 使用计算属性来处理对话框的可见性
const visible = computed({
  get: () => props.visible,
  set: val => emit("update:visible", val)
});

// 添加 watch 来监听 modelConfig 的变化
watch(
  modelConfig,
  newValue => {
    // 当 modelConfig 变化时，更新 form.modelFile
    form.value.modelFile = JSON.stringify(newValue);
  },
  { deep: true }
);
</script>

<style lang="scss">
.device-config-dialog {
  margin-top: 5vh;

  .el-dialog__body {
    height: 80vh;
    padding: 20px;
    overflow-y: auto;
  }

  .line-overlay {
    position: absolute;
    inset: 0;
    z-index: 3;
    cursor: crosshair;
    background-color: rgb(0 0 0 / 10%);
  }

  .line-segment {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .line {
      position: absolute;
      height: 2px;
      pointer-events: none;
      background-color: #409eff;
    }

    .line-point {
      position: absolute;
      z-index: 4;
      width: 12px;
      height: 12px;
      cursor: move;
      background-color: #409eff;
      border: 2px solid #fff;
      border-radius: 50%;
      transform: translate(-50%, -50%);

      &:hover {
        transform: translate(-50%, -50%) scale(1.2);
      }
    }
  }

  .line-info {
    padding: 8px 16px;
    font-family: monospace;
    background: #f5f7fa;
    border-radius: 4px;

    p {
      margin: 4px 0;
      color: #666;
    }
  }

  .line {
    &.temporary {
      height: 2px;
      background-color: rgb(64 158 255 / 50%);
    }
  }

  .config-item {
    margin-right: 30px;
  }
}

.config-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;

  .el-input-number {
    margin: 0 8px;
  }
}
</style>
