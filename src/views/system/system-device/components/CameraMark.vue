<template>
  <!-- 添加视频预览区域 -->
  <div class="preview-container">
    <div class="preview-item">
      <div>
        <h4>来向摄像头预览</h4>
        <video ref="videoRef1" style="display: none" />
        <div class="canvas-container">
          <canvas
            ref="canvasRef1"
            width="800"
            height="600"
            class="fabric-canvas"
          />
          <div v-if="loadingStates[1]" class="loading-overlay">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
        </div>
        <div class="video-info">
          视频规格: {{ videoSizes[1]?.width || 0 }}x{{
            videoSizes[1]?.height || 0
          }}
        </div>
      </div>
      <div class="right-container">
        <el-button
          type="primary"
          size="small"
          :loading="loadingStates[1]"
          :disabled="loadingStates[1]"
          @click="refreshFrame(1)"
        >
          重新获取帧
        </el-button>
        <el-button
          type="success"
          size="small"
          :disabled="hasRect[1] || loadingStates[1]"
          @click="startDrawRect(1)"
        >
          添加来向区域裁剪
        </el-button>
        <el-button
          type="success"
          size="small"
          :disabled="hasLine[1] || loadingStates[1]"
          @click="startDrawLine(1)"
        >
          添加来向区域碰线
        </el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="
            !fabricCanvasMap.get(1)?.getActiveObject() || loadingStates[1]
          "
          @click="deleteSelectedShape(1)"
        >
          删除选中项
        </el-button>
      </div>
    </div>
    <div class="switch">
      <el-button
        type="primary"
        size="small"
        :loading="loadingStates[1] || loadingStates[2]"
        :disabled="loadingStates[1] || loadingStates[2]"
        @click="switchCameras"
      >
        切换摄像头
      </el-button>
    </div>
    <div class="preview-item">
      <div>
        <h4>去向摄像头预览</h4>
        <video ref="videoRef2" style="display: none" />
        <div class="canvas-container">
          <canvas
            ref="canvasRef2"
            width="800"
            height="600"
            class="fabric-canvas"
          />
          <div v-if="loadingStates[2]" class="loading-overlay">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
        </div>
        <div class="video-info">
          视频规格: {{ videoSizes[2]?.width || 0 }}x{{
            videoSizes[2]?.height || 0
          }}
        </div>
      </div>
      <div class="right-container">
        <el-button
          type="primary"
          size="small"
          :loading="loadingStates[2]"
          :disabled="loadingStates[2]"
          @click="refreshFrame(2)"
        >
          重新获取帧
        </el-button>
        <el-button
          type="success"
          size="small"
          :disabled="hasRect[2] || loadingStates[2]"
          @click="startDrawRect(2)"
        >
          添加去向区域裁剪
        </el-button>
        <el-button
          type="success"
          size="small"
          :disabled="hasLine[2] || loadingStates[2]"
          @click="startDrawLine(2)"
        >
          添加去向区域碰线
        </el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="
            !fabricCanvasMap.get(2)?.getActiveObject() || loadingStates[2]
          "
          @click="deleteSelectedShape(2)"
        >
          删除选中项
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCameraUrl } from "@/api/device";
import { ElMessage } from "element-plus";
import { computed, onBeforeUnmount, onMounted, ref, watch } from "vue";
// import "vue-cropper/dist/index.css";
import { fabric } from "fabric";
import flvjs from "flv.js";
import type { DeviceItem } from "../utils/types";

const props = defineProps<{
  form: DeviceItem;
  deviceInfo: DeviceItem;
}>();

const emit = defineEmits<{
  (event: "updateForm", data: DeviceItem): void;
}>();

// 添加视频尺寸存储
interface VideoSize {
  width: number;
  height: number;
  displayWidth: number;
  displayHeight: number;
}

const videoSizes = ref<Record<number, VideoSize>>({});

// 添加视频尺寸的响应式变量
const video1Size = ref({ width: 1920, height: 1080 });
const video2Size = ref({ width: 1920, height: 1080 });

// 添加表单引用
const form = ref<DeviceItem>({
  ...props.form
});

// 监听 props.form 的变化
watch(
  () => props.form,
  newForm => {
    form.value = { ...newForm };
  },
  { immediate: true, deep: true }
);

// 修改样式计算
const cropBoxStyle1 = computed(() => {
  const { clipRect1 } = form.value;
  return {
    left: `${(clipRect1[0] / video1Size.value.width) * 100}%`,
    top: `${(clipRect1[1] / video1Size.value.height) * 100}%`,
    width: `${(clipRect1[2] / video1Size.value.width) * 100}%`,
    height: `${(clipRect1[3] / video1Size.value.height) * 100}%`
  };
});

// 添加第二个视频的样式计算
const cropBoxStyle2 = computed(() => {
  const { clipRect2 } = form.value;
  return {
    left: `${(clipRect2[0] / video2Size.value.width) * 100}%`,
    top: `${(clipRect2[1] / video2Size.value.height) * 100}%`,
    width: `${(clipRect2[2] / video2Size.value.width) * 100}%`,
    height: `${(clipRect2[3] / video2Size.value.height) * 100}%`
  };
});

// 扩展 HTMLVideoElement 类型
interface CustomVideoElement extends HTMLVideoElement {
  flvPlayer?: flvjs.Player;
}

// 修改 ref 的类型定义
const videoRef1 = ref<CustomVideoElement | null>(null);
const videoRef2 = ref<CustomVideoElement | null>(null);
const canvasRef1 = ref<HTMLCanvasElement | null>(null);
const canvasRef2 = ref<HTMLCanvasElement | null>(null);

// 修改 Canvas 存储方式，不使用响应式引用
const fabricCanvasMap = new Map<number, fabric.Canvas>();

// 修改 fabric.Canvas 实例存储的类型
const drawingMode = ref<Record<number, "rect" | "line" | null>>({});
const hasRect = ref<Record<number, boolean>>({});
const hasLine = ref<Record<number, boolean>>({});

// 添加 loading 状态
const loadingStates = ref<Record<number, boolean>>({
  1: false,
  2: false
});

// 添加新的状态变量来跟踪绘制状态
const isDrawingLine = ref<Record<number, boolean>>({});
const tempLine = ref<Record<number, fabric.Line>>({});
const firstPoint = ref<Record<number, { x: number; y: number }>>({});

onMounted(() => {
  fetchCameraUrls();
});

// 添加一个安全的视频销毁函数
const destroyVideoPlayer = (video: CustomVideoElement) => {
  if (!video.flvPlayer) return;

  try {
    const player = video.flvPlayer;
    // 先清空引用，避免重复销毁
    video.flvPlayer = null;

    // 按顺序执行销毁操作
    player.pause();
    player.unload();
    player.detachMediaElement();
    player.destroy();

    // 清理视频元素
    video.removeAttribute("src");
    video.load();
  } catch (error) {
    console.error("Error destroying video player:", error);
  }
};

// 首先定义返回类型接口
interface RectCoords {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface LineCoords {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

// 修改 convertToOriginalCoords 函数，转换为百分比坐标
const convertToOriginalCoords = (
  obj: fabric.Object,
  cameraIndex: 1 | 2
): RectCoords | LineCoords | null => {
  const videoSize = videoSizes.value[cameraIndex];
  if (!videoSize) return null;

  const canvas = fabricCanvasMap.get(cameraIndex);
  if (!canvas) return null;

  // 计算绘制区域的偏移
  const drawX = (canvas.width! - videoSize.displayWidth) / 2;
  const drawY = (canvas.height! - videoSize.displayHeight) / 2;

  if (obj instanceof fabric.Rect) {
    // 转换矩形坐标为百分比
    const x = obj.left! - drawX;
    const y = obj.top! - drawY;
    const width = obj.width! * obj.scaleX!;
    const height = obj.height! * obj.scaleY!;

    const coords: RectCoords = {
      x: Math.round((x / videoSize.displayWidth) * 100),
      y: Math.round((y / videoSize.displayHeight) * 100),
      width: Math.round((width / videoSize.displayWidth) * 100),
      height: Math.round((height / videoSize.displayHeight) * 100)
    };

    return coords;
  } else if (obj instanceof fabric.Line) {
    const line = obj as fabric.Line;
    const videoSize = videoSizes.value[cameraIndex];

    // 计算绘制区域的偏移
    const drawX = (canvas.width! - videoSize.displayWidth) / 2;
    const drawY = (canvas.height! - videoSize.displayHeight) / 2;

    // 获取线条在视频区域内的相对坐标
    const x1 = line.left! + line.x1! - drawX;
    const y1 = line.top! + line.y1! - drawY;
    const x2 = line.left! + line.x2! - drawX;
    const y2 = line.top! + line.y2! - drawY;

    // 确保坐标在视频区域内
    const clampedX1 = Math.max(0, Math.min(x1, videoSize.displayWidth));
    const clampedY1 = Math.max(0, Math.min(y1, videoSize.displayHeight));
    const clampedX2 = Math.max(0, Math.min(x2, videoSize.displayWidth));
    const clampedY2 = Math.max(0, Math.min(y2, videoSize.displayHeight));

    // 转换为百分比
    const coords: LineCoords = {
      x1: Math.round((clampedX1 / videoSize.displayWidth) * 100),
      y1: Math.round((clampedY1 / videoSize.displayHeight) * 100),
      x2: Math.round((clampedX2 / videoSize.displayWidth) * 100),
      y2: Math.round((clampedY2 / videoSize.displayHeight) * 100)
    };

    return coords;
  }
  return null;
};

// 添加 captureFirstFrame 函数
const captureFirstFrame = async (
  video: CustomVideoElement,
  canvas: HTMLCanvasElement,
  cameraIndex: 1 | 2
) => {
  const context = canvas.getContext("2d");
  if (!context) return;

  // 保存视频原始尺寸
  const videoWidth = video.videoWidth;
  const videoHeight = video.videoHeight;
  const canvasWidth = canvas.width;
  const canvasHeight = canvas.height;

  if (
    video.readyState >= video.HAVE_CURRENT_DATA &&
    videoWidth &&
    videoHeight
  ) {
    // 计算保持宽高比的绘制尺寸和位置
    let drawWidth = canvasWidth;
    let drawHeight = canvasHeight;
    let drawX = 0;
    let drawY = 0;

    const videoRatio = videoWidth / videoHeight;
    const canvasRatio = canvasWidth / canvasHeight;

    if (videoRatio > canvasRatio) {
      drawHeight = canvasWidth / videoRatio;
      drawY = (canvasHeight - drawHeight) / 2;
    } else {
      drawWidth = canvasHeight * videoRatio;
      drawX = (canvasWidth - drawWidth) / 2;
    }

    // 保存尺寸信息
    videoSizes.value[cameraIndex] = {
      width: videoWidth,
      height: videoHeight,
      displayWidth: drawWidth,
      displayHeight: drawHeight
    };

    // 如果已经存在 fabric 实例，先销毁
    if (fabricCanvasMap.get(cameraIndex)) {
      fabricCanvasMap.get(cameraIndex)?.dispose();
    }

    // 创建临时 canvas 用于绘制视频帧
    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = canvasWidth;
    tempCanvas.height = canvasHeight;
    const tempContext = tempCanvas.getContext("2d");

    if (tempContext) {
      // 在临时 canvas 上绘制视频帧
      tempContext.fillStyle = "#000";
      tempContext.fillRect(0, 0, canvasWidth, canvasHeight);

      try {
        tempContext.drawImage(video, drawX, drawY, drawWidth, drawHeight);
      } catch (error) {
        console.error("Error drawing video frame:", error);
        return;
      }

      // 创建新的 fabric.Canvas 实例
      const fabricCanvas = new fabric.Canvas(canvas, {
        selection: false,
        preserveObjectStacking: true,
        backgroundColor: "#000"
      });

      // 将临时 canvas 的内容转换为图片
      const imageUrl = tempCanvas.toDataURL("image/jpeg", 1.0);

      // 使用临时 canvas 的内容作为背景
      fabric.Image.fromURL(imageUrl, img => {
        if (!img) {
          console.error("Failed to create fabric image");
          return;
        }

        fabricCanvas.setBackgroundImage(
          img,
          fabricCanvas.renderAll.bind(fabricCanvas),
          {
            scaleX: 1,
            scaleY: 1,
            originX: "left",
            originY: "top"
          }
        );

        // 在背景图片设置完成后恢复标注
        setTimeout(() => {
          if (cameraIndex === 1) {
            if (form.value.clipRect1?.length === 4) {
              restoreRect(1, form.value.clipRect1);
            }
            if (form.value.line1?.length === 4) {
              restoreLine(1, form.value.line1);
            }
          } else {
            if (form.value.clipRect2?.length === 4) {
              restoreRect(2, form.value.clipRect2);
            }
            if (form.value.line2?.length === 4) {
              restoreLine(2, form.value.line2);
            }
          }
        }, 100);
      });

      // 存储 fabric.Canvas 实例
      fabricCanvasMap.set(cameraIndex, fabricCanvas);

      // 监听所有可能改变对象状态的事件
      fabricCanvas.on({
        "object:modified": () => {
          updateAnnotationData(cameraIndex, fabricCanvas);
        },
        "object:moving": () => {
          updateAnnotationData(cameraIndex, fabricCanvas);
        },
        "object:scaling": () => {
          updateAnnotationData(cameraIndex, fabricCanvas);
        },
        "object:rotating": () => {
          updateAnnotationData(cameraIndex, fabricCanvas);
        }
      });

      // 添加边界限制
      fabricCanvas.on("object:moving", e => {
        const obj = e.target;
        if (!obj) return;

        // 获取对象的边界
        const bound = 0;
        const objBounds = obj.getBoundingRect();

        // 限制对象不超出画布范围
        if (objBounds.left < bound) {
          obj.left! += bound - objBounds.left;
        }
        if (objBounds.top < bound) {
          obj.top! += bound - objBounds.top;
        }
        if (objBounds.left + objBounds.width > fabricCanvas.width!) {
          obj.left! -= objBounds.left + objBounds.width - fabricCanvas.width!;
        }
        if (objBounds.top + objBounds.height > fabricCanvas.height!) {
          obj.top! -= objBounds.top + objBounds.height - fabricCanvas.height!;
        }
      });

      video.pause();
      if (video.flvPlayer) {
        video.flvPlayer.destroy();
      }

      // 在成功捕获第一帧后立即销毁播放器
      await new Promise<void>(resolve => {
        // 确保图像已经绘制完成
        setTimeout(() => {
          destroyVideoPlayer(video);
          resolve();
        }, 100);
      });
    }
  } else {
    // 修改检查逻辑，添加超时处理
    let attempts = 0;
    const maxAttempts = 50; // 5秒超时 (50 * 100ms)

    const checkVideo = () => {
      attempts++;
      if (attempts > maxAttempts) {
        console.error("Video loading timeout");
        destroyVideoPlayer(video);
        loadingStates.value[cameraIndex] = false;
        ElMessage.error("视频加载超时，请重试");
        return;
      }

      if (
        video.readyState >= video.HAVE_CURRENT_DATA &&
        video.videoWidth &&
        video.videoHeight
      ) {
        captureFirstFrame(video, canvas, cameraIndex);
      } else {
        setTimeout(checkVideo, 100);
      }
    };
    setTimeout(checkVideo, 100);
  }
};

// 修改初始化播放器函数
const initFlvPlayer = async (
  url: string,
  video: CustomVideoElement,
  canvas: HTMLCanvasElement,
  cameraIndex: 1 | 2
) => {
  loadingStates.value[cameraIndex] = true;

  try {
    if (flvjs.isSupported()) {
      const flvPlayer = flvjs.createPlayer({
        type: "flv",
        url: url,
        isLive: true,
        hasAudio: false
      });

      flvPlayer.attachMediaElement(video);

      flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail) => {
        console.error("FLV Player Error:", errorType, errorDetail);
        destroyVideoPlayer(video);
        ElMessage.error("视频加载失败，请重试");
        loadingStates.value[cameraIndex] = false;
      });

      const handleLoadedData = () => {
        if (video.videoWidth && video.videoHeight) {
          captureFirstFrame(video, canvas, cameraIndex);
          video.removeEventListener("loadeddata", handleLoadedData);
          loadingStates.value[cameraIndex] = false;
        }
      };

      video.addEventListener("loadeddata", handleLoadedData);
      flvPlayer.load();

      try {
        await flvPlayer.play();
      } catch (err) {
        console.error("Error playing video:", err);
        destroyVideoPlayer(video);
      }

      video.flvPlayer = flvPlayer;
    }
  } catch (error) {
    console.error("Error initializing FLV player:", error);
    destroyVideoPlayer(video);
    ElMessage.error("初始化视频播放器失败");
    loadingStates.value[cameraIndex] = false;
  }
};

// 修改获取摄像头URL的函数
const fetchCameraUrls = async () => {
  try {
    if (!props.deviceInfo?.equipmentNumber) {
      console.warn("No equipment number available");
      return;
    }
    const res = await getCameraUrl(props.deviceInfo.equipmentNumber);
    if (res.code === 200 && res.data) {
      const cameras = res.data;
      if (cameras && cameras.length >= 2) {
        // 直接初始化两个视频流
        if (videoRef1.value && canvasRef1.value) {
          initFlvPlayer(
            cameras[0]?.streamKey,
            videoRef1.value,
            canvasRef1.value,
            1
          );
        }
        if (videoRef2.value && canvasRef2.value) {
          initFlvPlayer(
            cameras[1]?.streamKey,
            videoRef2.value,
            canvasRef2.value,
            2
          );
        }
      }
    }
  } catch (error) {
    console.error("Failed to fetch camera URLs:", error);
    ElMessage.error("获取摄像头地址失败");
  }
};

// 修改组件卸载时的清理逻辑
onBeforeUnmount(() => {
  // 清理视频资源
  if (videoRef1.value?.flvPlayer) {
    videoRef1.value.flvPlayer.destroy();
  }
  if (videoRef2.value?.flvPlayer) {
    videoRef2.value.flvPlayer.destroy();
  }

  // 清理 Canvas 实例
  fabricCanvasMap.forEach(canvas => {
    canvas.dispose();
  });
  fabricCanvasMap.clear();

  // 清理标注状态
  hasRect.value = {};
  hasLine.value = {};

  // 清理绘制状态
  [1, 2].forEach(cameraIndex => {
    cleanupDrawingState(cameraIndex as 1 | 2);
  });
});

// 修改刷新帧方法
const refreshFrame = async (cameraIndex: 1 | 2) => {
  loadingStates.value[cameraIndex] = true;

  try {
    const video = cameraIndex === 1 ? videoRef1.value : videoRef2.value;
    const canvas = cameraIndex === 1 ? canvasRef1.value : canvasRef2.value;
    const url =
      cameraIndex === 1 ? props.form.cameraUrl1 : props.form.cameraUrl2;

    if (!video || !canvas || !url) {
      throw new Error("Missing required elements");
    }

    // 清空标注数据
    hasRect.value[cameraIndex] = false;
    hasLine.value[cameraIndex] = false;

    // 清空画布上的所有对象
    const fabricCanvas = fabricCanvasMap.get(cameraIndex);
    if (fabricCanvas) {
      fabricCanvas.getObjects().forEach(obj => {
        fabricCanvas.remove(obj);
      });
      fabricCanvas.renderAll();
    }

    // 销毁现有的播放器
    if (video.flvPlayer) {
      try {
        const player = video.flvPlayer;
        video.flvPlayer = null;

        if (player.buffered && player.buffered.length) {
          player.pause();
        }
        player.unload();
        player.detachMediaElement();
        player.destroy();
      } catch (error) {
        console.warn("Warning while destroying player:", error);
      }
    }

    // 确保视频元素处于初始状态
    video.removeAttribute("src");
    video.load();

    await new Promise(resolve => setTimeout(resolve, 100));
    await initFlvPlayer(url, video, canvas, cameraIndex);
  } catch (error) {
    console.error("Error refreshing frame:", error);
    ElMessage.error("刷新帧失败，请重试");
    loadingStates.value[cameraIndex] = false;
  }
};

// 修改开始绘制矩形的函数
const startDrawRect = (cameraIndex: 1 | 2) => {
  if (hasRect.value[cameraIndex]) {
    ElMessage.warning("已存在矩形标注，请先删除");
    return;
  }

  const fabricCanvas = fabricCanvasMap.get(cameraIndex);
  if (!fabricCanvas) return;

  const rect = new fabric.Rect({
    left: fabricCanvas.width! / 4,
    top: fabricCanvas.height! / 4,
    width: fabricCanvas.width! / 2,
    height: fabricCanvas.height! / 2,
    fill: "#409eff61",
    selectable: true,
    hasControls: true,
    hasBorders: true,
    transparentCorners: false,
    cornerSize: 8,
    cornerStrokeColor: "white"
  });

  fabricCanvas.add(rect);
  fabricCanvas.setActiveObject(rect);
  hasRect.value[cameraIndex] = true;
  fabricCanvas.renderAll();

  // 更新标注数据到父组件
  updateAnnotationData(cameraIndex, fabricCanvas);
};

// 修改开始绘制线的函数
const startDrawLine = (cameraIndex: 1 | 2) => {
  if (hasLine.value[cameraIndex]) {
    ElMessage.warning("已存在线条标注，请先删除");
    return;
  }

  const fabricCanvas = fabricCanvasMap.get(cameraIndex);
  if (!fabricCanvas) return;

  // 启用绘制模式
  isDrawingLine.value[cameraIndex] = true;

  // 修改鼠标样式
  fabricCanvas.defaultCursor = "crosshair";

  // 添加点击和移动事件监听器
  const handleClick = (event: fabric.IEvent) =>
    handleCanvasClick(event, cameraIndex);
  const handleMove = (event: fabric.IEvent) =>
    handleMouseMove(event, cameraIndex);

  fabricCanvas.on("mouse:down", handleClick);
  fabricCanvas.on("mouse:move", handleMove);

  // 使用类型断言来保存事件处理函数引用
  (fabricCanvas as any).clickHandler = handleClick;
  (fabricCanvas as any).moveHandler = handleMove;

  ElMessage.info("请点击确定线段起点");
};

// 修改画布点击处理函数，使用百分比坐标
const handleCanvasClick = (event: fabric.IEvent, cameraIndex: 1 | 2) => {
  const fabricCanvas = fabricCanvasMap.get(cameraIndex);
  if (!fabricCanvas || !isDrawingLine.value[cameraIndex]) return;

  const pointer = fabricCanvas.getPointer(event.e);
  const videoSize = videoSizes.value[cameraIndex];
  if (!videoSize) return;

  // 计算显示区域的偏移
  const drawX = (fabricCanvas.width! - videoSize.displayWidth) / 2;
  const drawY = (fabricCanvas.height! - videoSize.displayHeight) / 2;

  // 计算相对于视频区域的坐标
  const relativeX = pointer.x - drawX;
  const relativeY = pointer.y - drawY;

  // 确保坐标在视频区域内
  const clampedX = Math.max(0, Math.min(relativeX, videoSize.displayWidth));
  const clampedY = Math.max(0, Math.min(relativeY, videoSize.displayHeight));

  // 计算百分比坐标
  const percentX = Math.round((clampedX / videoSize.displayWidth) * 100);
  const percentY = Math.round((clampedY / videoSize.displayHeight) * 100);

  // 如果是第一个点
  if (!firstPoint.value[cameraIndex]) {
    // 记录第一个点（使用百分比）
    firstPoint.value[cameraIndex] = {
      x: percentX,
      y: percentY
    };

    // 创建临时线段（使用实际坐标）
    tempLine.value[cameraIndex] = new fabric.Line(
      [pointer.x, pointer.y, pointer.x, pointer.y],
      {
        stroke: "#409eff",
        strokeWidth: 3,
        selectable: false
      }
    );

    fabricCanvas.add(tempLine.value[cameraIndex]);
    ElMessage.info("请点击确定线段终点");
  } else {
    // 第二个点 - 完成线段绘制（使用百分比坐标）
    const x1 = firstPoint.value[cameraIndex].x;
    const y1 = firstPoint.value[cameraIndex].y;
    const x2 = percentX;
    const y2 = percentY;

    // 转换百分比坐标为显示坐标
    const displayX1 = (x1 / 100) * videoSize.displayWidth + drawX;
    const displayY1 = (y1 / 100) * videoSize.displayHeight + drawY;
    const displayX2 = (x2 / 100) * videoSize.displayWidth + drawX;
    const displayY2 = (y2 / 100) * videoSize.displayHeight + drawY;

    const line = new fabric.Line([displayX1, displayY1, displayX2, displayY2], {
      stroke: "#409eff",
      strokeWidth: 3,
      selectable: true,
      hasControls: true,
      hasBorders: true,
      cornerSize: 8,
      cornerStrokeColor: "white",
      transparentCorners: false
    });

    // 设置控制点的可见性
    line.setControlsVisibility({
      mt: false,
      mb: false,
      ml: true,
      mr: true,
      mtr: true,
      bl: false,
      br: false,
      tl: false,
      tr: false
    });

    // 移除临时线段
    fabricCanvas.remove(tempLine.value[cameraIndex]);
    fabricCanvas.add(line);

    // 直接更新表单数据，使用百分比坐标
    const newForm = { ...props.form };
    if (cameraIndex === 1) {
      newForm.line1 = [x1, y1, x2, y2];
    } else {
      newForm.line2 = [x1, y1, x2, y2];
    }
    emit("updateForm", newForm);

    // 清理绘制状态
    cleanupDrawingState(cameraIndex);

    // 更新状态
    hasLine.value[cameraIndex] = true;
  }
};

// 修改鼠标移动处理函数，添加 cameraIndex 参数
const handleMouseMove = (event: fabric.IEvent, cameraIndex: 1 | 2) => {
  const fabricCanvas = fabricCanvasMap.get(cameraIndex);
  if (
    !fabricCanvas ||
    !isDrawingLine.value[cameraIndex] ||
    !tempLine.value[cameraIndex] ||
    !firstPoint.value[cameraIndex]
  )
    return;

  const pointer = fabricCanvas.getPointer(event.e);
  tempLine.value[cameraIndex].set({
    x2: pointer.x,
    y2: pointer.y
  });

  fabricCanvas.renderAll();
};

// 修改清理绘制状态的函数
const cleanupDrawingState = (cameraIndex: 1 | 2) => {
  const fabricCanvas = fabricCanvasMap.get(cameraIndex);
  if (!fabricCanvas) return;

  // 重置所有状态
  isDrawingLine.value[cameraIndex] = false;
  firstPoint.value[cameraIndex] = undefined;
  tempLine.value[cameraIndex] = undefined;

  // 使用类型断言来访问事件处理函数
  const canvas = fabricCanvas as any;
  if (canvas.clickHandler) {
    fabricCanvas.off("mouse:down", canvas.clickHandler);
  }
  if (canvas.moveHandler) {
    fabricCanvas.off("mouse:move", canvas.moveHandler);
  }

  // 恢复默认鼠标样式
  fabricCanvas.defaultCursor = "default";
};

// 修改删除选中形状的函数
const deleteSelectedShape = (cameraIndex: 1 | 2) => {
  const fabricCanvas = fabricCanvasMap.get(cameraIndex);
  if (!fabricCanvas) return;

  const activeObject = fabricCanvas.getActiveObject();
  if (!activeObject) {
    ElMessage.warning("请先选中要删除的标注");
    return;
  }

  // 根据对象类型重置对应的状态
  if (activeObject instanceof fabric.Rect) {
    hasRect.value[cameraIndex] = false;
  } else if (activeObject instanceof fabric.Line) {
    hasLine.value[cameraIndex] = false;
  }

  fabricCanvas.remove(activeObject);
  fabricCanvas.renderAll();

  // 清空对应的标注数据
  const newForm = { ...props.form };
  if (cameraIndex === 1) {
    if (activeObject instanceof fabric.Rect) {
      newForm.clipRect1 = [];
    } else if (activeObject instanceof fabric.Line) {
      newForm.line1 = [];
    }
  } else {
    if (activeObject instanceof fabric.Rect) {
      newForm.clipRect2 = [];
    } else if (activeObject instanceof fabric.Line) {
      newForm.line2 = [];
    }
  }
  emit("updateForm", newForm);
};

// 修改切换函数
const switchCameras = async () => {
  try {
    // 显示加载状态
    loadingStates.value[1] = true;
    loadingStates.value[2] = true;

    // 1. 保存当前的标注状态和对象
    const canvas1 = fabricCanvasMap.get(1);
    const canvas2 = fabricCanvasMap.get(2);
    const objects1: fabric.Object[] = [];
    const objects2: fabric.Object[] = [];

    // 保存并清除画布1的对象
    if (canvas1) {
      canvas1.getObjects().forEach(obj => {
        objects1.push(obj);
        canvas1.remove(obj);
      });
      canvas1.renderAll();
    }

    // 保存并清除画布2的对象
    if (canvas2) {
      canvas2.getObjects().forEach(obj => {
        objects2.push(obj);
        canvas2.remove(obj);
      });
      canvas2.renderAll();
    }

    // 2. 交换 URL - 通过 emit 更新
    const newForm = { ...props.form };
    const tempUrl = newForm.cameraUrl1;
    newForm.cameraUrl1 = newForm.cameraUrl2;
    newForm.cameraUrl2 = tempUrl;
    emit("updateForm", newForm);

    // 3. 交换标注状态
    const tempHasRect = hasRect.value[1];
    hasRect.value[1] = hasRect.value[2];
    hasRect.value[2] = tempHasRect;

    const tempHasLine = hasLine.value[1];
    hasLine.value[1] = hasLine.value[2];
    hasLine.value[2] = tempHasLine;

    // 4. 重新加载两个画布
    await Promise.all([refreshFrame(1), refreshFrame(2)]);

    // 5. 等待一小段时间确保画布已经初始化
    await new Promise(resolve => setTimeout(resolve, 500));

    // 6. 恢复标注对象到对方画布
    const newCanvas1 = fabricCanvasMap.get(1);
    const newCanvas2 = fabricCanvasMap.get(2);

    if (newCanvas1 && objects2.length > 0) {
      objects2.forEach(obj => {
        obj.clone((clonedObj: fabric.Object) => {
          newCanvas1.add(clonedObj);
          // 更新标注数据
          updateAnnotationData(1, newCanvas1);
        });
      });
      newCanvas1.renderAll();
    }

    if (newCanvas2 && objects1.length > 0) {
      objects1.forEach(obj => {
        obj.clone((clonedObj: fabric.Object) => {
          newCanvas2.add(clonedObj);
          // 更新标注数据
          updateAnnotationData(2, newCanvas2);
        });
      });
      newCanvas2.renderAll();
    }

    ElMessage.success("摄像头切换成功");
  } catch (error) {
    console.error("Error switching cameras:", error);
    ElMessage.error("摄像头切换失败，请重试");
  } finally {
    loadingStates.value[1] = false;
    loadingStates.value[2] = false;
  }
};

// 修改更新标注数据的函数
const updateAnnotationData = (cameraIndex: 1 | 2, canvas: fabric.Canvas) => {
  const newForm = { ...props.form };
  const objects = canvas.getObjects();

  objects.forEach(obj => {
    const coords = convertToOriginalCoords(obj, cameraIndex);
    if (!coords) return;

    if (obj instanceof fabric.Rect) {
      // 确保 coords 是 RectCoords 类型
      const rectCoords = coords as RectCoords;
      // 更新裁剪区域数据
      if (cameraIndex === 1) {
        newForm.clipRect1 = [
          rectCoords.x,
          rectCoords.y,
          rectCoords.width,
          rectCoords.height
        ];
      } else {
        newForm.clipRect2 = [
          rectCoords.x,
          rectCoords.y,
          rectCoords.width,
          rectCoords.height
        ];
      }
    } else if (obj instanceof fabric.Line) {
      // 确保 coords 是 LineCoords 类型
      const lineCoords = coords as LineCoords;
      // 更新线条数据
      if (cameraIndex === 1) {
        newForm.line1 = [
          lineCoords.x1,
          lineCoords.y1,
          lineCoords.x2,
          lineCoords.y2
        ];
      } else {
        newForm.line2 = [
          lineCoords.x1,
          lineCoords.y1,
          lineCoords.x2,
          lineCoords.y2
        ];
      }
    }
  });

  emit("updateForm", newForm);
};

// 修改恢复矩形的函数，从百分比还原
const restoreRect = (cameraIndex: 1 | 2, coords: number[]) => {
  const canvas = fabricCanvasMap.get(cameraIndex);
  if (!canvas) return;

  const videoSize = videoSizes.value[cameraIndex];
  if (!videoSize) return;

  // 计算显示区域的偏移
  const drawX = (canvas.width! - videoSize.displayWidth) / 2;
  const drawY = (canvas.height! - videoSize.displayHeight) / 2;

  // 从百分比转换为实际像素
  const displayX = (coords[0] / 100) * videoSize.displayWidth + drawX;
  const displayY = (coords[1] / 100) * videoSize.displayHeight + drawY;
  const displayWidth = (coords[2] / 100) * videoSize.displayWidth;
  const displayHeight = (coords[3] / 100) * videoSize.displayHeight;

  const rect = new fabric.Rect({
    left: displayX,
    top: displayY,
    width: displayWidth,
    height: displayHeight,
    fill: "#409eff61",
    selectable: true,
    hasControls: true,
    hasBorders: true,
    transparentCorners: false,
    cornerSize: 8,
    cornerStrokeColor: "white"
  });

  canvas.add(rect);
  hasRect.value[cameraIndex] = true;
  canvas.renderAll();
};

// 修改恢复线条的函数，使用与矩形相同的百分比逻辑
const restoreLine = (cameraIndex: 1 | 2, coords: number[]) => {
  const canvas = fabricCanvasMap.get(cameraIndex);
  if (!canvas) return;

  const videoSize = videoSizes.value[cameraIndex];
  if (!videoSize) return;

  // 计算显示区域的偏移
  const drawX = (canvas.width! - videoSize.displayWidth) / 2;
  const drawY = (canvas.height! - videoSize.displayHeight) / 2;

  // 从百分比转换为实际像素（与矩形相同的逻辑）
  const x1 = (coords[0] / 100) * videoSize.displayWidth + drawX;
  const y1 = (coords[1] / 100) * videoSize.displayHeight + drawY;
  const x2 = (coords[2] / 100) * videoSize.displayWidth + drawX;
  const y2 = (coords[3] / 100) * videoSize.displayHeight + drawY;

  // 创建线段
  const line = new fabric.Line([x1, y1, x2, y2], {
    stroke: "#409eff",
    strokeWidth: 3,
    selectable: true,
    hasControls: true,
    hasBorders: true,
    cornerSize: 8,
    cornerStrokeColor: "white",
    transparentCorners: false
  });

  // 设置控制点的可见性
  line.setControlsVisibility({
    mt: false,
    mb: false,
    ml: true,
    mr: true,
    mtr: true,
    bl: false,
    br: false,
    tl: false,
    tr: false
  });

  canvas.add(line);
  hasLine.value[cameraIndex] = true;
  canvas.renderAll();
};
</script>

<style lang="scss" scoped>
.device-config-dialog {
  :deep(.el-dialog__body) {
    max-height: 90vh;
    padding: 20px;
    overflow-y: auto;
  }
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center; // 确保垂直居中对齐
  justify-content: space-around;
  margin: 20px 0;

  .preview-item {
    display: flex;
    align-items: center;
    text-align: center;

    h4 {
      margin-bottom: 10px;
    }

    .right-container {
      display: flex;
      flex-direction: column;
      gap: 8px;
      align-items: baseline;
      margin-top: 10px;
    }

    .fabric-canvas {
      max-width: 100%; // 确保在小屏幕上也能完整显示
      height: auto; // 保持宽高比
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .video-info {
      margin: 8px 0;
      font-size: 12px;
      color: #666;
    }

    .canvas-container {
      position: relative;
      display: inline-block;

      .loading-overlay {
        position: absolute;
        inset: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #409eff;
        background: rgb(255 255 255 / 70%);

        .el-icon {
          margin-bottom: 8px;
          font-size: 24px;
        }
      }
    }
  }

  .switch {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 20px;
  }
}
</style>
