<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ref, computed, watch, getCurrentInstance, onMounted } from "vue";

import Dept from "@iconify-icons/ri/git-branch-line";
// import Reset from "@iconify-icons/ri/restart-line";
import More2Fill from "@iconify-icons/ri/more-2-fill";
import OfficeBuilding from "@iconify-icons/ep/office-building";
import LocationCompany from "@iconify-icons/ep/add-location";
import ExpandIcon from "@/assets/svg/expand.svg?component";
import UnExpandIcon from "@/assets/svg/unexpand.svg?component";
import AddFill from "@iconify-icons/ri/add-circle-line";
import { TreeNode } from "@/views/system/system-user/utils/types";
import { getAreaTree } from "@/api/system";
import { TreeNodeMenu } from "../system-user/utils/enum";
import { TreeNodeType } from "@/views/system/system-user/utils/enum";

interface Tree {
  id: number;
  name: string;
  highlight?: boolean;
  children?: Tree[];
}

const emit = defineEmits(["tree-select", "tree-contextmenu"]);

defineProps({
  treeLoading: Boolean,
  treeData: {
    type: Object as PropType<TreeNode[]>
  }
});

const treeRef = ref();
const isExpand = ref(true);
const searchValue = ref("");
const contextMenuData = ref();
const highlightMap = ref({});
const contextMenuVisible = ref(false);
const menuPosition = ref({ x: 0, y: 0 });
const { proxy } = getCurrentInstance();
const defaultProps = {
  children: "childList",
  label: "label"
};
const buttonClass = computed(() => {
  return [
    "!h-[20px]",
    "!text-sm",
    "reset-margin",
    "!text-[var(--el-text-color-regular)]",
    "dark:!text-white",
    "dark:hover:!text-primary"
  ];
});

const filterNode = (value: string, data: TreeNode) => {
  if (!value) return true;
  return data.label.includes(value);
};

function nodeClick(value) {
  const nodeId = value.$treeNodeId;
  highlightMap.value[nodeId] = highlightMap.value[nodeId]?.highlight
    ? Object.assign({ id: nodeId }, highlightMap.value[nodeId], {
        highlight: false
      })
    : Object.assign({ id: nodeId }, highlightMap.value[nodeId], {
        highlight: true
      });
  Object.values(highlightMap.value).forEach((v: Tree) => {
    if (v.id !== nodeId) {
      v.highlight = false;
    }
  });
  emit(
    "tree-select",
    highlightMap.value[nodeId]?.highlight
      ? Object.assign({ ...value, selected: true })
      : Object.assign({ ...value, selected: false })
  );
}

function toggleRowExpansionAll(status) {
  isExpand.value = status;
  const nodes = (proxy.$refs["treeRef"] as any).store._getAllNodes();
  for (let i = 0; i < nodes.length; i++) {
    nodes[i].expanded = status;
  }
}

function onTreeReset() {
  highlightMap.value = {};
  searchValue.value = "";
  toggleRowExpansionAll(true);
}

function addTopNode() {
  emit(
    "tree-contextmenu",
    TreeNodeMenu.find(item => item.cmd === "ADD_NODE"),
    null
  );
}

/** 菜单点击事件 */
const menuClickHandle = item => {
  emit("tree-contextmenu", item, contextMenuData.value);
};

/** 点击按钮事件 */
const openMenu = (nodeData, event: MouseEvent) => {
  contextMenuData.value = nodeData;
  menuPosition.value = { x: event.pageX, y: event.pageY };
  contextMenuVisible.value = true;
};

watch(searchValue, val => {
  treeRef.value!.filter(val);
});

defineExpose({ onTreeReset });
</script>

<template>
  <div
    v-loading="treeLoading"
    class="h-full bg-bg_color overflow-hidden relative"
  >
    <!-- 右键菜单 -->
    <el-popover
      ref="popover"
      v-model:visible="contextMenuVisible"
      placement="right"
      width="160"
      :showArrow="false"
      :popper-style="{
        left: `${menuPosition.x}px`,
        top: `${menuPosition.y}px`,
        position: 'absolute',
        padding: '5px 0'
      }"
    >
      <ul class="contextmenu">
        <div
          v-for="(item, key) in TreeNodeMenu"
          :key="key"
          style="display: flex; align-items: center"
        >
          <li
            v-if="
              !(
                contextMenuData &&
                item.cmd === 'ADD_NODE' &&
                contextMenuData.level === TreeNodeType.SITE
              )
            "
            @click="menuClickHandle(item)"
          >
            <IconifyIconOffline :icon="item.icon" />
            {{ item.name }}
          </li>
        </div>
      </ul>
    </el-popover>
    <div class="flex items-center h-[34px]">
      <el-input
        v-model="searchValue"
        class="ml-2"
        size="small"
        placeholder="请输入区域关键字"
        clearable
      >
        <template #suffix>
          <el-icon class="el-input__icon">
            <IconifyIconOffline
              v-show="searchValue.length === 0"
              icon="ri:search-line"
            />
          </el-icon>
        </template>
      </el-input>
      <el-dropdown :hide-on-click="false">
        <IconifyIconOffline
          class="w-[28px] cursor-pointer"
          width="18px"
          :icon="More2Fill"
        />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <el-button
                :class="buttonClass"
                link
                type="primary"
                :icon="useRenderIcon(isExpand ? ExpandIcon : UnExpandIcon)"
                @click="toggleRowExpansionAll(isExpand ? false : true)"
              >
                {{ isExpand ? "折叠全部" : "展开全部" }}
              </el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button
                :class="buttonClass"
                link
                type="primary"
                :icon="useRenderIcon(AddFill)"
                @click="addTopNode"
              >
                新增节点
              </el-button>
            </el-dropdown-item>
            <!-- <el-dropdown-item>
              <el-button
                :class="buttonClass"
                link
                type="primary"
                :icon="useRenderIcon(Reset)"
                @click="onTreeReset"
              >
                重置状态
              </el-button>
            </el-dropdown-item> -->
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-divider />
    <el-scrollbar height="calc(90vh - 88px)">
      <el-tree
        ref="treeRef"
        :data="treeData"
        node-key="id"
        size="small"
        :default-expand-all="true"
        :props="defaultProps"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @node-click="nodeClick"
      >
        <template #default="{ node, data }">
          <div
            class="!truncate"
            :class="[
              'rounded',
              'flex',
              'items-center',
              'select-none',
              'hover:text-primary',

              searchValue.trim().length > 0 &&
                node.label.includes(searchValue) &&
                'text-red-500',
              highlightMap[node.id]?.highlight ? 'dark:text-primary' : ''
            ]"
            :style="{
              color: highlightMap[node.id]?.highlight
                ? 'var(--el-color-primary)'
                : '',
              background: highlightMap[node.id]?.highlight
                ? 'var(--el-color-primary-light-7)'
                : 'transparent'
            }"
            @contextmenu.prevent="openMenu(data, $event)"
          >
            <IconifyIconOffline
              :icon="
                data.type === 1
                  ? OfficeBuilding
                  : data.type === 2
                    ? LocationCompany
                    : Dept
              "
            />
            <span class="!truncate f-1" :title="node.label">
              {{ node.label }}
            </span>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-divider) {
  margin: 0;
}

:deep(.el-tree) {
  --el-tree-node-hover-bg-color: transparent;
}

.contextmenu {
  margin: 0;
  font-size: 13px;
  font-weight: normal;
  white-space: nowrap;
  list-style-type: none;
  background: #fff;
  outline: 0;

  li {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 7px 12px;
    margin: 0;
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary);
    }

    svg {
      display: block;
      margin-right: 0.5em;
    }
  }
}
</style>
