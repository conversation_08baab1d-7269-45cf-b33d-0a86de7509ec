<template>
  <div class="device-management-container flex">
    <Tree
      ref="treeRef"
      class="tree-panel mr-4"
      :treeData="treeData"
      :treeLoading="treeLoading"
      @tree-select="onTreeSelect"
      @tree-contextmenu="onTreeContextMenu"
    />
    <div class="content-panel flex-1">
      <el-form
        ref="filterRef"
        :inline="true"
        :model="filterParams"
        class="search-form mb-4"
      >
        <el-form-item label="设备IP：" prop="ip">
          <el-input
            v-model="filterParams.ip"
            placeholder="请输入设备IP"
            clearable
            class="input-width"
          />
        </el-form-item>
        <el-form-item label="设备名称：" prop="deviceName">
          <el-input
            v-model="filterParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            class="input-width"
          />
        </el-form-item>
        <el-form-item label="设备编号：" prop="equipmentNumber">
          <el-input
            v-model="filterParams.equipmentNumber"
            placeholder="请输入设备编号"
            clearable
            class="input-width"
          />
        </el-form-item>
        <el-form-item label="维护人员电话：" prop="maintainerPhone">
          <el-input
            v-model="filterParams.maintainerPhone"
            placeholder="请输入维护人员电话"
            clearable
            class="input-width"
          />
        </el-form-item>
        <!-- <el-form-item label="经度：" prop="longitude">
          <el-input
            v-model="filterParams.longitude"
            placeholder="请输入经度"
            clearable
            class="input-width"
          />
        </el-form-item>
        <el-form-item label="纬度：" prop="latitude">
          <el-input
            v-model="filterParams.latitude"
            placeholder="请输入纬度"
            clearable
            class="input-width"
          />
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            @click="resetFilterForm(filterRef)"
          >
            重置
          </el-button>
          <el-button
            v-if="selectedTreeNode"
            v-permission="'/device/list/device/addDevice'"
            type="primary"
            :icon="useRenderIcon(AddFill)"
            @click="openDialog('add')"
          >
            新增设备
          </el-button>
        </el-form-item>
      </el-form>
      <pure-table
        ref="tableRef"
        row-key="id"
        adaptive
        :adaptiveConfig="{ offsetBottom: 108 }"
        align-whole="center"
        table-layout="auto"
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="{ ...pagination }"
        :header-cell-style="{
          background: 'var(--el-fill-color-light)',
          color: 'var(--el-text-color-primary)'
        }"
        @selection-change="handleSelectionChange"
        @page-size-change="handleSizeChange"
        @page-current-change="handleCurrentChange"
      >
        <template #operation="{ row }">
          <el-button
            v-permission="'/device/list/device/updateDevice'"
            link
            type="primary"
            :icon="useRenderIcon(EditPen)"
            @click="openDialog('edit', row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            style="margin-left: 5px"
            :icon="useRenderIcon(VideoPlay)"
            @click="handleViewVideo(row)"
          >
            查看视频
          </el-button>
          <el-popconfirm
            title="是否确认删除此设备?"
            style="margin-left: 5px"
            @confirm="handleDelete(row)"
          >
            <template #reference>
              <el-button
                v-permission="'/device/list/device/deleteDevice'"
                text
                style="margin-left: -5px"
                type="danger"
                :icon="useRenderIcon(Delete)"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>

    <!-- 新增/编辑设备对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form
        ref="formRef"
        :model="currentDevice.value"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="设备位置">
          <el-input :value="getLocationDesc()" disabled />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="currentDevice.value.deviceName" />
        </el-form-item>
        <el-form-item label="设备编号" prop="equipmentNumber">
          <el-input v-model="currentDevice.value.equipmentNumber" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select
            v-model="currentDevice.value.deviceType"
            placeholder="请选择设备类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in deviceTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="currentDevice.value.ip" />
        </el-form-item>
        <el-form-item
          v-if="currentDevice.value.deviceType === '摄像机'"
          label="流密钥"
        >
          <el-input
            v-model="currentDevice.value.streamKey"
            placeholder="视频流地址将根据设备编号和IP自动生成"
            readonly
          />
        </el-form-item>
        <el-form-item label="经度">
          <el-input v-model="currentDevice.value.longitude" />
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="currentDevice.value.latitude" />
        </el-form-item>
        <el-form-item label="维护电话" prop="maintainerPhone">
          <el-input
            v-model="currentDevice.value.maintainerPhone"
            placeholder="请输入维护人员电话"
          />
        </el-form-item>
        <el-form-item v-if="currentDevice.value.deviceType === '电脑'">
          <el-button type="primary" @click="openConfigDialog">
            修改配置
          </el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 设备配置对话框 -->
    <device-config-dialog
      v-if="configDialogVisible"
      v-model:visible="configDialogVisible"
      :config="currentDevice.value"
      :device-info="currentDevice.value"
      :device-type="currentDevice.value.deviceType"
      @update:config="updateDeviceConfig"
    />

    <!-- 添加视频弹窗组件 -->
    <video-dialog
      v-model:visible="videoDialogVisible"
      :stream-key="currentDevice.value.streamKey"
      :device-id="currentDevice.value.equipmentNumber"
    />
  </div>
</template>

<script setup lang="ts">
import { addDevice, updateDevice } from "@/api/device";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { handleFormField } from "@/utils/tree";
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import Refresh from "@iconify-icons/ep/refresh";
import VideoPlay from "@iconify-icons/ep/video-play";
import AddFill from "@iconify-icons/ri/add-circle-line";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { reactive, ref, computed, watch } from "vue";
import DeviceConfigDialog from "./components/DeviceConfigDialog.vue";
import VideoDialog from "./components/VideoDialog.vue";
import Tree from "./tree.vue";
import { useDevice } from "./utils/hooks";
import type { DeviceItem } from "./utils/types";

const treeRef = ref();
const filterRef = ref();
const tableRef = ref();
const dialogVisible = ref(false);
const dialogTitle = ref("新增设备");
const configDialogVisible = ref(false);
const videoDialogVisible = ref(false);

const currentDevice = reactive<{ value: DeviceItem }>({
  value: {
    id: 0,
    deviceName: "",
    equipmentNumber: "",
    ip: "",
    deviceType: "",
    streamKey: "",
    status: 0,
    updateTime: "",
    createTime: "",
    maintainerPhone: "",
    city: "",
    county: "",
    township: "",
    hamlet: "",
    site: "",
    longitude: "",
    latitude: "",
    devMode: false,
    addressText: "",
    cameraUrl1: "",
    cameraUrl2: "",
    clipRect1: [0, 0, 1280, 720],
    clipRect2: [0, 0, 1280, 720],
    line1: [300, 785, 1812, 836],
    line2: [1562, 811, 15, 788],
    nohelmetThreshold: 0.5,
    weatherShieldThreshold: 0.7,
    persuadTimeDuration: 10,
    advicerFaceRecogDuration: 2,
    faceRecogDistanceThreshold: 0.3,
    loadParamInterval: 3600,
    loadFaceDatabaseInterval: 3600,
    bikeTrackidLife: 30,
    saveViolateVideo: false,
    startEndTime: 0,
    videoFps: 15,
    announceTimes: JSON.stringify({}),
    maxAnnounceTimes: 1,
    announceTimeout: 3,
    announceVolume: JSON.stringify({}),
    unmuteStart: 8,
    unmuteEnd: 21,
    bikePlatesQuantity: 4,
    retryIntervals: [],
    onlineStatusInterval: 5,
    backwardPriority: false,
    modelFile: JSON.stringify({
      helmetUrl: "",
      helmetVersion: 0,
      plateUrl: "",
      plateVersion: 0
    })
  }
});

const {
  selectedTreeNode,
  filterParams,
  loading,
  dataList,
  columns,
  pagination,
  onSearch,
  resetFilterForm,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  handleDelete,
  treeData,
  treeLoading,
  onTreeSelect,
  onTreeContextMenu
} = useDevice();

// IP地址校验正则
const ipRegex =
  /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

// 表单校验规则
const rules = {
  deviceName: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
  equipmentNumber: [
    { required: true, message: "请输入设备编号", trigger: "blur" }
  ],
  deviceType: [
    { required: true, message: "请选择设备类型", trigger: "change" }
  ],
  ip: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
        } else if (!ipRegex.test(value)) {
          callback(new Error("请输入正确的IP地址"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  maintainerPhone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ]
};

// 设备类型选项
const deviceTypeOptions = [
  { label: "摄像机", value: "摄像机" },
  { label: "电脑", value: "电脑" }
];

const formRef = ref<FormInstance>();

// 添加深拷贝函数
const deepClone = <T,>(obj: T): T => {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch (e) {
    console.error("Deep clone failed:", e);
    return obj;
  }
};

function openDialog(mode: string, row?: DeviceItem) {
  if (mode === "edit" && row) {
    dialogTitle.value = "编辑设备";
    Object.assign(currentDevice.value, row);
  } else {
    dialogTitle.value = "新增设备";
    Object.assign(currentDevice.value, {
      ...handleFormField(selectedTreeNode.value, treeData.value, "label"),
      deviceName: "",
      equipmentNumber: "",
      ip: "",
      deviceType: "",
      streamKey: "",
      status: 0,
      longitude: "",
      latitude: "",
      devMode: false,
      addressText: "",
      cameraUrl1: "",
      cameraUrl2: "",
      clipRect1: [0, 0, 1280, 720],
      clipRect2: [0, 0, 1280, 720],
      line1: [300, 785, 1812, 836],
      line2: [1562, 811, 15, 788],
      nohelmetThreshold: 0.5,
      weatherShieldThreshold: 0.7,
      persuadTimeDuration: 10,
      advicerFaceRecogDuration: 2,
      faceRecogDistanceThreshold: 0.3,
      loadParamInterval: 3600,
      loadFaceDatabaseInterval: 3600,
      bikeTrackidLife: 30,
      saveViolateVideo: false,
      startEndTime: 0,
      videoFps: 15,
      announceTimes: JSON.stringify({}),
      maxAnnounceTimes: 1,
      announceTimeout: 3,
      announceVolume: JSON.stringify({}),
      unmuteStart: 8,
      unmuteEnd: 21,
      bikePlatesQuantity: 4,
      retryIntervals: [],
      onlineStatusInterval: 5,
      backwardPriority: false,
      modelFile: JSON.stringify({
        helmetUrl: "",
        helmetVersion: 0,
        plateUrl: "",
        plateVersion: 0
      }),
      maintainerPhone: ""
    });
  }
  dialogVisible.value = true;
}

function getLocationDesc() {
  const locationObj = handleFormField(
    selectedTreeNode.value,
    treeData.value,
    "label"
  );
  return Object.keys(locationObj)
    .map(key => locationObj[key])
    .reverse()
    .join("-");
}

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 如果设备类型不是摄像机，清空 streamKey
    if (currentDevice.value.deviceType !== "摄像机") {
      currentDevice.value.streamKey = "";
    }

    if (dialogTitle.value === "新增设备") {
      addDevice(currentDevice.value)
        .then(res => {
          if (res.code === 200) {
            ElMessage.success("新增设备成功");
            onSearch();
            dialogVisible.value = false;
          }
        })
        .catch(() => {
          ElMessage.error("新增设备失败");
        });
    } else {
      updateDevice(currentDevice.value)
        .then(res => {
          if (res.code === 200) {
            ElMessage.success("编辑设备成功");
            onSearch();
            dialogVisible.value = false;
          }
        })
        .catch(() => {
          ElMessage.error("编辑设备失败");
        });
    }
  } catch (error) {
    console.error("表单验证失败:", error);
    return false;
  }
};

function handleViewVideo(row: DeviceItem) {
  if (row.deviceType !== "摄像机") {
    ElMessage.warning("只有摄像机设备可以查看视频");
    return;
  }
  if (!row.streamKey) {
    ElMessage.warning("该设备未配置视频流地址，无法查看视频");
    return;
  }
  Object.assign(currentDevice.value, row);
  videoDialogVisible.value = true;
}

function openConfigDialog() {
  if (currentDevice.value.deviceType !== "电脑") {
    ElMessage.warning("只有电脑设备可以配置");
    return;
  }
  configDialogVisible.value = true;
}

const updateDeviceConfig = (newConfig: DeviceItem) => {
  Object.assign(currentDevice.value, deepClone(newConfig));
};

// 添加流密钥生成的计算属性
const generateStreamKey = computed(() => {
  if (!currentDevice.value.equipmentNumber || !currentDevice.value.ip)
    return "";

  // 处理设备编号：去掉特殊符号
  const deviceNumber = currentDevice.value.equipmentNumber.replace(
    /[^a-zA-Z0-9]/g,
    ""
  );
  // 处理IP：去掉点号
  const ip = currentDevice.value.ip.replace(/\./g, "");

  // 返回完整的视频流地址
  return `https://countryside.yibindaoan.com/live/${deviceNumber}${ip}.flv`;
});

// 监听设备编号和IP的变化
watch(
  () => [currentDevice.value.equipmentNumber, currentDevice.value.ip],
  () => {
    if (currentDevice.value.deviceType === "摄像机") {
      currentDevice.value.streamKey = generateStreamKey.value;
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.device-management-container {
  display: flex;
  height: calc(100vh - 84px);
}

.pure-table {
  flex: 1;
}

.tree-panel {
  width: 300px;
  min-width: 300px;
  border-right: 1px solid var(--el-border-color-light);
}

.content-panel {
  padding: 16px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 5px;
}

.input-width {
  width: 180px;
}
</style>
