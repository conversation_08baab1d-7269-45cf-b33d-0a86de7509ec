import { getWarningList } from "@/api/warning"; // 假设你有这个 API
import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { useRoute } from "vue-router";

interface FilterParams {
  alarmType?: string; // 报警类型
  dateRange?: [string, string]; // 时间范围
}

interface WarningItem {
  id: number;
  alarmType: string; // 报警类型
  eventTime: string; // 事件时间
  content: string; // 内容
  targetType: string; // 目标类型
}

interface TableColumn {
  label: string;
  prop: string;
  minWidth?: number;
  width?: number;
  align?: "left" | "center" | "right";
  formatter?: (row: any) => string;
}

type TableColumnList = TableColumn[];

export function useWarning() {
  const filterParams = reactive<FilterParams>({});
  const dataList = ref<WarningItem[]>([]);
  const loading = ref(false);
  const route = useRoute();

  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    align: "center"
  });

  const columns: TableColumnList = [
    {
      label: "内容",
      prop: "content",
      minWidth: 180
    },
    {
      label: "报警类型",
      prop: "alarmType",
      minWidth: 120
    },
    {
      label: "报警对象类型",
      prop: "targetType",
      minWidth: 120
    },
    {
      label: "报警时间",
      prop: "eventTime",
      minWidth: 160,
      formatter: ({ eventTime }) =>
        dayjs(eventTime).format("YYYY-MM-DD HH:mm:ss")
    }
  ];

  onMounted(() => {
    Object.keys(route.query).forEach(key => {
      filterParams[key] = route.query[key];
    });
    onSearch();
  });

  const onSearch = () => {
    loading.value = true;
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      alarmType: filterParams.alarmType || "",
      startTime: filterParams.dateRange?.[0]
        ? `${filterParams.dateRange[0]} 00:00:00`
        : undefined,
      endTime: filterParams.dateRange?.[1]
        ? `${filterParams.dateRange[1]} 23:59:59`
        : undefined
    };

    getWarningList(params)
      .then(res => {
        if (res.code === 200) {
          dataList.value = res.data.records;
          pagination.total = res.data.total;
        } else {
          dataList.value = [];
        }
      })
      .catch(() => {
        dataList.value = [];
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    pagination.currentPage = 1;
    onSearch();
  };

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  return {
    loading,
    filterParams,
    dataList,
    pagination,
    columns,
    onSearch,
    resetFilterForm,
    handleSizeChange,
    handleCurrentChange
  };
}
