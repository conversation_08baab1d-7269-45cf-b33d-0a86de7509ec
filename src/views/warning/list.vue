<script setup lang="ts">
import { ref } from "vue";
import { useWarning } from "./utils/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";

defineOptions({
  name: "WarningList"
});

const filterRef = ref();
const {
  filterParams,
  loading,
  dataList,
  columns,
  pagination,
  onSearch,
  resetFilterForm,
  handleSizeChange,
  handleCurrentChange
} = useWarning();
</script>

<template>
  <div>
    <el-form
      ref="filterRef"
      :inline="true"
      :model="filterParams"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="报警类型：" prop="alarmType">
        <el-input
          v-model="filterParams.alarmType"
          placeholder="请输入报警类型"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="filterParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          class="!w-[380px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon(Refresh)"
          @click="resetFilterForm(filterRef)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar title="报警列表" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{
            total: pagination.total,
            pageSize: pagination.pageSize,
            currentPage: pagination.currentPage,
            background: true,
            align: 'center'
          }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        />
      </template>
    </PureTableBar>
  </div>
</template>
