<template>
  <el-card shadow="hover">
    <template #header>
      <div class="card-header">
        <span>违法类型统计</span>
        <div class="filter-row" style="display: flex; align-items: center">
          <!-- <div class="range-picker-wrapper" style="flex: 1">
            <el-date-picker
              v-model="selectedRange"
              type="datetimerange"
              format="YYYY-MM-DD HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              @change="onRangeChange"
            />
          </div> -->
        </div>
      </div>
    </template>
    <pure-table
      row-key="illegalType"
      :loading="loading"
      :data="data"
      :columns="columns"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-regular)'
      }"
      :cell-style="{
        padding: '8px'
      }"
      class="statistics-table"
    />
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { StatisticsData, TableColumnList } from "../utils/type";

const props = defineProps<{
  loading: boolean;
  data: StatisticsData[];
  columns: TableColumnList;
  onRangeChange: (range: [string, string]) => void;
}>();
const selectedRange = ref<[string, string] | null>(null);

const onRangeChange = (range: [string, string]) => {
  if (range) {
    const [startTime, endTime] = range;
    props.onRangeChange([startTime, endTime]);
  }
};

// 添加日志以检查数据更新
watch(
  () => props.data,
  newData => {}
);
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filter-row {
  display: flex;
  align-items: center;
}

.range-picker-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.statistics-table {
  width: 100%;
  height: 400px;

  :deep(.el-table__body-wrapper) {
    height: calc(400px - 40px); // 减去表头高度
    overflow-y: hidden;
  }

  :deep(.el-table__header) {
    th {
      font-weight: 500;
      color: var(--el-text-color-regular);
      background: var(--el-fill-color-light);
    }
  }

  :deep(.el-table) {
    width: 100% !important;
  }

  :deep(.el-table__inner-wrapper) {
    width: 100% !important;
  }
}
</style>
