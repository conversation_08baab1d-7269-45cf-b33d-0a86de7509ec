<template>
  <el-card shadow="hover">
    <template #header>
      <div class="card-header">
        <span>违法类型分布趋势</span>
        <el-radio-group v-model="selectedView" @change="onViewChange">
          <el-radio-button label="day">今天</el-radio-button>
          <el-radio-button label="week">本周</el-radio-button>
          <el-radio-button label="month">本月</el-radio-button>
          <el-radio-button label="year">全年</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <div ref="chartRef" class="main-trend-chart" />
  </el-card>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import { onMounted, ref, watch } from "vue";

const props = defineProps<{
  onViewChange: (view: string) => void;
  trendData: {
    series: { data: number[]; name: string }[];
    categories: string[];
  };
}>();

const selectedView = ref("day");

const onViewChange = (view: string) => {
  props.onViewChange(view);
};

const chartRef = ref<HTMLElement>();

const updateChart = () => {
  if (chartRef.value) {
    const myChart = echarts.init(chartRef.value);
    myChart.clear();
    const option = {
      tooltip: {
        trigger: "axis",
        // 优化tooltip位置，防止被遮挡
        confine: true, // 限制tooltip在图表容器内
        backgroundColor: "rgba(0, 24, 75, 0.9)",
        borderColor: "rgba(64, 158, 255, 0.3)",
        borderWidth: 1,
        padding: [6, 8], // 减小内边距，让tooltip更紧凑
        textStyle: {
          color: "rgba(255, 255, 255, 0.9)",
          fontSize: 12 // 减小字体大小，让tooltip更小巧
        },
        position: function (point, params, dom, rect, size) {
          // 智能位置计算，防止tooltip超出边界
          const [mouseX, mouseY] = point;
          const { contentSize, viewSize } = size;
          const [tooltipWidth, tooltipHeight] = contentSize;
          const [containerWidth, containerHeight] = viewSize;

          let x = mouseX + 10; // 默认在鼠标右侧
          let y = mouseY - tooltipHeight / 2; // 垂直居中

          // 检查右边界，如果超出则显示在左侧
          if (x + tooltipWidth > containerWidth) {
            x = mouseX - tooltipWidth - 10;
          }

          // 检查上下边界
          if (y < 0) {
            y = 10;
          } else if (y + tooltipHeight > containerHeight) {
            y = containerHeight - tooltipHeight - 10;
          }

          return [x, y];
        }
      },
      legend: {
        data: props.trendData.series.map(item => item.name)
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: props.trendData.categories
      },
      yAxis: {
        type: "value",
        name: "违法数量(起)"
      },
      series: props.trendData.series.map(item => ({
        name: item.name,
        type: "line",
        smooth: true,
        data: item.data
      }))
    };
    myChart.setOption(option);

    window.addEventListener("resize", () => {
      myChart.resize();
    });
  }
};

// 监听 selectedView 和 trendData 的变化并更新图表
watch([selectedView, () => props.trendData], () => {
  updateChart();
});

onMounted(() => {
  updateChart();
});
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-trend-chart {
  width: 100%;
  height: 400px;
}
</style>
