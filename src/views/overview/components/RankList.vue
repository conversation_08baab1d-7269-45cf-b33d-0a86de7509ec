<template>
  <el-card shadow="hover" class="rank-card">
    <template #header>
      <div class="card-header">
        <span>高发地点TOP7</span>
        <el-radio-group v-model="selectedView" @change="onViewChange">
          <el-radio-button label="day">今天</el-radio-button>
          <el-radio-button label="week">本周</el-radio-button>
          <el-radio-button label="month">本月</el-radio-button>
          <el-radio-button label="year">全年</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <div class="rank-list">
      <div v-for="(item, index) in data" :key="index" class="rank-item">
        <div class="rank-info">
          <span :class="['rank-tag', getRankClass(index)]">{{
            index + 1
          }}</span>
          <span class="rank-name">{{ item.name }}</span>
        </div>
        <div class="rank-value">
          <el-progress
            :percentage="item.value"
            :color="getProgressColor(item.value)"
            :show-text="false"
          />
          <span class="progress-value">{{ item.value }}次</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { RankData } from "../utils/type";

const props = defineProps<{
  data: RankData[];
  getRankClass: (index: number) => string;
  getProgressColor: (value: number) => string;
  onViewChange: (view: string) => void;
}>();

const selectedView = ref("day");

const onViewChange = (view: string) => {
  props.onViewChange(view);
};
</script>

<style lang="scss" scoped>
.rank-card {
  height: 100%;

  :deep(.card-header) {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .rank-list {
    display: flex;
    flex-direction: column;
    height: 400px;
    padding: 8px 0;

    .rank-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 35px;

      .rank-info {
        display: flex;
        align-items: center;

        .rank-tag {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          font-size: 12px;
          line-height: 20px;
          color: var(--el-text-color-regular);
          text-align: center;
          background: #f5f7fa;
          border-radius: 2px;

          &.rank-top-1 {
            color: #fff;
            background: #314659;
          }

          &.rank-top-2 {
            color: #fff;
            background: #526079;
          }

          &.rank-top-3 {
            color: #fff;
            background: #738094;
          }
        }

        .rank-name {
          width: 160px;
          overflow: hidden;
          font-size: 14px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .rank-value {
        display: flex;
        flex: 1;
        align-items: center;
        margin-left: 12px;

        .el-progress {
          width: 100%;
          margin-right: 6px;
        }

        .progress-value {
          min-width: 50px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
          text-align: right;
        }
      }
    }
  }
}
</style>
