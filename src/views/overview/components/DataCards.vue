<template>
  <div class="data-cards">
    <el-row :gutter="16">
      <el-col v-for="(item, index) in data" :key="index" :span="6">
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
          <div class="card-content">
            <div class="card-left">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value">
                {{ item.value }}
                <span class="card-unit">{{ item.unit }}</span>
              </div>
              <div class="card-compare">
                <span class="compare-value">
                  <span :class="item.trend > 0 ? 'up' : 'down'">
                    {{ Math.abs(item.trend) }}%
                    <span v-if="item.trend > 0" class="arrow-up">↑</span>
                    <span v-else-if="item.trend < 0" class="arrow-down">↓</span>
                  </span>
                  <span class="compare-text">较上周</span>
                </span>
              </div>
            </div>
            <div class="card-right">
              <div class="card-trend-chart" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import type { CardData } from "../utils/type";
import { onMounted } from "vue";
import * as echarts from "echarts";

const props = defineProps<{
  data: CardData[];
}>();

onMounted(() => {
  // 初始化小趋势图
  const cardChartDoms =
    document.querySelectorAll<HTMLElement>(".card-trend-chart");
  cardChartDoms.forEach((dom, index) => {
    const myChart = echarts.init(dom);
    const option = {
      tooltip: {
        trigger: "axis",
        // 优化tooltip位置，防止被遮挡
        confine: true, // 限制tooltip在图表容器内
        backgroundColor: "rgba(0, 24, 75, 0.9)",
        borderColor: "rgba(64, 158, 255, 0.3)",
        borderWidth: 1,
        padding: [4, 6], // 小图表使用更小的内边距
        textStyle: {
          color: "rgba(255, 255, 255, 0.9)",
          fontSize: 11 // 小图表使用更小的字体
        },
        position: function (point, params, dom, rect, size) {
          // 智能位置计算，防止tooltip超出边界
          const [mouseX, mouseY] = point;
          const { contentSize, viewSize } = size;
          const [tooltipWidth, tooltipHeight] = contentSize;
          const [containerWidth, containerHeight] = viewSize;

          let x = mouseX + 10; // 默认在鼠标右侧
          let y = mouseY - tooltipHeight / 2; // 垂直居中

          // 检查右边界，如果超出则显示在左侧
          if (x + tooltipWidth > containerWidth) {
            x = mouseX - tooltipWidth - 10;
          }

          // 检查上下边界
          if (y < 0) {
            y = 10;
          } else if (y + tooltipHeight > containerHeight) {
            y = containerHeight - tooltipHeight - 10;
          }

          return [x, y];
        }
      },
      xAxis: {
        type: "category",
        show: false,
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
      },
      yAxis: {
        type: "value",
        show: false
      },
      grid: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      },
      series: [
        {
          data: [82, 93, 90, 93, 129, 133, 132],
          type: "line",
          smooth: true,
          showSymbol: false,
          itemStyle: {
            normal: {
              color: props.data[index].color,
              lineStyle: {
                color: props.data[index].color
              }
            }
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: `${props.data[index].color}33`
              },
              {
                offset: 1,
                color: `${props.data[index].color}00`
              }
            ])
          }
        }
      ]
    };
    myChart.setOption(option);
  });
});
</script>

<style lang="scss" scoped>
.data-cards {
  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;

    .card-left {
      flex: 1;
      margin-right: 16px;

      .card-title {
        margin-bottom: 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }

      .card-value {
        margin-bottom: 8px;
        font-size: 24px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .card-unit {
          margin-left: 4px;
          font-size: 14px;
          font-weight: normal;
          color: var(--el-text-color-secondary);
        }
      }

      .card-compare {
        font-size: 12px;
        color: var(--el-text-color-secondary);

        .compare-value {
          display: flex;
          align-items: center;

          .up {
            color: #f56c6c;
          }

          .down {
            color: #67c23a;
          }

          .arrow-up,
          .arrow-down {
            margin-left: 4px;
            font-size: 12px;
          }

          .compare-text {
            margin-left: 4px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }

    .card-right {
      width: 100px;
      height: 40px;

      .card-trend-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
