<template>
  <el-card shadow="hover" class="dynamic-card">
    <template #header>
      <div class="card-header">
        <span>最新动态</span>
      </div>
    </template>
    <div class="timeline-wrapper">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in data"
          :key="index"
          :timestamp="item.time"
        >
          {{ item.content }}
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import type { DynamicItem } from "../utils/type";

defineProps<{
  data: DynamicItem[];
}>();
</script>

<style lang="scss" scoped>
.dynamic-card {
  height: 100%;

  .timeline-wrapper {
    height: 400px;
    padding: 0 16px;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-lighter);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  :deep(.el-timeline) {
    padding-right: 8px;
  }

  :deep(.el-timeline-item__content) {
    font-size: 13px;
    color: var(--el-text-color-regular);
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  :deep(.el-timeline-item__node) {
    &.is-hollow {
      background: var(--el-bg-color);
    }
  }
}
</style>
