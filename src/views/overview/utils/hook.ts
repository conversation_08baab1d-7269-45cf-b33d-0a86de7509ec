import { onMounted, ref } from "vue";

import type { CardData, DynamicItem, RankData, StatisticsData } from "./type";

import { tableColumns } from "./data";

import dayjs from "dayjs";

import weekOfYear from "dayjs/plugin/weekOfYear";

import {
  getDeviceCount,
  getDeviceOnlineNum,
  getIllegalTrendByDay,
  getIllegalTrendByMonth,
  getIllegalTrendByWeek,
  getIllegalTrendByYear,
  getLatestNews,
  getProcessingRate,
  getTodayIllegalNum,
  getTopIllegalSiteByDay,
  getTopIllegalSiteByLastSeventDay,
  getTopIllegalSiteByMonth,
  getTopIllegalSiteByYear,
  getViolationCountsByDay,
  getViolationCountsByTimeRange
} from "@/api/screen";
import { useUserStoreHook } from "@/store/modules/user";

dayjs.extend(weekOfYear);

export function useOverview() {
  // 数据定义

  const timeRange = ref("week");

  const loading = ref(false);

  const tableData = ref<StatisticsData[]>([]);

  const trendData = ref<{
    series: { data: number[]; name: string }[];

    categories: string[];
  }>({
    series: [],

    categories: []
  });

  // 卡片数据

  const cardData = ref<CardData[]>([
    {
      title: "今日违法数",

      value: "0",

      unit: "起",

      trend: 0,

      color: "#F56C6C"
    },

    {
      title: "设备总数",

      value: "0",

      unit: "台",

      trend: 0,

      color: "#409EFF"
    },

    {
      title: "设备在线率",

      value: "0",

      unit: "%",

      trend: 0,

      color: "#67C23A"
    },

    {
      title: "处理率",

      value: "0",

      unit: "%",

      trend: 0,

      color: "#E6A23C"
    }
  ]);

  // 排名数据
  const rankData = ref<RankData[]>([]);

  // 动态列表数据
  const dynamicList = ref<DynamicItem[]>([]);

  // 获取排名样式

  const getRankClass = (index: number) => {
    if (index < 3) return `rank-top-${index + 1}`;

    return "rank-normal";
  };

  // 获取进度条颜色

  const getProgressColor = (value: number) => {
    if (value >= 90) return "#67C23A";

    if (value >= 80) return "#E6A23C";

    return "#F56C6C";
  };

  // 获取表格数据

  const getTableData = () => {
    loading.value = true;

    tableData.value = []; // 确保清空旧数据

    loading.value = false;
  };

  // 获取卡片数据

  const getCardData = async () => {
    const [
      todayIllegalNumResult,

      deviceCountResult,

      deviceOnlineNumResult,

      processingRateResult
    ] = await Promise.all([
      getTodayIllegalNum(), // 今日违法数量

      getDeviceCount(), // 设备总数

      getDeviceOnlineNum(), // 设备在线率

      getProcessingRate() // 处理率
    ]);

    try {
      cardData.value = [
        {
          ...cardData.value[0],

          value: todayIllegalNumResult.data.todayCount.toString(),

          trend: todayIllegalNumResult.data.increaseRate
        },

        {
          ...cardData.value[1],

          value: deviceCountResult.data.todayCount.toString(),

          trend: deviceCountResult.data.increaseRate
        },

        {
          ...cardData.value[2],

          value: deviceOnlineNumResult.data.todayOnlineCount.toString(),

          trend: deviceOnlineNumResult.data.increaseRate
        },

        {
          ...cardData.value[3],

          value: processingRateResult.data.currentRate.toString(),

          trend: processingRateResult.data.rateDifference
        }
      ];
    } catch (error) {
      console.error(error);
    }
  };

  // 获取排名数据

  const fetchRankData = async (view: string) => {
    try {
      let response;

      const today = dayjs();

      switch (view) {
        case "day":
          response = await getTopIllegalSiteByDay(
            today.year(),

            today.month() + 1,

            today.date()
          );

          break;

        case "week":
          response = await getTopIllegalSiteByLastSeventDay();

          break;

        case "month":
          response = await getTopIllegalSiteByMonth(
            today.year(),

            today.month() + 1
          );

          break;

        case "year":
          response = await getTopIllegalSiteByYear(today.year());

          break;

        default:
          response = await getTopIllegalSiteByDay(
            today.year(),

            today.month() + 1,

            today.date()
          );
      }

      rankData.value = response.data.map((item: any) => ({
        name: item.location,

        value: item.count
      }));
    } catch (error) {
      console.error("Failed to fetch rank data:", error);
    }
  };

  const fetchTrendData = async (view: string) => {
    try {
      let response;
      const today = dayjs();
      switch (view) {
        case "day":
          fetchTodayStatisticsData();
          response = await getIllegalTrendByDay(
            today.format("YYYY-MM-DD"),
            useUserStoreHook().getUserLocationPath
          );
          break;
        case "week":
          // 使用日期段获取数据
          const startDate = today
            .subtract(6, "day")
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss");
          const endDate = today.endOf("day").format("YYYY-MM-DD HH:mm:ss");
          fetchStatisticsByTimeRange([startDate, endDate]);
          response = await getIllegalTrendByWeek(
            startDate,
            endDate,
            useUserStoreHook().getUserLocationPath
          );
          break;
        case "month":
          response = await getIllegalTrendByMonth(
            today.year(),
            today.month() + 1,
            useUserStoreHook().getUserLocationPath
          );
          const startOfLastMonth = today
            .startOf("month")
            .format("YYYY-MM-DD HH:mm:ss");
          const endOfLastMonth = today
            .endOf("month")
            .format("YYYY-MM-DD HH:mm:ss");
          fetchStatisticsByTimeRange([startOfLastMonth, endOfLastMonth]);
          break;
        case "year":
          response = await getIllegalTrendByYear(
            today.year(),
            useUserStoreHook().getUserLocationPath
          );
          // 获取上一年此时的时间
          const startOfLastYear = today
            .subtract(1, "year")
            .startOf("year")
            .format("YYYY-MM-DD HH:mm:ss");

          // 获取当前时间
          const endOfNow = today.format("YYYY-MM-DD HH:mm:ss");
          fetchStatisticsByTimeRange([startOfLastYear, endOfNow]);
          break;
        default:
          response = await getIllegalTrendByDay(today.format("YYYY-MM-DD"));
      }
      trendData.value = {
        series: response.data.series,
        categories: response.data.categories
      };
    } catch (error) {
      console.error("Failed to fetch trend data:", error);
    }
  };

  /**
   * 查询档期当日违法类型统计数据
   */
  const fetchTodayStatisticsData = async () => {
    const date = dayjs().format("YYYY-MM-DD");
    try {
      tableData.value = []; // 清空旧数据
      const formattedDate = dayjs(date).format("YYYY-MM-DD");

      const response = await getViolationCountsByDay(
        formattedDate,
        useUserStoreHook().getUserLocationPath
      );

      const data = response.data.map((item: any) => ({
        id: item.id,
        illegalType: item.name,
        count: item.data,
        send: item.send,
        handled: item.handled,
        handleRate: item.handleRate + "%"
      }));

      tableData.value = data; // 更新为新数据
    } catch (error) {
      console.error("Failed to fetch statistics data:", error);
    }
  };

  /**
   * 根据时间范围查询违法类型统计
   * @param range
   */
  const fetchStatisticsByTimeRange = async (range: [string, string]) => {
    try {
      const [startTime, endTime] = range;

      const formattedStartTime = dayjs(startTime).format("YYYY-MM-DD HH:mm:ss");

      const formattedEndTime = dayjs(endTime).format("YYYY-MM-DD HH:mm:ss");

      tableData.value = []; // 清空旧数据
      const response = await getViolationCountsByTimeRange(
        formattedStartTime,
        formattedEndTime,
        useUserStoreHook().getUserLocationPath
      );

      const data = response.data.map((item: any) => ({
        id: item.id,
        illegalType: item.name,
        count: item.data,
        send: item.send,
        handled: item.handled,
        handleRate: item.handleRate + "%"
      }));

      tableData.value = data; // 更新为新数据
    } catch (error) {
      console.error("Failed to fetch statistics data by time range:", error);
    }
  };

  const fetchLatestNews = async () => {
    try {
      const response = await getLatestNews();
      dynamicList.value = response.data.map((item: any) => ({
        content: item.content,
        time: item.createTime
      }));
    } catch (error) {
      console.error("Failed to fetch latest news:", error);
    }
  };

  onMounted(() => {
    getTableData();

    getCardData();

    fetchRankData("day"); // 默认获取今天的数据

    fetchTrendData("day"); // 默认获取今天的数据
  });

  return {
    cardData,

    timeRange,

    rankData,

    dynamicList,

    loading,

    tableData,

    trendData,

    tableColumns,

    getRankClass,

    getProgressColor,

    getTableData,

    getCardData,

    fetchRankData,

    fetchTrendData,

    fetchStatisticsByTimeRange,

    fetchLatestNews
  };
}
