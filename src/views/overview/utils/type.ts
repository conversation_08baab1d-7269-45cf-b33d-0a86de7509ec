export type CardData = {
  title: string;
  value: string;
  unit: string;
  trend: number;
  color: string;
};

export type RankData = {
  name: string;
  value: number;
};

export type DynamicItem = {
  content: string;
  time: string;
  // type: "primary" | "success" | "warning" | "info" | "danger";
  // color: string;
};

export type StatisticsData = {
  id: number;
  illegalType: string;
  count: number;
  handled: number;
  handleRate: number;
  date: string;
};

export type TableColumnList = Array<{
  label: string;
  prop: string;
  width?: number;
  minWidth?: number;
  formatter?: (row: any) => string;
}>;
