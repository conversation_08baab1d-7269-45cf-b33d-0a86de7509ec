<template>
  <div class="overview-container">
    <div class="header-section">
      <div class="cards-wrapper">
        <DataCards :data="cardData" />
      </div>
    </div>

    <div class="data-analysis">
      <el-row :gutter="16" style="margin-top: 16px">
        <el-col :span="16">
          <TrendChart
            v-model="timeRange"
            :on-view-change="fetchTrendData"
            :trend-data="trendData"
          />
        </el-col>
        <el-col :span="8">
          <RankList
            :data="rankData"
            :get-rank-class="getRankClass"
            :get-progress-color="getProgressColor"
            :on-view-change="fetchRankData"
          />
        </el-col>
      </el-row>
    </div>

    <el-row :gutter="16" style="margin-top: 16px">
      <el-col :span="16">
        <StatisticsTable
          :loading="loading"
          :data="tableData"
          :columns="tableColumns"
          :on-range-change="fetchStatisticsByTimeRange"
        />
      </el-col>
      <el-col :span="8">
        <DynamicList :data="dynamicList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import DataCards from "./components/DataCards.vue";
import DynamicList from "./components/DynamicList.vue";
import RankList from "./components/RankList.vue";
import StatisticsTable from "./components/StatisticsTable.vue";
import TrendChart from "./components/TrendChart.vue";
import { useOverview } from "./utils/hook";

defineOptions({
  name: "Overview"
});

const {
  cardData,
  timeRange,
  rankData,
  dynamicList,
  loading,
  tableData,
  tableColumns,
  getRankClass,
  getProgressColor,
  fetchRankData,
  fetchTrendData,
  trendData,
  fetchStatisticsByTimeRange,
  fetchLatestNews
} = useOverview();

onMounted(() => {
  fetchLatestNews(); // 获取最新动态
});
</script>

<style lang="scss" scoped>
.overview-container {
  padding: 16px;
  background: var(--el-bg-color-page);
}

.header-section {
  margin-bottom: 16px;

  .cards-wrapper {
    width: 100%;
  }
}
</style>
