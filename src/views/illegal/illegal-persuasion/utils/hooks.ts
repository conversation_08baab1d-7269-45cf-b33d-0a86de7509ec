import {
  deleteAccuratePersuasion,
  deleteAccuratePersuasions,
  selectAccuratePersuasionList
} from "@/api/illegal"; // 需要创建对应的API*
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import dayjs from "dayjs";
import { onMounted, reactive, ref, type Ref } from "vue";
import type { AccuratePersuasion, FilterParams } from "./types";

interface ExtendedPaginationProps extends PaginationProps {
  align?: "left" | "center" | "right";
  small?: boolean;
}

export function useLog(_tableRef: Ref) {
  const filterParams = reactive<FilterParams>({});
  const dataList = ref<AccuratePersuasion[]>([]);
  const loading = ref(false);
  const selectedIds = ref<string[]>([]);
  // 添加控制抽屉显示的状态
  const drawerVisible = ref(false);
  const currentDetail = ref<AccuratePersuasion | null>(null);

  const pagination = reactive<ExtendedPaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    align: "center",
    small: false
  });

  const columns: TableColumnList = [
    {
      label: "违法名称",

      prop: "illegalName",

      minWidth: 150
    },
    {
      label: "用户名称",

      prop: "userName",

      minWidth: 100
    },
    {
      label: "处置状态",
      prop: "disposalStatus",
      minWidth: 100,
      formatter: ({ disposalStatus }) => {
        const statusMap = {
          0: "未处理",
          1: "按时处理",
          2: "超时处理",
          3: "核销"
        };
        return statusMap[disposalStatus] || disposalStatus;
      }
    },
    {
      label: "创建时间",

      prop: "createTime",

      minWidth: 160,

      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },

    {
      label: "备注",

      prop: "remarks",

      minWidth: 150,

      formatter: ({ remarks }) => remarks || "无"
    },

    {
      label: "处置方法",

      prop: "disposalMethod",

      minWidth: 100,

      formatter: ({ disposalMethod }) => disposalMethod || "-"
    },
    {
      label: "处理时间",

      prop: "processingTime",

      minWidth: 160,

      formatter: ({ processingTime }) =>
        processingTime
          ? dayjs(processingTime).format("YYYY-MM-DD HH:mm:ss")
          : "-"
    },

    {
      label: "截止时间",

      prop: "deadlineTime",

      minWidth: 160,

      formatter: ({ deadlineTime }) =>
        deadlineTime ? dayjs(deadlineTime).format("YYYY-MM-DD HH:mm:ss") : "-"
    },
    {
      label: "操作",
      fixed: "right",
      width: 80,
      className: "cell-flex-row",
      slot: "operation"
    }

    // {
    //   label: "操作",

    //   width: 100,

    //   fixed: "right",

    //   slot: "operation"
    // }
  ];

  onMounted(() => {
    onSearch();
  });

  const onSearch = () => {
    loading.value = true;
    const params = {
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      userName: filterParams.userName,
      disposalStatus: filterParams.disposalStatus,
      illegalName: filterParams.illegalName,
      processingStartTime: filterParams.processingTimeRange?.[0]
        ? `${filterParams.processingTimeRange[0]} 00:00:00`
        : undefined,
      processingEndTime: filterParams.processingTimeRange?.[1]
        ? `${filterParams.processingTimeRange[1]} 23:59:59`
        : undefined
    };

    selectAccuratePersuasionList(params)
      .then(res => {
        if (res.code === 200) {
          if (res.data && Array.isArray(res.data.records)) {
            dataList.value = res.data.records;
            pagination.total = Number(res.data.total) || 0;
            pagination.currentPage = Number(res.data.curPage) || 1;
            pagination.pageSize = Number(res.data.pageSize) || 10;
          } else if (Array.isArray(res.data)) {
            dataList.value = res.data;
          } else {
            dataList.value = [];
          }
        } else {
          dataList.value = [];
        }
      })
      .catch(() => {
        dataList.value = [];
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    onSearch();
  };

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1; // 切换每页数量时重置为第一页
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSelectionChange(selection) {
    selectedIds.value = selection.map(item => item.uuid);
  }

  async function handleDelete(row) {
    try {
      await deleteAccuratePersuasion(row.uuid);
      message(`删除成功`, { type: "success" });
      onSearch(); // 刷新数据
    } catch (error) {
      message(`删除失败: ${error.message}`, { type: "error" });
    }
  }

  async function handleBatchDelete() {
    if (selectedIds.value.length === 0) {
      message(`请选择要删除的数据`, { type: "warning" });
      return;
    }
    const stringIds = selectedIds.value
      .filter(id => id != null)
      .map(id => id.toString());
    if (stringIds.length === 0) {
      message(`无有效的ID进行删除`, { type: "warning" });
      return;
    }
    try {
      await deleteAccuratePersuasions(stringIds);
      message(`批量删除成功`, { type: "success" });
      selectedIds.value = []; // 清空选择
      onSearch(); // 刷新数据
    } catch (error) {
      message(`批量删除失败: ${error.message}`, { type: "error" });
    }
  }

  /** 预览违法详情 */
  const previewIllegalDetail = (row: AccuratePersuasion) => {
    if (!row) return;
    currentDetail.value = { ...row }; // 创建一个新的对象副本
    drawerVisible.value = true;
  };

  return {
    loading,
    filterParams,
    dataList,
    pagination,
    columns,
    currentDetail,
    drawerVisible,
    onSearch,
    resetFilterForm,
    handleSizeChange,
    handleCurrentChange,
    selectedIds,
    handleSelectionChange,
    handleDelete,
    handleBatchDelete,
    previewIllegalDetail
  };
}
