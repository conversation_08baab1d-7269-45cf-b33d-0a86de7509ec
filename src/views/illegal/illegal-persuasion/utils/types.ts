export interface FilterParams {
  userName?: string; // 操作人员
  disposalStatus?: string; // 处置状态
  illegalName?: string; // 违法名称
  processingTimeRange?: [string, string]; // 处理时间范围
}

export interface AccuratePersuasion {
  uuid: string; // 唯一标识符
  illegalName: string; // 违法名称
  userName: string; // 用户名称
  disposalMethod: string; // 处置方法
  disposalStatus: string; // 处置状态
  imgl: number; // 图片链接
  remarks: string; // 备注
  illegalType: string; // 违法类型
  createTime: string; // 创建时间
  processingTime: number; // 处理时间
  deadlineTime: string; // 截止时间
  illegalRecordsUuid: string; // 违法记录唯一标识符
}
