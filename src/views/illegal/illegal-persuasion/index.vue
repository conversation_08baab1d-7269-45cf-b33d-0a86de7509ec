<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import View from "@iconify-icons/ep/view";
import { ref } from "vue";
import DetailDrawer from "../illegal-list/components/detail-drawer.vue";
import { useLog } from "./utils/hooks";
defineOptions({
  name: "IllegalPersuasion"
});

const filterRef = ref();
const tableRef = ref();
const {
  filterParams,
  loading,
  dataList,
  columns,
  pagination,
  selectedIds,
  currentDetail,
  drawerVisible,
  onSearch,
  resetFilterForm,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  handleDelete,
  handleBatchDelete,
  previewIllegalDetail
} = useLog(tableRef);
</script>

<template>
  <div>
    <el-form
      ref="filterRef"
      :inline="true"
      :model="filterParams"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="处理人员：" prop="userName">
        <el-input
          v-model="filterParams.userName"
          placeholder="请输入处理人员"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="违法名称：" prop="illegalName">
        <el-input
          v-model="filterParams.illegalName"
          placeholder="请输入违法名称"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="处理状态：" prop="disposalStatus">
        <el-select
          v-model="filterParams.disposalStatus"
          placeholder="请选择处理状态"
          clearable
          class="!w-[180px]"
        >
          <el-option label="未处理" value="0" />
          <el-option label="按时处理" value="1" />
          <el-option label="超时处理" value="2" />
          <el-option label="核销" value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="处理时间">
        <el-date-picker
          v-model="filterParams.processingTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          class="!w-[380px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon(Refresh)"
          @click="resetFilterForm(filterRef)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <pure-table
      ref="tableRef"
      row-key="id"
      adaptive
      :adaptiveConfig="{ offsetBottom: 108 }"
      align-whole="center"
      table-layout="auto"
      :loading="loading"
      :data="dataList"
      :columns="columns"
      :pagination="{
        total: pagination.total,
        pageSize: pagination.pageSize,
        currentPage: pagination.currentPage,
        background: true,
        align: 'center'
      }"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-primary)'
      }"
      @selection-change="handleSelectionChange"
      @page-size-change="handleSizeChange"
      @page-current-change="handleCurrentChange"
    >
      <template #operation="{ row }">
        <el-button
          class="reset-margin"
          link
          type="primary"
          :icon="useRenderIcon(View)"
          @click="previewIllegalDetail(row)"
        >
          查看详情
        </el-button>
      </template>
    </pure-table>
    <detail-drawer
      v-model="drawerVisible"
      :currentId="currentDetail?.illegalRecordsUuid"
      :list="dataList.map(item => item.illegalRecordsUuid)"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-pagination) {
  padding: 16px 0;
  margin: 0 !important;
  background-color: #fff;
}
</style>
