import type { AreaTreeNode } from "@/utils/tree";

// 请假记录类型
export interface LeaveRecord {
  id: number;
  userId: number;
  userName: string;
  startTime: number;
  endTime: number;
  duration: number;
  reason: string;
  status: number;
  statusDesc: string;
  leaveType: number;
  leaveTypeDesc: string;
  createTime: number;
  approvedTime?: number; // 审批时间
  approveStatus?: number; // 审批状态
  approvalComment?: string; // 审批意见
  approverName?: string; // 审批人
  city?: string; // 市
  county?: string; // 县
  township?: string; // 镇
  hamlet?: string; // 村
  site?: string; // 点位
}

// 查询表单类型
export interface QueryForm {
  userName?: string;
  status?: number | undefined;
  startDate: string;
  endDate: string;
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}

// 分页参数类型
export interface PaginationParams {
  page: number;
  size: number;
}

// 使用 AreaTreeNode 替代之前的 TreeNode
export type TreeNode = AreaTreeNode;
