import { ref, onMounted } from "vue";
import type { Ref } from "vue";
import type { FormInstance } from "element-plus";
import type { LeaveRecord, QueryForm, TreeNode } from "../types";
import { ElMessage, ElMessageBox } from "element-plus";
import { getLeaveRecords, approveLeave, getLeaveDetail } from "@/api/leave";
import { formatTimestamp } from "@/utils/date";
import { getAreaTree } from "@/api/system";
import { handleTreeNode } from "@/utils/tree";

// 添加返回值类型定义
interface UseExamineLeave {
  queryForm: Ref<QueryForm>;
  queryFormRef: Ref<FormInstance | undefined>;
  tableData: Ref<LeaveRecord[]>;
  loading: Ref<boolean>;
  total: Ref<number>;
  currentPage: Ref<number>;
  pageSize: Ref<number>;
  getLeaveList: () => Promise<void>;
  resetQuery: () => void;
  handleCurrentChange: (val: number) => void;
  handleSizeChange: (val: number) => void;
  handleApprove: (row: LeaveRecord) => Promise<void>;
  formatDateTime: (timestamp: number) => string;
  dialogVisible: Ref<boolean>;
  dialogTitle: Ref<string>;
  approvalForm: Ref<{
    approvalStatus: number;
    approvalComment: string;
  }>;
  submitApproval: () => Promise<void>;
  hiddenTree: Ref<boolean>;
  treeData: Ref<TreeNode[]>;
  treeLoading: Ref<boolean>;
  onTreeSelect: (node: TreeNode) => void;
  detailVisible: Ref<boolean>;
  currentDetail: Ref<LeaveRecord | null>;
  handleViewDetail: (row: LeaveRecord) => Promise<void>;
}

export default function useExamineLeave(): UseExamineLeave {
  // 查询表单
  const queryForm = ref<QueryForm>({
    userName: undefined,
    status: undefined,
    startDate: "",
    endDate: "",
    city: undefined,
    county: undefined,
    township: undefined,
    hamlet: undefined,
    site: undefined
  });

  // 表格数据
  const tableData = ref<LeaveRecord[]>([]);
  const loading = ref(false);
  const total = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 表单ref
  const queryFormRef = ref<FormInstance | undefined>(undefined);

  // 审批对话框相关
  const dialogVisible = ref(false);
  const dialogTitle = ref("");
  const currentRecord = ref<LeaveRecord | null>(null);
  const approvalForm = ref({
    approvalStatus: 1,
    approvalComment: ""
  });

  // 添加树相关状态
  const hiddenTree = ref(false);
  const treeData = ref<TreeNode[]>([]);
  const selectedTreeNode = ref<TreeNode | null>(null);
  const treeLoading = ref(false);

  // 详情对话框相关
  const detailVisible = ref(false);
  const currentDetail = ref<LeaveRecord | null>(null);

  // 获取请假记录列表
  const getLeaveList = async () => {
    loading.value = true;
    try {
      const params = {
        ...queryForm.value,
        // 添加区域筛选参数
        ...(selectedTreeNode.value
          ? handleTreeNode(selectedTreeNode.value)
          : {}),
        page: currentPage.value,
        size: pageSize.value
      };

      const res = await getLeaveRecords(params);
      if (res.code === 200) {
        tableData.value = res.data.records;
        total.value = res.data.total;
      } else {
        ElMessage.error(res.message || "获取请假记录失败");
      }
    } catch (error) {
      ElMessage.error("获取请假记录失败");
    } finally {
      loading.value = false;
    }
  };

  // 重置查询表单
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    getLeaveList();
  };

  // 处理页码变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    getLeaveList();
  };

  // 处理每页条数变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    getLeaveList();
  };

  // 处理审批
  const handleApprove = async (row: LeaveRecord) => {
    try {
      const action = await ElMessageBox.confirm("请选择审批结果", "请假审批", {
        confirmButtonText: "批准",
        cancelButtonText: "拒绝",
        type: "warning",
        distinguishCancelAndClose: true
      });

      currentRecord.value = row;
      approvalForm.value.approvalStatus = action === "confirm" ? 1 : 2;
      dialogTitle.value = action === "confirm" ? "批准请假" : "拒绝请假";
      dialogVisible.value = true;
    } catch (err: any) {
      if (err === "cancel") {
        // 用户点击"拒绝"按钮
        currentRecord.value = row;
        approvalForm.value.approvalStatus = 2;
        dialogTitle.value = "拒绝请假";
        dialogVisible.value = true;
      }
      // 用户点击关闭按钮时不做任何处理
    }
  };

  // 提交审批
  const submitApproval = async () => {
    if (!currentRecord.value) return;

    try {
      const res = await approveLeave({
        leaveId: currentRecord.value.id,
        approvalStatus: approvalForm.value.approvalStatus,
        approvalComment: approvalForm.value.approvalComment
      });

      if (res.code === 200) {
        ElMessage.success(
          approvalForm.value.approvalStatus === 1 ? "已批准" : "已拒绝"
        );
        dialogVisible.value = false;
        approvalForm.value.approvalComment = ""; // 清空审批意见
        await getLeaveList();
      } else {
        ElMessage.error(res.message || "操作失败");
      }
    } catch (error) {
      ElMessage.error("操作失败");
    }
  };

  // 格式化时间戳
  const formatDateTime = (timestamp: number) => {
    return formatTimestamp(timestamp, "YYYY-MM-DD HH:mm:ss");
  };

  // 处理树节点选择
  const onTreeSelect = (node: TreeNode) => {
    selectedTreeNode.value = node.selected ? node : null;
    getLeaveList();
  };

  // 初始化获取区域树
  const initAreaTree = async () => {
    treeLoading.value = true;
    try {
      const res = await getAreaTree();
      if (res.code === 200) {
        treeData.value = res.data;
      }
    } finally {
      treeLoading.value = false;
    }
  };

  // 查看详情
  const handleViewDetail = async (row: LeaveRecord) => {
    try {
      const res = await getLeaveDetail(row.id);
      if (res.code === 200) {
        currentDetail.value = res.data;
        detailVisible.value = true;
      } else {
        ElMessage.error(res.message || "获取详情失败");
      }
    } catch (error) {
      ElMessage.error("获取详情失败");
    }
  };

  onMounted(() => {
    getLeaveList();
    initAreaTree();
  });

  return {
    queryForm,
    queryFormRef,
    tableData,
    loading,
    total,
    currentPage,
    pageSize,
    getLeaveList,
    resetQuery,
    handleCurrentChange,
    handleSizeChange,
    handleApprove,
    formatDateTime,
    dialogVisible,
    dialogTitle,
    approvalForm,
    submitApproval,
    hiddenTree,
    treeData,
    treeLoading,
    onTreeSelect,
    detailVisible,
    currentDetail,
    handleViewDetail
  };
}
