<script setup lang="ts">
import { onMounted } from "vue";
import { Search, ArrowLeftBold } from "@element-plus/icons-vue";
import useExamineLeave from "./utils/hook";
import { formatTimestamp } from "@/utils/date";
import Tree from "../illegal-list/tree.vue";

const {
  queryForm,
  queryFormRef,
  tableData,
  loading,
  total,
  currentPage,
  pageSize,
  getLeaveList,
  resetQuery,
  handleCurrentChange,
  handleSizeChange,
  handleApprove,
  dialogVisible,
  dialogTitle,
  approvalForm,
  submitApproval,
  hiddenTree,
  treeData,
  treeLoading,
  onTreeSelect,
  detailVisible,
  currentDetail,
  handleViewDetail
} = useExamineLeave();

// 格式化时间戳
const formatDateTime = (timestamp: number) => {
  return formatTimestamp(timestamp, "YYYY-MM-DD HH:mm:ss");
};

onMounted(() => {
  getLeaveList();
});
</script>

<template>
  <div :class="['flex', 'justify-between', 'relative']">
    <tree
      ref="treeRef"
      :class="[
        'mr-2',
        'transition-all duration-300 ease-in-out overflow-hidden',
        hiddenTree ? 'w-0 opacity-0' : 'w-[300px] opacity-100'
      ]"
      :treeData="treeData"
      :treeLoading="treeLoading"
      @tree-select="onTreeSelect"
    />

    <div
      :class="[
        'transition-all duration-300 ease-in-out relative',
        hiddenTree ? 'w-full' : 'w-[calc(100%-320px)]'
      ]"
    >
      <div
        class="toggle-bar"
        :style="{
          left: '-12px'
        }"
        @click="hiddenTree = !hiddenTree"
      >
        <el-icon
          class="toggle-icon"
          :class="hiddenTree ? '' : 'transform rotate-180'"
        >
          <ArrowLeftBold />
        </el-icon>
      </div>

      <!-- 搜索区域 -->
      <el-form
        ref="queryFormRef"
        :model="queryForm"
        :inline="true"
        class="search-form"
      >
        <el-form-item label="员工姓名" prop="userName">
          <el-input
            v-model="queryForm.userName"
            placeholder="请输入员工姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="审核状态" prop="status">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="待审批" :value="0" />
            <el-option label="已批准" :value="1" />
            <el-option label="已拒绝" :value="2" />
            <el-option label="已取消" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="queryForm.startDate"
            type="datetime"
            placeholder="开始时间"
          />
          <span class="date-separator">至</span>
          <el-date-picker
            v-model="queryForm.endDate"
            type="datetime"
            placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="getLeaveList">
            搜索
          </el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <!-- <el-table-column prop="id" label="请假ID" width="80" /> -->
        <el-table-column prop="userId" label="员工ID" width="80" />
        <el-table-column prop="userName" label="员工姓名" width="120" />
        <el-table-column label="请假类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.leaveTypeDesc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column label="请假时长" width="100">
          <template #default="{ row }"> {{ row.duration }}小时 </template>
        </el-table-column>
        <el-table-column
          prop="reason"
          label="请假原因"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === 0
                  ? 'warning'
                  : row.status === 1
                    ? 'success'
                    : row.status === 2
                      ? 'danger'
                      : 'info'
              "
            >
              {{ row.statusDesc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              查看
            </el-button>
            <el-button
              v-if="row.status === 0"
              type="primary"
              link
              @click="handleApprove(row)"
            >
              审批
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        class="pagination"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <!-- 审批对话框 -->
      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
        <el-form :model="approvalForm" label-width="80px">
          <el-form-item label="审批意见">
            <el-input
              v-model="approvalForm.approvalComment"
              type="textarea"
              :rows="3"
              placeholder="请输入审批意见（选填）"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitApproval">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 修改详情对话框 -->
      <el-dialog
        v-model="detailVisible"
        title="请假详情"
        width="700px"
        destroy-on-close
      >
        <el-descriptions :column="2" border>
          <el-descriptions-item label="员工ID">
            {{ currentDetail?.userId }}
          </el-descriptions-item>
          <el-descriptions-item label="员工姓名">
            {{ currentDetail?.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="请假类型">
            <el-tag>{{ currentDetail?.leaveTypeDesc }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="请假状态">
            <el-tag
              :type="
                currentDetail?.status === 0
                  ? 'warning'
                  : currentDetail?.status === 1
                    ? 'success'
                    : currentDetail?.status === 2
                      ? 'danger'
                      : 'info'
              "
            >
              {{ currentDetail?.statusDesc }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDateTime(currentDetail?.startTime || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ formatDateTime(currentDetail?.endTime || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="请假时长">
            {{ currentDetail?.duration }}小时
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentDetail?.createTime || 0) }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="请假原因">
            {{ currentDetail?.reason }}
          </el-descriptions-item>
          <!-- 添加区域信息 -->
          <el-descriptions-item :span="2" label="所属区域">
            {{ currentDetail?.city || "-" }} {{ currentDetail?.county || "" }}
            {{ currentDetail?.township || "" }}
            {{ currentDetail?.hamlet || "" }} {{ currentDetail?.site || "" }}
          </el-descriptions-item>
          <!-- 添加审批信息 -->
          <template v-if="currentDetail?.status !== 0">
            <el-descriptions-item label="审批人">
              {{ currentDetail?.approverName || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="审批时间">
              {{
                currentDetail?.approvedTime
                  ? formatDateTime(currentDetail.approvedTime)
                  : "-"
              }}
            </el-descriptions-item>
            <el-descriptions-item :span="2" label="审批意见">
              {{ currentDetail?.approvalComment || "-" }}
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.scss");

.toggle-bar {
  position: absolute;
  top: 50%;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 60px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #fff;
  border-right: none;
  border-radius: 4px 0 0 4px;
  box-shadow: -2px 0 6px -2px rgb(0 0 0 / 8%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(-50%);

  &:hover {
    background: var(--el-color-primary-light-9);

    .toggle-icon {
      color: var(--el-color-primary);
    }
  }
}

.toggle-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.transform {
    transform: rotate(180deg);
  }
}
</style>
