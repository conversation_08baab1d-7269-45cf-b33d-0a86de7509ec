<template>
  <el-drawer
    v-model="visible"
    title="违法详情"
    size="600px"
    :destroy-on-close="true"
    class="illegal-detail-drawer"
  >
    <div v-loading="loading" class="flex flex-col h-full overflow-hidden">
      <div class="flex-1 overflow-y-auto overflow-x-hidden content-container">
        <template v-if="!loading && detailData.illegalRecords">
          <!-- 违法信息卡片 -->
          <div class="info-card mb-4">
            <div class="card-header">
              <el-icon><Warning /></el-icon>
              <span>违法信息</span>
            </div>
            <div class="card-content">
              <!-- 图片显示区域 - 左右布局 -->
              <div class="image-container-wrapper mb-4">
                <!-- 左侧：违法抓拍图片 -->
                <div class="image-section">
                  <div class="image-section-title">
                    <span class="section-title-text">违法抓拍图片</span>
                  </div>
                  <div class="image-grid">
                    <div
                      v-for="(url, index) in pictureUrls"
                      :key="index"
                      class="image-item"
                      @click="onPreviewImg(pictureUrls, index)"
                    >
                      <el-image
                        :src="url"
                        fit="contain"
                        class="w-full h-full bg-[#f8f9fa]"
                        loading="lazy"
                      >
                        <template #error>
                          <div class="image-error-state">
                            <div class="error-icon-wrapper">
                              <el-icon class="error-icon"><Picture /></el-icon>
                            </div>
                            <span class="error-text">加载失败</span>
                            <div class="error-retry">点击重试</div>
                          </div>
                        </template>
                        <template #placeholder>
                          <div class="image-loading-state">
                            <div class="loading-skeleton">
                              <div class="skeleton-shimmer" />
                            </div>
                            <div class="loading-content">
                              <div class="loading-spinner">
                                <div class="spinner-ring" />
                                <div class="spinner-ring" />
                                <div class="spinner-ring" />
                              </div>
                              <span class="loading-text">加载中...</span>
                            </div>
                          </div>
                        </template>
                      </el-image>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 违法信息网格 -->
              <div class="info-list">
                <div class="info-row">
                  <span class="info-label">抓拍时间</span>
                  <span class="info-value">
                    {{
                      dayjs(detailData.illegalRecords.captureTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">抓拍地点</span>
                  <span class="info-value">{{
                    getFullAddress(detailData.illegalRecords)
                  }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">车牌号</span>
                  <span class="info-value">{{
                    detailData.illegalRecords.plateNumber
                  }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">车牌颜色</span>
                  <span class="info-value">
                    {{
                      formatTagCell(
                        detailData.illegalRecords,
                        "plateColor",
                        true
                      )
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">车辆类型</span>
                  <span class="info-value">
                    {{
                      formatTagCell(
                        detailData.illegalRecords,
                        "vehicleType",
                        true
                      )
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">违法类型</span>
                  <span class="info-value">
                    {{
                      formatTagCell(
                        detailData.illegalRecords,
                        "illegalType",
                        true
                      )
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">实载人数</span>
                  <span class="info-value">{{
                    detailData.illegalRecords.numberOfPassengers
                  }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">现场劝导</span>
                  <span class="info-value">
                    {{
                      formatTagCell(
                        detailData.illegalRecords,
                        "persuasiveBehavior",
                        true
                      )
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">劝导视频</span>
                  <span class="info-value">
                    <el-link
                      v-if="detailData.illegalRecords?.videoUrl"
                      :icon="VideoPlay"
                      type="primary"
                      @click="videoPlayHandle"
                      >播放视频</el-link
                    >
                    <span v-else class="text-gray-400">没有相关视频</span>
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">经度</span>
                  <span class="info-value">{{
                    detailData.illegalRecords.longitude
                  }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">纬度</span>
                  <span class="info-value">{{
                    detailData.illegalRecords.latitude
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 处理信息卡片 -->
          <div v-if="detailData.accuratePersuasion" class="info-card mb-4">
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>处理信息</span>
            </div>
            <div class="card-content">
              <!-- 处理图片轮播图 -->
              <div
                v-if="detailData.accuratePersuasion?.imgl"
                class="carousel-wrapper"
                :class="{ 'has-two-images': handlePictureUrls.length === 2 }"
              >
                <el-carousel
                  height="240px"
                  :autoplay="false"
                  trigger="click"
                  :type="handlePictureUrls.length > 1 ? 'card' : ''"
                  indicator-position="outside"
                  class="rounded-lg overflow-hidden mb-4"
                  :class="{ 'el-carousel--2': handlePictureUrls.length === 2 }"
                  :initial-index="0"
                >
                  <el-carousel-item
                    v-for="(url, index) in handlePictureUrls"
                    :key="index"
                    class="cursor-pointer"
                    @click="onPreviewImg(handlePictureUrls, index)"
                  >
                    <el-image
                      :src="url"
                      fit="contain"
                      class="w-full h-full bg-[#f8f9fa]"
                      loading="lazy"
                    >
                      <template #error>
                        <div class="image-error-state">
                          <div class="error-icon-wrapper">
                            <el-icon class="error-icon"><Picture /></el-icon>
                          </div>
                          <span class="error-text">加载失败</span>
                          <div class="error-retry">点击重试</div>
                        </div>
                      </template>
                      <template #placeholder>
                        <div class="image-loading-state">
                          <div class="loading-skeleton">
                            <div class="skeleton-shimmer" />
                          </div>
                          <div class="loading-content">
                            <div class="loading-spinner">
                              <div class="spinner-ring" />
                              <div class="spinner-ring" />
                              <div class="spinner-ring" />
                            </div>
                            <span class="loading-text">加载中...</span>
                          </div>
                        </div>
                      </template>
                    </el-image>
                    <div
                      class="absolute inset-0 bg-black opacity-0 hover:opacity-20 transition-opacity flex items-center justify-center"
                    >
                      <el-icon :size="24" color="#fff"><ZoomIn /></el-icon>
                    </div>
                  </el-carousel-item>
                </el-carousel>
              </div>

              <!-- 处理信息网格 -->
              <div class="info-list">
                <div class="info-row">
                  <span class="info-label">处理人员</span>
                  <span class="info-value">
                    {{ detailData.accuratePersuasion.actualProcessingName }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">处理状态</span>
                  <span class="info-value">
                    <el-tag
                      :type="
                        detailData.accuratePersuasion.disposalStatus
                          ? 'success'
                          : 'warning'
                      "
                      size="small"
                    >
                      {{
                        detailData.accuratePersuasion.disposalStatus
                          ? "已处理"
                          : "未处理"
                      }}
                    </el-tag>
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">处理时间</span>
                  <span class="info-value">
                    {{
                      dayjs(
                        detailData.accuratePersuasion.processingTime
                      ).format("YYYY-MM-DD HH:mm:ss")
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">截止时间</span>
                  <span class="info-value">
                    {{
                      dayjs(detailData.accuratePersuasion.deadlineTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">处理方式</span>
                  <span class="info-value">
                    {{ detailData.accuratePersuasion.disposalMethod || "-" }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">备注</span>
                  <span class="info-value">
                    {{ detailData.accuratePersuasion.remarks || "-" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 加载失败提示 -->
        <el-empty
          v-if="!loading && !detailData.illegalRecords"
          description="加载数据失败"
        >
          <template #image>
            <el-icon :size="48" class="text-gray-400">
              <Warning />
            </el-icon>
          </template>
          <template #description>
            <p class="text-gray-400 mb-4">数据加载失败，请稍后重试</p>
            <el-button type="primary" @click="fetchDetail(props.currentId)"
              >重新加载</el-button
            >
          </template>
        </el-empty>
      </div>

      <!-- 操作区域 -->
      <div class="drawer-footer">
        <el-button @click="visible = false">关 闭</el-button>
        <el-button
          v-if="currentDataIndex > 0"
          type="primary"
          plain
          @click="prevHistory"
        >
          上一条
        </el-button>
        <el-button
          v-if="currentDataIndex < props.list.length - 1"
          type="primary"
          plain
          @click="nextHistory"
        >
          下一条
        </el-button>
        <el-button type="primary" @click="handlePrint">
          <el-icon class="mr-1"><Printer /></el-icon>
          打 印
        </el-button>
      </div>
    </div>

    <!-- 打印区域（隐藏） -->
    <div ref="printRef" class="print-area">
      <template v-if="detailData.illegalRecords">
        <h2 class="print-title">违法详情</h2>

        <!-- 违法信息 -->
        <div class="print-section">
          <h3 class="print-subtitle">
            <el-icon><Warning /></el-icon>
            违法信息
          </h3>

          <!-- 违法图片网格 -->
          <div v-if="pictureUrls.length" class="print-images">
            <div
              v-for="(url, index) in pictureUrls"
              :key="index"
              class="image-item"
            >
              <el-image :src="url" fit="cover" />
            </div>
          </div>
          <!-- 违法信息表格 -->
          <div class="print-table">
            <!-- 复制 info-list 的内容，保持相同的数据展示 -->
            <div
              v-for="(item, index) in illegalInfoItems"
              :key="index"
              class="info-row"
            >
              <span class="info-label">{{ item.label }}</span>
              <span class="info-value">{{ item.value }}</span>
            </div>
          </div>
        </div>

        <!-- 处理信息 -->
        <div v-if="detailData.accuratePersuasion" class="print-section">
          <h3 class="print-subtitle">
            <el-icon><Document /></el-icon>
            处理信息
          </h3>

          <!-- 处理图片网格 -->
          <div v-if="handlePictureUrls.length" class="print-images">
            <div
              v-for="(url, index) in handlePictureUrls"
              :key="index"
              class="image-item"
            >
              <el-image :src="url" fit="cover" />
            </div>
          </div>

          <!-- 处理信息表格 -->
          <div class="print-table">
            <!-- 复制处理信息的展示内容 -->
            <div
              v-for="(item, index) in handleInfoItems"
              :key="index"
              class="info-row"
            >
              <span class="info-label">{{ item.label }}</span>
              <span class="info-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 视频播放弹窗 -->
    <el-dialog
      v-model="videoDialogVisible"
      width="60vw"
      destroy-on-close
      :close-on-click-modal="false"
      class="video-dialog"
    >
      <template #title>
        <div class="dialog-title">
          <span
            class="location"
            style="display: inline-block; margin-right: 10px"
            >{{ videoDialogLocation }}</span
          >
          <el-tag size="small" :type="videoDialogType.type" class="type-tag">
            {{ videoDialogType.text }}
          </el-tag>
        </div>
      </template>
      <video-player :options="videoOptions" class="video-player" />
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
import { fecthIllegalHandleResult } from "@/api/illegal";
import { usePreviewImg } from "@/hooks/usePreviewImg";
import { message } from "@/utils/message";
import {
  Document,
  Picture,
  Printer,
  VideoPlay,
  Warning,
  ZoomIn
} from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { computed, ref, watch } from "vue";
import { formatTagCell } from "../utils/tool";
import type { IllegalRecord, IllegalResultDto } from "../utils/type";

const props = defineProps<{
  modelValue: boolean;
  currentId: string;
  list: string[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "refresh"): void;
}>();

const { onPreviewImg } = usePreviewImg();
const detailData = ref<IllegalResultDto>({} as IllegalResultDto);
const loading = ref(false);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

// 计算当前记录的索引
const currentDataIndex = computed(() => {
  return props.list.findIndex(
    item => item === detailData.value?.illegalRecords?.uuid
  );
});

// 切换到下一条数据记录
const nextHistory = () => {
  if (currentDataIndex.value < props.list.length - 1) {
    fetchDetail(props.list[currentDataIndex.value + 1]);
  }
};

// 切换到上一条数据记录
const prevHistory = () => {
  if (currentDataIndex.value > 0) {
    fetchDetail(props.list[currentDataIndex.value - 1]);
  }
};

// 获取详情数据
const fetchDetail = async (id: string) => {
  try {
    loading.value = true;
    const res = await fecthIllegalHandleResult(id);
    if (res.code === 200) {
      detailData.value = res.data;
    }
  } catch (error) {
    console.error("获取详情失败:", error);
    message("获取详情失败，请稍后重试", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 确保 currentId 有值时再发起请求
watch(
  () => props.currentId,
  newVal => {
    if (newVal) {
      fetchDetail(newVal);
    }
  },
  { immediate: true }
);

const printRef = ref();

// 违法信息项
const illegalInfoItems = computed(() => {
  if (!detailData.value?.illegalRecords) return [];
  const record = detailData.value.illegalRecords;

  return [
    {
      label: "违法类型",
      value: formatTagCell(record, "illegalType", true)
    },
    {
      label: "车牌号",
      value: record.plateNumber
    },
    {
      label: "车牌颜色",
      value: formatTagCell(record, "plateColor", true)
    },
    {
      label: "车辆类型",
      value: formatTagCell(record, "vehicleType", true)
    },
    {
      label: "实载人数",
      value: record.numberOfPassengers
    },
    {
      label: "现场劝导",
      value: formatTagCell(record, "persuasiveBehavior", true)
    },
    {
      label: "抓拍时间",
      value: dayjs(record.captureTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "抓拍地点",
      value: getFullAddress(record)
    },
    {
      label: "经度",
      value: record.longitude
    },
    {
      label: "纬度",
      value: record.latitude
    }
  ];
});

// 处理信息项
const handleInfoItems = computed(() => {
  if (!detailData.value?.accuratePersuasion) return [];
  const handle = detailData.value.accuratePersuasion;

  return [
    {
      label: "处理人员",
      value: handle.actualProcessingName
    },
    {
      label: "处理状态",
      value: handle.disposalStatus ? "已处理" : "未处理"
    },
    {
      label: "处理时间",
      value: dayjs(handle.processingTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "截止时间",
      value: dayjs(handle.deadlineTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "处理方式",
      value: handle.disposalMethod || "-"
    },
    {
      label: "备注",
      value: handle.remarks || "-"
    }
  ];
});

// 打印处理
const handlePrint = () => {
  const content = printRef.value.innerHTML;
  const style = `
     <style>
      .print-area {
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .print-title {
        text-align: center;
        font-size: 20px;
        margin-bottom: 20px;
        font-weight: 500;
        color: #303133;
      }
      .print-section {
        margin-bottom: 30px;
      }
      .print-subtitle {
        font-size: 16px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        padding: 12px 16px;
        background: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
        color: #303133;
      }
      .print-subtitle .el-icon {
        font-size: 16px;
        color: var(--el-color-primary, #409eff);
      }
      .print-images {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 20px;

        .print-image-title {
          grid-column: 1 / -1;
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          padding-bottom: 6px;
          border-bottom: 1px solid var(--el-border-color-lighter);
        }
      }
      .image-item {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        overflow: hidden;
      }
      .image-item img {
        width: 100%;
        height: 180px;
        object-fit: contain;
        background: #f5f7fa;
      }
      .print-table {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        overflow: hidden;
      }
      .info-row {
        display: flex;
        border-bottom: 1px solid #dcdfe6;
      }
      .info-row:last-child {
        border-bottom: none;
      }
      .info-label {
        width: 100px;
        padding: 10px 14px;
        background-color: #f5f7fa;
        border-right: 1px solid #dcdfe6;
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        display: flex;
        align-items: center;
      }
      .info-value {
        flex: 1;
        padding: 10px 12px;
        color: #303133;
        font-size: 14px;
        line-height: 1.6;
        display: flex;
        align-items: center;
      }
      .el-tag {
        display: inline-block;
        padding: 0 8px;
        height: 24px;
        line-height: 22px;
        font-size: 12px;
        border-radius: 2px;
      }
      .el-tag--success {
        color: #67c23a;
        background-color: #f0f9eb;
        border: 1px solid #b3e19d;
      }
      .el-tag--warning {
        color: #e6a23c;
        background-color: #fdf6ec;
        border: 1px solid #f3d19e;
      }
      @media print {
        @page {
          size: A4;
          margin: 1.5cm;
        }
        body {
          color: #303133;
        }
        .print-subtitle {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .info-label {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .el-tag {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      }
    </style>
  `;

  const printWindow = document.createElement("iframe");
  printWindow.style.display = "none";
  document.body.appendChild(printWindow);

  printWindow.contentDocument.write(`
    <!DOCTYPE html>
    <html>
      <head>${style}</head>
      <body>${content}</body>
    </html>
  `);

  printWindow.contentDocument.close();
  printWindow.contentWindow.focus();

  // 等待图片加载完成后打印
  setTimeout(() => {
    printWindow.contentWindow.print();
    document.body.removeChild(printWindow);
  }, 500);
};

// 处理图片URL数组
const pictureUrls = computed(() => {
  try {
    // 确保 detailData.illegalRecords.pictureUrl 是一个 JSON 字符串或数组
    return JSON.parse(detailData.value?.illegalRecords?.pictureUrl || "[]");
  } catch (e) {
    console.error("解析图片URL失败:", e);
    return [];
  }
});
// 处理图片URL数组
const handlePictureUrls = computed(() => {
  try {
    return JSON.parse(detailData.value?.accuratePersuasion?.imgl || "[]");
  } catch (e) {
    console.error("解析处理图片URL失败:", e);
    return [];
  }
});

// 获取完整地址
const getFullAddress = (record: IllegalRecord) => {
  const parts = [
    record.county,
    record.township,
    record.hamlet,
    record.site
  ].filter(Boolean);
  return parts.length ? parts.join(" - ") : "-";
};

// 添加视频播放相关的响应式变量
const videoDialogVisible = ref(false);
const videoOptions = ref({
  autoplay: false,
  controls: true,
  preload: "auto",
  fluid: true,
  sources: [
    {
      src: "",
      type: "video/mp4"
    }
  ],
  controlBar: {
    children: [
      "playToggle",
      "volumePanel",
      "currentTimeDisplay",
      "timeDivider",
      "durationDisplay",
      "progressControl",
      "remainingTimeDisplay",
      "fullscreenToggle"
    ]
  }
});

const videoPlayHandle = () => {
  videoOptions.value.sources[0].src =
    detailData.value?.illegalRecords?.videoUrl || "";
  videoDialogVisible.value = true;
};

// 拆分视频弹窗标题为地点和类型
const videoDialogLocation = computed(() => {
  if (!detailData.value?.illegalRecords) return "";
  return getFullAddress(detailData.value.illegalRecords);
});

const videoDialogType = computed(() => {
  if (!detailData.value?.illegalRecords)
    return { text: "", type: "info" as const };

  const status = detailData.value.illegalRecords.disposalStatus;
  const illegalType = detailData.value.illegalRecords.illegalType;

  // 直接使用 illegalType.desc 而不是 formatTagCell
  return {
    text: illegalType?.desc || "",
    type:
      status?.code === 1 || status?.code === 2 || status?.code === 3
        ? ("danger" as const)
        : ("info" as const)
  };
});
</script>

<style lang="scss">
/* 动画定义 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.illegal-detail-drawer {
  .el-drawer__header {
    position: relative;
    z-index: 1;
    padding: 16px 20px;
    margin-bottom: 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    box-shadow: 0 1px 2px rgb(0 0 0 / 6%);
  }

  .el-drawer__body {
    height: 100%;
    padding: 0;
    overflow: hidden;
  }

  .content-container {
    height: 100%;
    padding: 16px;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--el-border-color-darker);
      border-radius: 3px;

      &:hover {
        background-color: var(--el-border-color);
      }
    }

    &::-webkit-scrollbar-track {
      background-color: var(--el-fill-color-lighter);
      border-radius: 3px;
    }
  }

  .info-card {
    margin-bottom: 16px;
    background: var(--el-bg-color-overlay);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 8px rgb(0 0 0 / 8%);
    }

    .card-header {
      display: flex;
      gap: 8px;
      align-items: center;
      padding: 12px 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      background: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-lighter);

      .el-icon {
        font-size: 16px;
        color: var(--el-color-primary);
      }
    }

    .card-content {
      padding: 16px;

      .el-carousel {
        margin-bottom: 16px;
        overflow: hidden;
        border-radius: 4px;

        .el-image {
          background: transparent !important;
        }
      }
    }
  }

  .info-list {
    overflow: hidden;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;

    .info-row {
      display: flex;
      align-items: stretch;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        width: 100px;
        padding: 10px 14px;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.6;
        color: var(--el-text-color-regular);
        background-color: var(--el-fill-color-light);
        border-right: 1px solid var(--el-border-color-lighter);
      }

      .info-value {
        display: flex;
        flex: 1;
        align-items: center;
        padding: 10px 12px;
        font-size: 14px;
        line-height: 1.6;
        color: var(--el-text-color-primary);

        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }

  // 暗色主题适配
  &.dark {
    .info-list {
      .info-row {
        .info-label {
          background-color: var(--el-fill-color-dark);
        }
      }
    }
  }

  .el-carousel__indicators {
    bottom: -25px;
  }

  .el-carousel__button {
    width: 24px;
    border-radius: 3px;
  }

  .el-carousel__item {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-loading-mask {
    background-color: var(--el-mask-color);
  }

  .drawer-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 16px;
    background-color: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-lighter);
  }

  // 打印区域默认隐藏
  .print-area {
    display: none;
  }

  // 优化轮播图样式
  .el-carousel--card {
    // 通用轮播图样式
    .el-carousel__container {
      padding: 0;
    }

    .el-carousel__item {
      width: 50%;
      overflow: hidden;
      transition: all 0.4s ease;

      &.is-in-stage {
        z-index: 1;
        transform: translateX(0);

        &.is-hover {
          transform: translateY(-2px) scale(1.01);
        }

        &:not(.is-active) {
          cursor: pointer;
          transform: scale(0.83);
        }
      }

      &:not(.is-in-stage) {
        z-index: 0;
        opacity: 0;
        transform: translateX(-50%);
      }

      &.is-active {
        z-index: 2;
      }
    }

    // 两张图片的特殊处理
    &.el-carousel--2 {
      .el-carousel__container {
        display: flex !important;
        gap: 16px;
        justify-content: space-between;
        padding: 0 !important;

        .el-carousel__item {
          position: relative !important;
          width: calc(50% - 8px) !important;
          margin: 0 !important;
          transform: none !important;

          &.is-active {
            z-index: 1;
          }
        }
      }
    }
  }

  // 添加一个辅助类来处理两张图片的情况
  .carousel-wrapper {
    &.has-two-images {
      .el-carousel--card {
        .el-carousel__arrow {
          display: none;
        }

        .el-carousel__mask {
          display: none;
        }

        .el-carousel__indicators {
          display: none;
        }
      }
    }
  }

  // 图片容器样式优化
  .image-card {
    height: 100%;
    overflow: hidden;
    background: var(--el-fill-color-light);
    border-radius: 4px;

    .el-image {
      width: 100%;
      height: 100%;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  // 轮播箭头样式优化
  .el-carousel__arrow {
    width: 36px;
    height: 36px;
    font-size: 16px;
    background-color: rgb(0 0 0 / 30%);
    border: none;

    &:hover {
      background-color: rgb(0 0 0 / 50%);
    }
  }

  // 指示器样式优化
  .el-carousel__indicators {
    bottom: -25px;

    .el-carousel__button {
      width: 24px;
      border-radius: 3px;
    }
  }
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;

  .image-item {
    aspect-ratio: 16/9;
    overflow: hidden;
    cursor: pointer;
    background: var(--el-fill-color-light);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
      transform: translateY(-2px);
    }

    .el-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

.image-stack {
  position: relative;
  width: 100%;
  overflow: hidden; /* 防止图片溢出容器 */
}

.image-card {
  position: absolute;
  width: 100%;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.image-card:hover {
  transform: translateY(-10px) scale(1.05); /* 鼠标悬停时向上移动并稍微放大 */
}

.el-carousel-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 修改视频播放器相关样式 */
.video-dialog {
  :deep(.el-dialog) {
    overflow: hidden;
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    padding: 16px 20px;
    margin: 0;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-lighter);

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .el-dialog__headerbtn {
      top: 16px;
      right: 16px;
    }

    .dialog-title {
      display: flex;
      gap: 12px;
      align-items: center;

      .location {
        display: inline-block;
        flex: 1;
        margin-right: 10px;
        overflow: hidden;
        font-size: 15px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    background: #000;

    .video-player {
      width: 100%;
      max-height: 70vh;
      aspect-ratio: 16/9;

      :deep(.video-js) {
        width: 100%;
        height: 100%;

        .vjs-big-play-button {
          top: 50%;
          left: 50%;
          width: 60px;
          height: 60px;
          margin: 0;
          line-height: 60px;
          border: 2px solid #fff;
          border-radius: 50%;
          transform: translate(-50%, -50%);

          .vjs-icon-placeholder::before {
            line-height: 56px;
          }
        }

        .vjs-control-bar {
          background-color: rgb(0 0 0 / 70%);
        }
      }
    }
  }
}

/* 图片加载状态样式 */
.image-loading-state {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 50%, #f5f7fa 100%);

  .loading-skeleton {
    position: absolute;
    inset: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgb(64 158 255 / 10%) 50%,
      transparent 100%
    );

    .skeleton-shimmer {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgb(64 158 255 / 15%) 25%,
        rgb(64 158 255 / 30%) 50%,
        rgb(64 158 255 / 15%) 75%,
        transparent 100%
      );
      animation: shimmer 2s infinite;
    }
  }

  .loading-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .loading-spinner {
    position: relative;
    width: 40px;
    height: 40px;

    .spinner-ring {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1.5s linear infinite;

      &:nth-child(1) {
        border-top-color: var(--el-color-primary);
        animation-delay: 0s;
      }

      &:nth-child(2) {
        top: 10%;
        left: 10%;
        width: 80%;
        height: 80%;
        border-right-color: var(--el-color-primary);
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        top: 20%;
        left: 20%;
        width: 60%;
        height: 60%;
        border-bottom-color: var(--el-color-primary);
        animation-delay: 0.6s;
      }
    }
  }

  .loading-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-primary);
    letter-spacing: 0.5px;
  }
}

/* 图片错误状态样式 */
.image-error-state {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 50%, #fef0f0 100%);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #fde2e2 0%, #fca5a5 50%, #fde2e2 100%);

    .error-icon-wrapper {
      transform: scale(1.1);
    }
  }

  .error-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgb(239 68 68 / 10%);
    border: 2px solid rgb(239 68 68 / 30%);
    border-radius: 50%;
    transition: transform 0.3s ease;

    .error-icon {
      font-size: 24px;
      color: var(--el-color-danger);
    }
  }

  .error-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-danger);
  }

  .error-retry {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  &:hover .error-retry {
    opacity: 1;
  }
}

/* 图片容器上下布局样式 */
.image-container-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .image-section {
    width: 100%;

    .image-section-title {
      display: flex;
      align-items: center;
      padding: 8px 0;
      margin-bottom: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      .section-title-text {
        position: relative;
        padding-left: 12px;
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 3px;
          height: 14px;
          content: "";
          background: var(--el-color-primary);
          border-radius: 2px;
          transform: translateY(-50%);
        }
      }
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      @media (width <= 768px) {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
      }

      .image-item {
        aspect-ratio: 16/9;
        overflow: hidden;
        cursor: pointer;
        background: var(--el-fill-color-light);
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
          transform: translateY(-2px);
        }

        .el-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
}

/* 图片网格标题样式（保持兼容性） */
.image-grid-title {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .grid-title-text {
    position: relative;
    padding-left: 12px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 3px;
      height: 14px;
      content: "";
      background: var(--el-color-primary);
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}
</style>
