<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
    class="p-4"
  >
    <el-form-item label="时间范围" prop="dateRange">
      <el-date-picker
        v-model="formData.dateRange"
        type="daterange"
        unlink-panels
        range-separator="到"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[
          new Date(2000, 1, 1, 0, 0, 0),
          new Date(2000, 1, 1, 23, 59, 59)
        ]"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance, FormRules } from "element-plus";

const formRef = ref<FormInstance>();
const formData = reactive({
  dateRange: []
});

const rules = reactive<FormRules>({
  dateRange: [
    {
      required: true,
      message: "请选择时间范围",
      trigger: "change",
      type: "array" as const
    }
  ]
});

defineExpose({
  formRef,
  formData
});
</script>
