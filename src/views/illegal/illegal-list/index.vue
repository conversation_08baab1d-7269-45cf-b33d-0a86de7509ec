<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import DetailDrawer from "./components/detail-drawer.vue";
import tree from "./tree.vue";
import useIllegal from "./utils/hook";
import DownLoad from "@iconify-icons/ep/download";
import Printer from "@iconify-icons/ep/printer";
import Refresh from "@iconify-icons/ep/refresh";
import View from "@iconify-icons/ep/view";
import Delete from "@iconify-icons/ep/delete";
import { ref } from "vue";

defineOptions({
  name: "IllegalList"
});
const filterRef = ref();
const {
  options,
  loading,
  filterParams,
  dataList,
  columns,
  selectedNum,
  pagination,
  hiddenTree,
  treeData,
  treeLoading,
  buttonClass,
  onTreeSelect,
  onSearch,
  resetFilterForm,
  previewIllegalDetail,
  dispatchCounselor,
  onSelectionCancel,
  onbatchDel,
  handleSelectionChange,
  handleSizeChange,
  handleCurrentChange,
  drawerVisible,
  currentDetail,
  handleDelete,
  exportData,
  printData
} = useIllegal();
</script>

<template>
  <div :class="['flex', 'justify-between', 'relative']">
    <tree
      ref="treeRef"
      :class="[
        'mr-2',
        'transition-all duration-300 ease-in-out overflow-hidden',
        hiddenTree ? 'w-0 opacity-0' : 'w-[300px] opacity-100'
      ]"
      :treeData="treeData"
      :treeLoading="treeLoading"
      @tree-select="onTreeSelect"
    />
    <div
      :class="[
        'transition-all duration-300 ease-in-out relative',
        hiddenTree ? 'w-full' : 'w-[calc(100%-320px)]'
      ]"
    >
      <div
        class="toggle-bar"
        :style="{
          left: '-12px'
        }"
        @click="hiddenTree = !hiddenTree"
      >
        <el-icon
          class="toggle-icon"
          :class="hiddenTree ? '' : 'transform rotate-180'"
        >
          <ArrowLeftBold />
        </el-icon>
      </div>

      <el-form
        ref="filterRef"
        :inline="true"
        :model="filterParams"
        label-width="100px"
        class="search-form bg-bg_color w-[99/100] pt-[12px] overflow-auto"
      >
        <el-form-item label="违法类型：" prop="status">
          <el-select
            v-model="filterParams.illegalType"
            placeholder="请选择"
            clearable
            class="!w-[180px]"
          >
            <el-option
              v-for="item in options.traffic"
              :key="item.desc"
              :label="item.desc"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否有车牌：" prop="status">
          <el-select
            v-model="filterParams.involvePlate"
            placeholder="请选择"
            clearable
            class="!w-[180px]"
          >
            <el-option label="有车牌" :value="1" />
            <el-option label="无车牌" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号：" prop="status">
          <el-input
            v-model="filterParams.plateNumber"
            class="!w-[180px]"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="乘坐人数：" prop="status">
          <el-input
            v-model="filterParams.numberOfPassengers"
            class="!w-[180px]"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="现场劝导：" prop="status">
          <el-select
            v-model="filterParams.persuasiveBehavior"
            placeholder="请选择"
            clearable
            class="!w-[180px]"
          >
            <el-option
              v-for="item in options.persuasion"
              :key="item.desc"
              :label="item.desc"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="修改状态：" prop="whetherToModify">
          <el-select
            v-model="filterParams.whetherToModify"
            placeholder="请选择"
            clearable
            class="!w-[180px]"
          >
            <el-option label="已修改" :value="1" />
            <el-option label="未修改" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="查询范围：" prop="dateRange">
          <el-date-picker
            v-model="filterParams.dateRange"
            class="!w-[280px]"
            type="daterange"
            unlink-panels
            range-separator="到"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>

        <el-form-item class="ml-4">
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            @click="resetFilterForm(filterRef)"
          >
            重置
          </el-button>
          <el-button
            :icon="useRenderIcon(DownLoad)"
            :loading="loading"
            @click="exportData"
          >
            导出
          </el-button>
          <el-button :icon="useRenderIcon(Printer)" @click="printData">
            打印
          </el-button>
        </el-form-item>
      </el-form>

      <PureTableBar
        title=" "
        :columns="columns"
        :titleSolt="false"
        @refresh="onSearch"
      >
        <template v-slot="{ size }">
          <div
            v-if="selectedNum > 0"
            v-motion-fade
            class="bg-[var(--el-fill-color-light)] w-full h-[46px] mb-2 pl-4 flex items-center"
          >
            <div class="flex-auto">
              <span
                style="font-size: var(--el-font-size-base)"
                class="text-[rgba(42,46,54,0.5)] dark:text-[rgba(220,220,242,0.5)]"
              >
                已选 {{ selectedNum }} 项
              </span>
              <el-button type="primary" text @click="onSelectionCancel">
                取消选择
              </el-button>
            </div>
            <el-popconfirm title="是否确认删除?" @confirm="onbatchDel">
              <template #reference>
                <el-button type="danger" text class="mr-1">
                  批量删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
          <pure-table
            ref="tableRef"
            :border="true"
            row-key="id"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="dataList"
            :columns="columns"
            :pagination="{ ...pagination, size }"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="handleSelectionChange"
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          >
            <template #operation="{ row }">
              <el-button
                v-permission="'/illegal/list/IllegalRecords/selectDetails'"
                class="reset-margin"
                :disabled="JSON.parse(row.pictureUrl).length === 0"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon(View)"
                @click="previewIllegalDetail(row)"
              >
                查看详情
              </el-button>
              <el-popconfirm
                title="是否确认删除此违法记录？"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button
                    v-permission="'/illegal/list/IllegalRecords/delete'"
                    class="reset-margin"
                    link
                    type="danger"
                    :size="size"
                    :icon="useRenderIcon(Delete)"
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
              <!-- <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon(Pointer)"
                @click="dispatchCounselor(row)"
              >
                违法下派
              </el-button> -->
            </template>
          </pure-table>
        </template>
      </PureTableBar>

      <detail-drawer
        v-model="drawerVisible"
        :currentId="currentDetail?.uuid || ''"
        :list="dataList?.map(item => item.uuid) || []"
        @refresh="onSearch"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
@import url("./index.scss");
</style>
