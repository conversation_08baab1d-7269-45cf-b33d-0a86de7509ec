<script setup lang="ts">
import { getDispatchUserList } from "@/api/illegal";
import ReCol from "@/components/ReCol";
import { User } from "@/views/system/system-user/utils/types";
import { onMounted, ref } from "vue";
import { formRules } from "../utils/rule";
import { FormItemProp } from "../utils/type";

const props = withDefaults(defineProps<FormItemProp>(), {
  formInline: () => ({})
});

const formRef = ref();
const dispatchUsers = ref<User[]>([]);

const newFormInline = ref(props.formInline);
function getRef() {
  return formRef.value;
}

onMounted(() => {
  getDispatchUserList({
    city: props.row.city,
    county: props.row.county,
    hamlet: props.row.hamlet,
    site: props.row.site,
    township: props.row.township
  }).then(res => {
    if (res.code === 200) {
      dispatchUsers.value = res.data;
    }
  });
});

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="formRef"
    :rules="formRules"
    :model="newFormInline"
    label-width="80px"
  >
    <el-row :gutter="30">
      <re-col>
        <el-form-item label="劝导员" prop="userIds">
          <el-select
            v-model="newFormInline.userIds"
            placeholder="请选择"
            class="w-full"
            clearable
            multiple
          >
            <el-option
              v-for="(item, index) in dispatchUsers"
              :key="index"
              :value="item.userId"
              :label="item.name"
            >
              {{ item.name }}
            </el-option>
          </el-select>
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>
