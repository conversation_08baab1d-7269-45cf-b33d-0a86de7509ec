:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

:deep(.el-table__cell) {
  border-right: none;
}

:deep(.el-button:focus-visible) {
  outline: none;
}

:deep(.el-button.reset-margin) {
  margin-left: 2px;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button {
  cursor: pointer;
  border: 1px solid #e4e7ed;

  &:hover {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
}

:deep(.title-solt) {
  height: auto;
}

.toggle-bar {
  position: absolute;
  top: 50%;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 60px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #fff;
  border-right: none;
  border-radius: 4px 0 0 4px;
  box-shadow: -2px 0 6px -2px rgb(0 0 0 / 8%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(-50%);

  &:hover {
    background: var(--el-color-primary-light-9);

    .toggle-icon {
      color: var(--el-color-primary);
    }
  }

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: linear-gradient(
      to bottom,
      transparent 0%,
      var(--el-color-primary-light-9) 50%,
      transparent 100%
    );
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover::before {
    opacity: 0.3;
  }
}

.toggle-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.transform {
    transform: rotate(180deg);
  }
}

.transition-all {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
