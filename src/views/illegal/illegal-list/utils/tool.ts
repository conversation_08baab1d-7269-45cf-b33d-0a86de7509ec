import { ElTag } from "element-plus";
import { h } from "vue";
import type { IllegalRecord } from "./type";
import { formatEnumCellHandle } from "@/utils/translate";

export const formatTagCell = (
  row: IllegalRecord,
  filed: keyof Pick<
    IllegalRecord,
    | "illegalType"
    | "vehicleType"
    | "persuasiveBehavior"
    | "disposalStatus"
    | "plateColor"
  >,
  simple: boolean = false
) => {
  const fieldValue = row[filed];
  if (!fieldValue) return "无";

  const displayValue = formatEnumCellHandle(fieldValue);

  // 如果是简单模式（用于详情页），直接返回文本
  if (simple) return displayValue;

  // 如果是违法类型且在列表中显示，根据 disposalStatus.code 来决定颜色
  if (filed === "illegalType" && row.disposalStatus?.code) {
    const statusCode = row.disposalStatus.code;
    if ([1, 2, 3].includes(statusCode)) {
      return h(
        ElTag,
        {
          key: row.uuid,
          effect: "plain",
          style: {
            marginRight: "5px",
            marginBottom: "5px",
            color: "#FF0000",
            borderColor: "#FF0000"
          }
        },
        { default: () => displayValue }
      );
    }
  }

  // 其他字段使用原来的逻辑
  const displayColor = fieldValue.color;
  if (!displayColor) return displayValue;

  return h(
    ElTag,
    {
      key: row.uuid,
      effect: "plain",
      style: {
        marginRight: "5px",
        marginBottom: "5px",
        color: displayColor,
        borderColor: displayColor
      }
    },
    { default: () => displayValue }
  );
};
