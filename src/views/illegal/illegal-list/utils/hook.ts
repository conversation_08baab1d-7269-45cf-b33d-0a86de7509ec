import {
  dispatchIllega,
  getIllegalRecords,
  deleteIllegalRecord
} from "@/api/illegal";
import type { PaginationProps } from "@pureadmin/table";
import { computed, h, onMounted, reactive, ref } from "vue";
// import { api as viewerApi } from "v-viewer";
import { getAreaTree } from "@/api/system";
import { addDialog } from "@/components/ReDialog";
import { notHasAllPermission } from "@/directives/auth";
import { useDataStore } from "@/store/modules/data";
import { message } from "@/utils/message";
import { handleFormField } from "@/utils/tree";
import dispatchForm from "@/views/illegal/illegal-list/form/dispatch.vue";
import type { TreeNode } from "@/views/system/system-user/utils/types";
import dayjs from "dayjs";
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import PrintForm from "../components/print-form.vue";
import { formatTagCell } from "./tool";
import type { FilterParams, IllegalRecord } from "./type";

function useIllegal() {
  const dataStore = useDataStore();
  const formRef = ref();
  const filterParams = reactive<FilterParams>({
    involvePlate: 1
  });
  const dataList = ref();
  const loading = ref(false);
  const hiddenTree = ref(false);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100, 200],
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const selectedNum = ref(0);
  const treeData = ref([]);
  const selectedTreeNode = ref<TreeNode | null>(null);
  const treeLoading = ref(false);
  const buttonClass = computed(() => {
    return [
      "!h-[20px]",
      "reset-margin",
      "!text-gray-500",
      "dark:!text-white",
      "dark:hover:!text-primary"
    ];
  });

  const columns = ref<TableColumnList>([
    {
      label: "抓拍时间",
      width: 160,
      prop: "captureTime",
      formatter: ({ captureTime }) =>
        dayjs(captureTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "县",
      prop: "county",
      minWidth: 90,
      hide: true
    },
    {
      label: "乡镇",
      prop: "township",
      minWidth: 90,
      hide: true
    },
    {
      label: "村",
      prop: "hamlet",
      minWidth: 90,
      hide: true
    },
    {
      label: "点位",
      prop: "site",
      minWidth: 80
    },
    {
      label: "车牌号",
      prop: "plateNumber",
      width: 120
    },
    {
      label: "车牌颜色",
      prop: "plateColor",
      width: 120,
      formatter: row => formatTagCell(row, "plateColor")
    },
    {
      label: "车辆类型",
      prop: "vehicleType",
      width: 120,
      formatter: row => formatTagCell(row, "vehicleType")
    },
    {
      label: "违法类型",
      prop: "illegalType",
      width: 220,
      formatter: row => formatTagCell(row, "illegalType")
    },

    {
      label: "实载人数",
      prop: "numberOfPassengers",
      width: 120
    },
    {
      label: "现场劝导",
      prop: "effective",
      width: 120,
      formatter: row => formatTagCell(row, "persuasiveBehavior")
    },

    {
      label: "经度",
      prop: "longitude",
      minWidth: 120
    },
    {
      label: "纬度",
      prop: "latitude",
      minWidth: 120
    },
    {
      label: "操作",
      fixed: "right",
      width: 140,
      className: "cell-flex-row",
      slot: "operation"
    }
  ]);

  onMounted(() => {
    // 校验是否存在操作列表的按钮
    const isPermissionHandle = notHasAllPermission([
      "/illegal/list/IllegalRecords/selectDetails",
      "/illegal/list/IllegalRecords/delete"
    ]);
    if (isPermissionHandle) {
      columns.value = columns.value.filter(item => item.slot !== "operation");
    }
    onSearch();

    // 获取区域树
    getAreaTree()
      .then(res => {
        if (res.code === 200) {
          treeData.value = res.data;
        }
      })
      .finally(() => {
        treeLoading.value = false;
      });
  });

  const onSearch = () => {
    loading.value = true;
    // 处理筛选参数
    const applyFilterParams = {
      ...filterParams
    };
    if (filterParams.dateRange) {
      applyFilterParams.startTime = dayjs(filterParams.dateRange[0]).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      applyFilterParams.endTime = dayjs(filterParams.dateRange[1]).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
    delete applyFilterParams.dateRange;
    const nodeAreaField = handleFormField(
      selectedTreeNode.value,
      treeData.value,
      "label"
    );
    getIllegalRecords({
      curPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...nodeAreaField,
      ...applyFilterParams
    })
      .then(res => {
        if (res.code === 200) {
          dataList.value = res.data.records;
          pagination.total = res.data.total;
          pagination.pageSize = res.data.size;
          pagination.currentPage = res.data.current;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const onTreeSelect = treeData => {
    selectedTreeNode.value = treeData;
    onSearch();
  };

  /** 预览违法详情 */
  const previewIllegalDetail = (row: IllegalRecord) => {
    if (!row) return;
    currentDetail.value = row;
    drawerVisible.value = true;
  };

  /** 下派劝导员 */
  const dispatchCounselor = row => {
    // 选中的角色列表
    addDialog({
      title: `违法下派`,
      props: {
        formInline: {}
      },
      width: "400px",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      sureBtnLoading: true,
      contentRenderer: () =>
        h(dispatchForm, {
          ref: formRef,
          formInline: {},
          row
        }),
      beforeSure: (done, { options, closeLoading }) => {
        const FormRef = formRef.value.getRef();
        FormRef.validate(valid => {
          if (valid) {
            const userIds = options.props.formInline.userIds;
            dispatchIllega({
              uuid: row.uuid,
              userId: userIds,
              illegalName: row.illegalName
            })
              .then(res => {
                if (res.code === 200) {
                  message("违法下派成功，请等待劝导员处理！", {
                    type: "success"
                  });
                  done();
                  onSearch();
                }
              })
              .finally(() => {
                closeLoading();
              });
          } else {
            closeLoading();
          }
        });
      }
    });
  };

  const resetFilterForm = filterFormRef => {
    if (!filterFormRef) return;
    filterFormRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    filterParams.involvePlate = 1;
    onSearch();
  };
  function handleSelectionChange() {}

  function onSelectionCancel() {}

  function onbatchDel() {}

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  // 添加控制抽屉显示的状态
  const drawerVisible = ref(false);
  const currentDetail = ref<IllegalRecord>({} as IllegalRecord);

  /** 删除违法记录 */
  const handleDelete = async (row: IllegalRecord) => {
    try {
      const res = await deleteIllegalRecord(row.uuid);
      if (res.code === 200) {
        message("删除成功", { type: "success" });
        onSearch(); // 刷新列表
      } else {
        message("删除失败", { type: "error" });
      }
    } catch (error) {
      console.error("删除失败:", error);
      message("删除失败，请重试", { type: "error" });
    }
  };

  /** 导出Excel */
  const exportData = () => {
    if (!dataList.value?.length) {
      message("暂无数据可导出", { type: "warning" });
      return;
    }

    // 定义要导出的列
    const exportColumns = [
      { title: "车牌号", dataIndex: "plateNumber" },
      {
        title: "违法类型",
        dataIndex: "illegalType",
        format: row => formatTagCell(row, "illegalType", true)
      },
      {
        title: "车辆类型",
        dataIndex: "vehicleType",
        format: row => formatTagCell(row, "vehicleType", true)
      },
      { title: "实载人数", dataIndex: "numberOfPassengers" },
      {
        title: "现场劝导",
        dataIndex: "persuasiveBehavior",
        format: row => formatTagCell(row, "persuasiveBehavior", true)
      },
      { title: "县", dataIndex: "county" },
      { title: "乡镇", dataIndex: "township" },
      { title: "村", dataIndex: "hamlet" },
      { title: "点位", dataIndex: "site" },
      {
        title: "抓拍时间",
        dataIndex: "captureTime",
        format: row => dayjs(row.captureTime).format("YYYY-MM-DD HH:mm:ss")
      },
      {
        title: "处理状态",
        dataIndex: "disposalStatus",
        format: row => formatTagCell(row, "disposalStatus", true)
      }
    ];

    // 转换数据格式
    const excelData = dataList.value.map(row => {
      const rowData = {};
      exportColumns.forEach(col => {
        rowData[col.title] = col.format ? col.format(row) : row[col.dataIndex];
      });
      return rowData;
    });

    // 创建工作簿
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "违法记录");

    // 设置列宽
    const colWidths = exportColumns.map(() => ({ wch: 15 }));
    worksheet["!cols"] = colWidths;

    // 生成文件并下载
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array"
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });

    // 生成文件名：违法记录-年月日时秒.xlsx
    const fileName = `违法记录-${dayjs().format("YYYYMMDDHHmmss")}.xlsx`;
    saveAs(blob, fileName);
  };

  /** 打印数据 */
  const printData = () => {
    addDialog({
      title: "打印设置",
      width: "500px",
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => h(PrintForm, { ref: formRef }),
      beforeSure: (done, { closeLoading }) => {
        const FormRef = formRef.value.formRef;
        FormRef.validate(async valid => {
          if (valid) {
            const { dateRange } = formRef.value.formData;

            // 使用 onSearch 的请求逻辑获取数据
            loading.value = true;
            try {
              const res = await getIllegalRecords({
                startTime: dateRange[0],
                endTime: dateRange[1],
                curPage: 1,
                pageSize: 9999
              });

              if (res.code === 200 && res.data.records?.length) {
                // 创建打印内容
                const printContent = `
                  <!DOCTYPE html>
                  <html>
                    <head>
                      <title>违法记录列表</title>
                      <meta charset="utf-8">
                      <style>
                        body {
                          padding: 20px;
                          font-family: Arial, sans-serif;
                        }
                        .print-title {
                          text-align: center;
                          font-size: 18px;
                          font-weight: bold;
                          margin-bottom: 20px;
                        }
                        .print-subtitle {
                          text-align: center;
                          font-size: 14px;
                          color: #666;
                          margin-bottom: 20px;
                        }
                        .print-table {
                          width: 100%;
                          border-collapse: collapse;
                          margin-bottom: 20px;
                        }
                        .print-table th,
                        .print-table td {
                          border: 1px solid #ddd;
                          padding: 8px;
                          text-align: left;
                          font-size: 14px;
                        }
                        .print-table th {
                          background-color: #f5f5f5;
                          font-weight: bold;
                        }
                        @media print {
                          body {
                            padding: 0;
                          }
                          .print-table th {
                            background-color: #f5f5f5 !important;
                            -webkit-print-color-adjust: exact;
                          }
                        }
                      </style>
                    </head>
                    <body>
                      <div class="print-title">违法记录列表</div>
                      <div class="print-subtitle">
                        统计时间：${dateRange[0]} 至 ${dateRange[1]}
                      </div>
                      <table class="print-table">
                        <thead>
                          <tr>
                            <th>车牌号</th>
                            <th>违法类型</th>
                            <th>车辆类型</th>
                            <th>实载人数</th>
                            <th>现场劝导</th>
                            <th>点位</th>
                            <th>抓拍时间</th>
                            <th>处理状态</th>
                          </tr>
                        </thead>
                        <tbody>
                          ${res.data.records
                            .map(
                              row => `
                            <tr>
                              <td>${row.plateNumber}</td>
                              <td>${formatTagCell(row, "illegalType", true)}</td>
                              <td>${formatTagCell(row, "vehicleType", true)}</td>
                              <td>${row.numberOfPassengers}</td>
                              <td>${formatTagCell(row, "persuasiveBehavior", true)}</td>
                              <td>${row.site || "-"}</td>
                              <td>${dayjs(row.captureTime).format("YYYY-MM-DD HH:mm:ss")}</td>
                              <td>${formatTagCell(row, "disposalStatus", true)}</td>
                            </tr>
                          `
                            )
                            .join("")}
                        </tbody>
                      </table>
                    </body>
                  </html>
                `;

                // 创建一个隐藏的 iframe
                const iframe = document.createElement("iframe");
                iframe.style.display = "none";
                document.body.appendChild(iframe);

                // 写入打印内容
                iframe.contentDocument.write(printContent);
                iframe.contentDocument.close();

                // 等待图片和样式加载完成后打印
                iframe.onload = () => {
                  try {
                    iframe.contentWindow.print();
                    // 打印完成后移除 iframe
                    setTimeout(() => {
                      document.body.removeChild(iframe);
                    }, 100);
                    done(); // 关闭弹窗
                  } catch (e) {
                    console.error("打印失败:", e);
                    message("打印失败，请重试", { type: "error" });
                  }
                };
              } else {
                message("所选时间范围内无数据", { type: "warning" });
              }
            } catch (error) {
              console.error("获取数据失败:", error);
              message("获取数据失败，请重试", { type: "error" });
            } finally {
              loading.value = false;
              closeLoading();
            }
          } else {
            closeLoading();
          }
        });
      }
    });
  };

  return {
    options: dataStore.options,
    loading,
    filterParams,
    dataList,
    selectedNum,
    columns,
    pagination,
    hiddenTree,
    treeData,
    treeLoading,
    buttonClass,
    onTreeSelect,
    onSearch,
    previewIllegalDetail,
    dispatchCounselor,
    resetFilterForm,
    handleSelectionChange,
    onSelectionCancel,
    onbatchDel,
    handleSizeChange,
    handleCurrentChange,
    drawerVisible,
    currentDetail,
    handleDelete,
    exportData,
    printData
  };
}

export default useIllegal;
