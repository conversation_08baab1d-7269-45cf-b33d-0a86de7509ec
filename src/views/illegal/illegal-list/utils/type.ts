import type { Option } from "@/store/types";
import type { Area } from "@/views/system/system-user/utils/types";

export interface FilterParams extends Area {
  /** 违法类型 */
  illegalType?: string;
  /** 是否有车牌 */
  involvePlate?: number;
  /** 车牌号 */
  plateNumber?: string;
  /** 乘坐人数 */
  numberOfPassengers?: number;
  /** 是否进行劝导动作 */
  persuasiveBehavior?: string;
  /** 处理状态 */
  disposalStatus?: string;
  /** 是否修改 */
  whetherToModify?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  dateRange?: string[];
}

export interface IllegalRecord {
  uuid: string;
  /** 违法类型 */
  illegalType: Option;
  /** 是否有车牌 */
  vehicleType: Option;
  /** 车牌号 */
  plateNumber: string;
  /** 车牌颜色 */
  plateColor: Option;
  /** 乘坐人数 */
  numberOfPassengers: number;
  /** 违法图片，需要json序列表 */
  pictureUrl: string;
  /** 是否进行劝导动作 */
  persuasiveBehavior: Option;
  /** 处理状态 */
  disposalStatus: Option;
  /** 劝导结果 */
  effective: 0 | 1;
  /** 市 */
  city: string | null;
  /** 县 */
  county: string | null;
  /** 乡镇 */
  township: string | null;
  /** 村 */
  hamlet: string | null;
  /** 点位 */
  site: string | null;
  /** 抓拍时间 */
  captureTime: string;
  /** 创建时间 */
  createTime: string;
  /** 经度 */
  longitude: string;
  /** 纬度 */
  latitude: string;
  /** 视频地址 */
  videoUrl: string;
  /** 实际车辆类型 */
  actualVehicleType?: Option;
  /** 实际车牌颜色 */
  actualPlateColor?: Option;
  /** 实际车牌号 */
  actualPlateNumber?: string;
  /** 实际违法类型 */
  actualIllegalType?: Option;
  /** 实际乘坐人数 */
  actualNumberOfPassengers?: number;
  /** 实际劝导动作 */
  actualPersuasiveBehavior?: Option;
  /** 实际违法图片，需要json序列表 */
  actualPictureUrl?: string;
  /** 审核状态 */
  auditStatus?: number;
}

export interface FormItemProp {
  formInline: {
    userIds?: number[];
  };
  row: IllegalRecord;
}

export interface HandleResult {
  /** UUID */
  uuid: string;
  /** 违法名称 */
  illegalName: string;
  /** 违法记录id */
  illegalRecordsUuid: string;
  /** 预处理人员 */
  userId: number;
  /** 预处理人员姓名 */
  userName: string;
  /** 处理方式 */
  disposalMethod: string;
  /** 处理状态(0未处理，1已处理) */
  disposalStatus: 0 | 1;
  /** 拍照图片地址(需要json序列化为一个数组) */
  imgl: string;
  /** 实际处理人员 */
  actualProcessing: number;
  /** 实际处理人员姓名 */
  actualProcessingName: string;
  /** 备注 */
  remarks: string;
  /** 处理时间 YYYY-MM-DD HH:mm:ss */
  processingTime: string;
  /** 截止处理时间 YYYY-MM-DD HH:mm:ss */
  deadlineTime: string;
  /** 创建时间 YYYY-MM-DD HH:mm:ss */
  createTime: string;
  /** 视频播放地址 */
  videoUrl?: string;
}

export interface IllegalResultDto {
  illegalRecords: IllegalRecord;
  accuratePersuasion?: HandleResult;
}
