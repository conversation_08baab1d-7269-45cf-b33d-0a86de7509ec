import type { UploadUserFile } from "element-plus";

// 表单数据类型
export interface DispatchForm extends LocationPath {
  illegalName: string;
  userIds: string;
  userNames: string;
  deadlineTime: string;
  locationIds: number[];
  pictureUrls: string[];
}

// 位置路径类型
export interface LocationPath {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}

// 文件类型
export type FileItem = UploadUserFile;

// 人员列表项类型
export interface StaffItem {
  text: string;
  value: number;
  userId: number;
  name: string;
  [key: string]: any;
}

// 地区树节点类型
export interface AreaTreeNode {
  id: number;
  label: string;
  parentId: number;
  childList?: AreaTreeNode[];
}

// 处理期限选项类型
export interface DeadlineOption {
  text: string;
  value: number;
}
