<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { Plus } from "@element-plus/icons-vue";
import { ref } from "vue";
import useIllegalDispatch from "./utils/hook";

const loading = ref(false);
const userStore = useUserStoreHook();

const {
  form,
  files,
  staffList,
  areaTree,
  showStaff,
  showTime,
  selectedDeadline,
  selectedStaffId,
  handleDeadlineChange,
  handleStaffChange,
  handleLocationChange,
  deadlineOptions,
  handleSubmit,
  handlePictureCardPreview,
  handleRemove,
  beforeUpload,
  customUpload
} = useIllegalDispatch();
</script>

<template>
  <div class="dispatch-wrapper">
    <div class="dispatch-content">
      <!-- 违法信息卡片 -->
      <div class="info-card">
        <div class="card-title">
          <span class="title-text">违法信息</span>
        </div>

        <div class="form-item">
          <span class="label required">违法名称</span>
          <el-input
            v-model="form.illegalName"
            type="textarea"
            :rows="4"
            placeholder="请输入违法名称"
            maxlength="200"
            show-word-limit
          />
        </div>

        <div class="form-item">
          <span class="label required">违法证据</span>
          <el-upload
            v-model:file-list="files"
            list-type="picture-card"
            :auto-upload="false"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :http-request="customUpload"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">只能上传jpg/png文件，且不超过5MB</div>
            </template>
          </el-upload>
        </div>
      </div>

      <!-- 分派信息卡片 -->
      <div class="form-card">
        <div class="card-title">
          <span class="title-text">分派信息</span>
        </div>

        <!-- 违法地点 -->
        <div class="form-item">
          <span class="label required">违法地点</span>
          <el-cascader
            v-model="form.locationIds"
            :options="userStore.getUserRegionTree"
            :props="{
              expandTrigger: 'hover',
              checkStrictly: true,
              label: 'label',
              value: 'id',
              children: 'childList'
            }"
            placeholder="选择违法地点"
            clearable
            @change="handleLocationChange"
          />
        </div>

        <!-- 处理人员 -->
        <div v-if="staffList.length > 0" class="form-item">
          <span class="label required">处理人员</span>
          <el-select
            v-model="selectedStaffId"
            placeholder="请选择处理人员"
            @change="handleStaffChange"
          >
            <el-option
              v-for="option in staffList"
              :key="option.userId"
              :label="option.name"
              :value="option.userId"
            />
          </el-select>
        </div>

        <!-- 处理期限 -->
        <div class="form-item">
          <span class="label required">处理期限</span>
          <el-select
            v-model="selectedDeadline"
            placeholder="请选择处理期限"
            @change="handleDeadlineChange"
          >
            <el-option
              v-for="option in deadlineOptions"
              :key="option.value"
              :label="option.text"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <!-- 提交按钮 -->
      <el-button
        type="primary"
        class="submit-btn"
        :loading="loading"
        @click="handleSubmit"
      >
        确认分派
      </el-button>
    </div>
  </div>
</template>

<style lang="scss">
@import url("./index.scss");
</style>
