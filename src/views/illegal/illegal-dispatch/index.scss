.dispatch-wrapper {
  min-height: 100vh;
  padding: 24px;
  background: #f5f7fa;

  .dispatch-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin: 0 auto;
  }

  .info-card,
  .form-card {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  }

  .card-title {
    padding-bottom: 16px;
    margin-bottom: 24px;
    border-bottom: 1px solid #ebeef5;

    .title-text {
      position: relative;
      padding-left: 12px;
      font-size: 18px;
      font-weight: 500;
      color: #303133;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: #409eff;
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }

  .form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;

      &.required::before {
        margin-right: 4px;
        color: #f56c6c;
        content: "*";
      }
    }

    .el-cascader,
    .el-select,
    .el-input {
      width: 100%;
    }

    .empty-staff {
      padding: 32px;
      text-align: center;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }

  .el-upload {
    --el-upload-picture-card-size: 120px;
  }

  .submit-btn {
    align-self: center;
    min-width: 120px;
    margin-top: 12px;
  }
}

// 自定义级联选择器下拉菜单样式
:deep(.el-cascader-panel) {
  .el-cascader-menu {
    min-width: 200px;
  }

  .el-cascader-menu__wrap {
    height: 280px;
  }
}

// 自定义上传组件样式
:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 120px;

  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    background-color: #ecf5ff;
    border-color: #409eff;
  }
}

// 自定义空状态样式
:deep(.el-empty) {
  padding: 40px 0;

  .el-empty__description {
    margin-top: 16px;
    color: #909399;
  }
}
