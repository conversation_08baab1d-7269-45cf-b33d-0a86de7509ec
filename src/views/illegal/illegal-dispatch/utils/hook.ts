// import { api as viewerApi } from "v-viewer";

import { dispatchIllegal } from "@/api/illegal";
import { getUserList } from "@/api/system";
import { useUserStoreHook } from "@/store/modules/user";
import type { AddressLevel } from "@/types/business";
import type { TreeNode, User } from "@/views/system/system-user/utils/types";
import type {
  UploadFile,
  UploadFiles,
  UploadRawFile,
  UploadUserFile
} from "element-plus";
import { ElLoading, ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import type {
  AreaTreeNode,
  DeadlineOption,
  DispatchForm,
  FileItem
} from "../types";

export default function useIllegalDispatch() {
  // 表单数据
  const form = reactive<DispatchForm>({
    illegalName: "",
    userIds: "",
    userNames: "",
    deadlineTime: "",
    locationIds: [],
    pictureUrls: []
  });

  const selectedDeadline = ref<number>();
  const selectedStaffId = ref<number>();

  // 状态数据
  const files = ref<FileItem[]>([]);
  const staffList = ref<User[]>([]);
  const areaTree = ref<AreaTreeNode[]>([]);

  // 弹窗控制
  const showStaff = ref(false);
  const showTime = ref(false);

  // 处理期限选项
  const deadlineOptions: DeadlineOption[] = [
    { text: "1天", value: 1 },
    { text: "3天", value: 3 },
    { text: "5天", value: 5 },
    { text: "7天", value: 7 }
  ];
  const userStore = useUserStoreHook();

  onMounted(() => {
    userStore.initUserRegionTree();
  });

  // 图片上传相关方法
  const handlePictureCardPreview = (file: UploadFile) => {
    window.open(file.url);
  };

  const handleRemove = (file: UploadFile, fileList: UploadFiles) => {
    files.value = fileList as UploadUserFile[];
  };

  // 处理人员变化
  const handleStaffChange = v => {
    form.userIds = v;
    const staffItem = staffList.value.find(staff => staff.userId === v);
    form.userNames = staffItem?.name;
  };

  // 处理地点选择变化
  const handleLocationChange = (value: number[]) => {
    if (value?.length) {
      const areaPath = getAddressParams(value);
      Object.keys(areaPath).forEach(key => {
        form[key] = areaPath[key];
      });
      getUserList({
        curPage: 1,
        pageSize: 1000,
        ...areaPath
      }).then(res => {
        if (res.code === 200) {
          staffList.value = res.data.records;
        }
      });
    } else {
      staffList.value = [];
      form.userIds = "";
      form.userNames = "";
    }
  };

  // 处理期限选择变化
  const handleDeadlineChange = (days: number) => {
    // 计算截止日期
    const deadline = new Date();
    deadline.setDate(deadline.getDate() + days);
    deadline.setHours(23, 59, 59); // 设置为当天的最后一秒

    form.deadlineTime = formatDateTime(deadline);
  };

  const formatDateTime = date => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // 获取地址参数
  const getAddressParams = (areaIds: number[]): AddressLevel => {
    const params: AddressLevel = {
      city: userStore.userInfo.city
    };
    if (!areaIds?.length) return params;
    let currentNode = userStore.userRegionTree.find(
      item => item.id === areaIds[0]
    );
    if (!currentNode) return {};
    const levels = ["county", "township", "hamlet", "site"] as const;
    for (let i = 0; i < areaIds.length; i++) {
      const id = areaIds[i + 1];
      if (!currentNode) break;

      const level = levels[i];
      params[level] = currentNode.label;
      currentNode = findNodeById(currentNode.childList, id);
    }
    return params;
  };

  // 根据ID查找节点
  const findNodeById = (
    nodes: TreeNode[] | null,
    id: number
  ): TreeNode | null => {
    if (!nodes) return null;

    for (const node of nodes) {
      if (node.id === id) return node;
      const found = findNodeById(node.childList, id);
      if (found) return found;
    }

    return null;
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith("image/");
    if (!isImage) {
      ElMessage.error("只能上传图片文件!");
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      ElMessage.error("图片大小不能超过 5MB!");
      return false;
    }
    return true;
  };

  const customUpload = async (params: { file: File }) => {
    // 创建一个符合 UploadRawFile 类型的文件对象
    const rawFile = params.file as UploadRawFile;
    rawFile.uid = Date.now(); // 直接使用数字类型的时间戳

    const newFile: UploadUserFile = {
      name: params.file.name,
      url: URL.createObjectURL(params.file),
      raw: rawFile
    };
    files.value.push(newFile);
  };

  // 表单验证
  const validateForm = () => {
    if (!form.illegalName) {
      ElMessage.warning("请输入违法说明");
      return false;
    }
    if (!files.value.length) {
      ElMessage.warning("请上传违法证据图片");
      return false;
    }
    return true;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return;

    const loading = ElLoading.service({
      text: "提交中...",
      background: "rgba(0, 0, 0, 0.7)"
    });

    try {
      const formData = new FormData();

      // 处理文件上传
      files.value.forEach(file => {
        if (file.raw) {
          formData.append("files", file.raw);
        }
      });

      // 添加其他表单数据
      formData.append("IllegalName", form.illegalName);
      formData.append("userIds", form.userIds);
      formData.append("userNames", form.userNames);
      formData.append("termTime", form.deadlineTime);

      // 添加地点信息
      formData.append("city", form.city || "");
      formData.append("county", form.county || "");
      formData.append("township", form.township || "");
      formData.append("hamlet", form.hamlet || "");
      formData.append("site", form.site || "");
      const formDataObject = {};
      formData.forEach((value, key) => {
        formDataObject[key] = value;
      });
      const res = await dispatchIllegal(formData);

      if (res.code === 200) {
        ElMessage.success("违法分派成功");
        files.value = [];
        selectedDeadline.value = undefined;
        selectedStaffId.value = undefined;
        Object.keys(form).forEach(key => {
          if (typeof form[key] === "object") {
            form[key] = [];
          } else {
            form[key] = "";
          }
        });
      } else {
        ElMessage.error("分派失败");
      }
    } finally {
      loading.close();
    }
  };

  return {
    form,
    selectedDeadline,
    selectedStaffId,
    files,
    staffList,
    areaTree,
    showStaff,
    showTime,
    deadlineOptions,
    handleStaffChange,
    handleSubmit,
    handlePictureCardPreview,
    handleRemove,
    beforeUpload,
    customUpload,
    handleLocationChange,
    handleDeadlineChange
  };
}
