export interface FilterParams {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
  examine?: string;
}

export interface FaceReviewDetail {
  id: number;
  equipmentnumber: string; // 设备编号
  examineUrl: string; // 人脸图片URL
  examine: string; // 审核状态
  reviewer: string; // 审核人
  bindingUsername: string; // 绑定人员姓名
  createTime: string; // 提交时间
  reviewerTime: string; // 审核时间
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
  bindUser?: {
    userId: number;
    phone: string;
    name: string;
    sex: string;
    deptName: string;
    city: string;
    county: string;
    township: string;
    hamlet: string;
    site: string;
    state: number;
    createTime: string;
    roles: any[];
  };
}

export interface User {
  userId: number;
  phone: string;
  name: string;
  sex: string;
  deptName: string;
  city: string;
  county: string;
  township: string;
  hamlet: string;
  site: string;
  state: number;
  createTime: string;
  roles: any[];
}
