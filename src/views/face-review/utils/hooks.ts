import { getFaceReviewList } from "@/api/face-review";
import { getAreaTree } from "@/api/system";
import { message } from "@/utils/message";
import { handleFormField } from "@/utils/tree";
import type { TreeNode } from "@/views/system/system-user/utils/types";
import type { PaginationProps, TableColumns } from "@pureadmin/table";
import dayjs from "dayjs";
import { h, onMounted, reactive, ref, resolveComponent } from "vue";
import type { FaceReviewDetail, FilterParams } from "./types";

export function useFaceReview() {
  const filterParams = reactive<FilterParams>({});
  const dataList = ref<FaceReviewDetail[]>([]);
  const loading = ref(false);
  const drawerVisible = ref(false);
  const currentDetail = ref<FaceReviewDetail | null>(null);
  const treeData = ref<TreeNode[]>([]);
  const selectedTreeNode = ref<TreeNode | null>(null);
  const treeLoading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns: TableColumns[] = [
    {
      label: "提交设备",
      prop: "equipmentnumber",
      width: 180
    },
    {
      label: "所属地区",
      prop: "site",
      minWidth: 180
    },
    {
      label: "提交时间",
      prop: "createTime",
      width: 180,
      formatter: ({ createTime }) =>
        createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss") : "-"
    },
    {
      label: "审核人",
      prop: "reviewer",
      width: 120
    },
    {
      label: "绑定人员姓名",
      prop: "bindingUsername",
      width: 180
    },

    {
      label: "审核时间",
      prop: "reviewerTime",
      width: 180,
      formatter: ({ reviewerTime }) =>
        reviewerTime ? dayjs(reviewerTime).format("YYYY-MM-DD HH:mm:ss") : "-"
    },
    {
      label: "审核状态",
      prop: "examine",
      width: 100,
      cellRenderer: ({ row }) => {
        const statusMap = {
          未审核: {
            text: "待审核",
            type: "warning"
          },
          通过: {
            text: "通过",
            type: "success"
          },
          未通过: {
            text: "未通过",
            type: "danger"
          }
        };

        const status = statusMap[row.examine] || {
          text: row.examine,
          type: "info"
        };

        return h(
          resolveComponent("el-tag"),
          {
            type: status.type,
            effect: "light",
            size: "small"
          },
          () => status.text
        );
      }
    },
    {
      label: "操作",
      fixed: "right" as const,
      width: 180,
      slot: "operation"
    }
  ];

  const onSearch = async () => {
    loading.value = true;
    try {
      // 节点树选择地点
      const nodeAreaField = handleFormField(
        selectedTreeNode.value,
        treeData.value,
        "label"
      );
      // 构建查询参数
      const params = {
        city: filterParams.city || "",
        county: filterParams.county || "",
        township: filterParams.township || "",
        hamlet: filterParams.hamlet || "",
        site: filterParams.site || "",
        examine: filterParams.examine || "",
        pageSize: pagination.pageSize,
        curPage: pagination.currentPage,
        ...nodeAreaField
      };

      const response = await getFaceReviewList(params);
      if (response.code === 200) {
        dataList.value = response.data.records || [];
        pagination.total = response.data.total || 0;
        pagination.currentPage = response.data.current || 1;
        pagination.pageSize = response.data.size || 10;
      } else {
        message(`获取数据失败：${response.msg}`, { type: "error" });
      }
    } catch (error) {
      message(`获取数据失败：${error.message}`, { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const resetFilterForm = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    Object.keys(filterParams).forEach(key => {
      filterParams[key] = undefined;
    });
    onSearch();
  };

  const handleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    onSearch();
  };

  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val;
    onSearch();
  };

  const previewDetail = (row: FaceReviewDetail) => {
    currentDetail.value = row;
    drawerVisible.value = true;
  };

  function onTreeSelect(node) {
    selectedTreeNode.value = node;
    onSearch();
  }

  /** 获取地区树 */
  const fetchAreaTree = () => {
    getAreaTree()
      .then(res => {
        if (res.code === 200) {
          treeData.value = res.data;
        }
      })
      .finally(() => {
        treeLoading.value = false;
      });
  };

  onMounted(() => {
    fetchAreaTree();
    onSearch();
  });

  return {
    loading,
    filterParams,
    dataList,
    columns,
    pagination,
    drawerVisible,
    currentDetail,
    treeData,
    treeLoading,
    onSearch,
    resetFilterForm,
    handleSizeChange,
    handleCurrentChange,
    previewDetail,
    onTreeSelect
  };
}
