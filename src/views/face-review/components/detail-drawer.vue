<template>
  <el-drawer
    v-model="visible"
    title="人脸信息详情"
    size="600px"
    :destroy-on-close="true"
  >
    <div v-loading="loading" class="face-detail">
      <!-- 人脸图片 -->
      <div class="image-section">
        <div class="section-title">人脸照片</div>
        <el-image
          :src="detail?.examineUrl"
          fit="cover"
          class="face-image"
          @click="onPreviewImg([detail?.examineUrl])"
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </div>

      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">设备编号：</span>
            <span class="value">{{ detail?.equipmentnumber }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属地区：</span>
            <span class="value">{{ detail?.site }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">提交时间：</span>
            <span class="value">{{ formatTime(detail?.createTime) }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">详细地址：</span>
            <span class="value">
              {{ detail?.city }}{{ detail?.county }}{{ detail?.township
              }}{{ detail?.hamlet }}
            </span>
          </div>
        </div>
      </div>

      <!-- 审核操作区域 -->
      <div v-if="detail?.examine === '未审核'" class="review-section">
        <div class="section-title">审核操作</div>
        <div class="user-select-area">
          <span class="label">选择人员：</span>
          <el-select
            v-model="selectedUser"
            placeholder="请选择绑定人员"
            clearable
            filterable
            :loading="loading"
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.name"
              :value="user.userId"
            />
          </el-select>
        </div>
        <div class="action-buttons">
          <el-button
            type="primary"
            :disabled="!selectedUser"
            @click="handleReview(true)"
          >
            通过
          </el-button>
          <el-button type="danger" @click="handleReview(false)">
            不通过
          </el-button>
        </div>
      </div>

      <!-- 已审核状态下显示绑定人员信息 -->
      <div v-else class="review-section">
        <div class="section-title">审核信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">审核状态：</span>
            <el-tag
              :type="detail?.examine === '通过' ? 'success' : 'danger'"
              effect="light"
            >
              {{ detail?.examine }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">审核人：</span>
            <span class="value">{{ detail?.reviewer || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">审核时间：</span>
            <span class="value">{{ formatTime(detail?.reviewerTime) }}</span>
          </div>
          <div v-if="detail?.examine === '通过'" class="info-item">
            <span class="label">绑定人员：</span>
            <span class="value">{{ detail?.bindingUsername || "-" }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航按钮 -->
    <template #footer>
      <div class="navigation-buttons">
        <el-button
          v-if="!isFirstItem"
          type="primary"
          plain
          @click="handleNavigate('prev')"
        >
          <el-icon><ArrowLeft /></el-icon>
          上一条
        </el-button>
        <el-button
          v-if="!isLastItem"
          type="primary"
          plain
          @click="handleNavigate('next')"
        >
          下一条
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { Picture, ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { usePreviewImg } from "@/hooks/usePreviewImg";
import dayjs from "dayjs";
import type { FaceReviewDetail, User } from "../utils/types";
import { getUsers, reviewFace } from "@/api/face-review";
import { message } from "@/utils/message";
import { useUserStoreHook } from "@/store/modules/user";
import type { UserInfo } from "@/store/types";

const props = defineProps<{
  modelValue: boolean;
  detail: FaceReviewDetail;
  dataList: FaceReviewDetail[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "refresh"): void;
  (e: "update:detail", value: FaceReviewDetail): void;
}>();

const userStore = useUserStoreHook();

const visible = computed({
  get: () => props.modelValue,
  set: value => {
    emit("update:modelValue", value);
    if (value) {
      // 当抽屉打开时加载用户列表
      loadUsers();
    }
  }
});

const loading = ref(false);
const userList = ref<User[]>([]);
const selectedUser = ref<number | null>(null);

const { onPreviewImg } = usePreviewImg();

const formatTime = (time: string) => {
  return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "-";
};

const loadUsers = async () => {
  try {
    loading.value = true;
    const params = {
      city: props.detail.city,
      county: props.detail.county,
      township: props.detail.township,
      hamlet: props.detail.hamlet,
      site: props.detail.site
    };
    const response = await getUsers(params);
    if (response.code === 200) {
      // 确保数据是数组
      userList.value = Array.isArray(response.data) ? response.data : [];

      // 如果已有绑定用户，设置为选中状态
      if (props.detail?.bindUser?.userId) {
        selectedUser.value = props.detail.bindUser.userId;
      } else {
        selectedUser.value = null;
      }
    } else {
      console.error("Failed to load users:", response.msg);
      message(`获取用户列表失败：${response.msg}`, { type: "error" });
    }
  } catch (error) {
    console.error("Error loading users:", error);
    message(`获取用户列表失败：${error.message}`, { type: "error" });
  } finally {
    loading.value = false;
  }
};
// 提交审核
const handleReview = async (isPass: boolean) => {
  if (!props.detail) return;
  try {
    loading.value = true;
    const userInfo = userStore.userInfo;
    // 从用户列表中获取选中用户的完整信息
    const selectedUserInfo = userList.value.find(
      user => user.userId === selectedUser.value
    );
    const response = await reviewFace({
      id: props.detail.id,
      userId: isPass ? selectedUserInfo?.userId : null,
      userName: isPass ? selectedUserInfo?.name : null,
      isExamine: isPass,
      reviewer: userStore.userInfo?.name,
      reviewerId: userStore.userInfo?.userId
    });

    if (response.code === 200) {
      message(isPass ? "审核通过" : "审核不通过", { type: "success" });
      emit("refresh");
      visible.value = false;
    } else {
      message(`审核失败：${response.msg}`, { type: "error" });
    }
  } catch (error) {
    message(`审核失败：${error.message}`, { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 重置选中的用户
watch(visible, newVisible => {
  if (!newVisible) {
    selectedUser.value = null;
  }
});

// 监听详情变化，重新加载用户列表
watch(
  () => props.detail,
  newDetail => {
    if (newDetail && visible.value) {
      loadUsers();
    }
  },
  { deep: true }
);

// 计算当前项是否为第一项或最后一项
const currentIndex = computed(() => {
  return props.dataList.findIndex(item => item.id === props.detail?.id);
});

const isFirstItem = computed(() => currentIndex.value === 0);
const isLastItem = computed(
  () => currentIndex.value === props.dataList.length - 1
);

// 处理导航
const handleNavigate = (direction: "prev" | "next") => {
  const newIndex =
    direction === "prev" ? currentIndex.value - 1 : currentIndex.value + 1;
  if (newIndex >= 0 && newIndex < props.dataList.length) {
    emit("update:detail", props.dataList[newIndex]);
  }
};
</script>

<style lang="scss" scoped>
.face-detail {
  height: calc(100vh - 180px); // 减去头部和底部的高度
  padding: 20px;
  overflow-y: auto;

  .section-title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .image-section {
    margin-bottom: 24px;

    .face-image {
      width: 200px;
      height: 200px;
      cursor: pointer;
      border-radius: 4px;
    }
  }

  .info-section {
    padding: 16px;
    margin-bottom: 24px;
    background: var(--el-fill-color-light);
    border-radius: 8px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 16px;

      .info-item {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.full-width {
          grid-column: span 2;
        }

        .label {
          margin-right: 8px;
          color: var(--el-text-color-secondary);
        }

        .value {
          color: var(--el-text-color-primary);
        }
      }
    }
  }

  .review-section {
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 8px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .info-item {
        .label {
          margin-right: 8px;
          color: var(--el-text-color-secondary);
        }

        .value {
          color: var(--el-text-color-primary);
        }
      }
    }

    .user-select-area {
      display: flex;
      align-items: center;
      margin-bottom: 24px;

      .label {
        width: 80px;
        color: var(--el-text-color-secondary);
      }

      .el-select {
        width: 240px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
    }
  }
}

// 导航按钮样式
.navigation-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;

  .el-button {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    min-width: 100px;

    &:first-child {
      margin-right: auto;
    }

    &:last-child {
      margin-left: auto;
    }
  }
}
</style>
