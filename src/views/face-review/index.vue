<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import View from "@iconify-icons/ep/view";
import { ref } from "vue";
import DetailDrawer from "./components/detail-drawer.vue";
import tree from "./tree.vue";
import { useFaceReview } from "./utils";

defineOptions({
  name: "FaceReview"
});

const filterRef = ref();
const {
  loading,
  filterParams,
  dataList,
  columns,
  pagination,
  treeData,
  treeLoading,
  drawerVisible,
  currentDetail,
  onTreeSelect,
  onSearch,
  resetFilterForm,
  handleSizeChange,
  handleCurrentChange,
  previewDetail
} = useFaceReview();
</script>

<template>
  <div :class="['flex', 'justify-between']">
    <tree
      ref="treeRef"
      :class="['mr-2', 'min-w-[300px]']"
      :treeData="treeData"
      :treeLoading="treeLoading"
      @tree-select="onTreeSelect"
    />
    <div class="face-review">
      <el-form
        ref="filterRef"
        :inline="true"
        :model="filterParams"
        class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px] overflow-auto"
      >
        <el-form-item label="审核状态：" prop="examine">
          <el-select
            v-model="filterParams.examine"
            placeholder="请选择审核状态"
            clearable
            class="!w-[180px]"
          >
            <el-option label="待审核" value="未审核" />
            <el-option label="已通过" value="通过" />
            <el-option label="未通过" value="未通过" />
          </el-select>
        </el-form-item>
        <el-form-item label="市：" prop="city">
          <el-input
            v-model="filterParams.city"
            placeholder="请输入市"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <el-form-item label="区/县：" prop="county">
          <el-input
            v-model="filterParams.county"
            placeholder="请输入区/县"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <el-form-item label="乡镇：" prop="township">
          <el-input
            v-model="filterParams.township"
            placeholder="请输入乡镇"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <el-form-item label="村/社区：" prop="hamlet">
          <el-input
            v-model="filterParams.hamlet"
            placeholder="请输入村/社区"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <el-form-item label="站点：" prop="site">
          <el-input
            v-model="filterParams.site"
            placeholder="请输入站点"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            @click="resetFilterForm(filterRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <pure-table
        row-key="id"
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        :header-cell-style="{
          background: 'var(--el-fill-color-light)',
          color: 'var(--el-text-color-primary)'
        }"
        @page-size-change="handleSizeChange"
        @page-current-change="handleCurrentChange"
      >
        <template #operation="{ row }">
          <el-button
            class="reset-margin"
            link
            type="primary"
            :icon="useRenderIcon(View)"
            @click="previewDetail(row)"
          >
            查看
          </el-button>
        </template>
      </pure-table>

      <detail-drawer
        v-model="drawerVisible"
        v-model:detail="currentDetail"
        :data-list="dataList"
        @refresh="onSearch"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.face-review {
  display: flex;
  flex: 1;
  flex-direction: column;

  .search-form {
    margin-bottom: 16px;
  }

  .pure-table {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 0.5rem;
    background-color: #fff;

    :deep(.el-table--fit) {
      flex: 1;
    }
  }
}
</style>
