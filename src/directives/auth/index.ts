import { hasAuth } from "@/router/utils";
import { useUserStoreHook } from "@/store/modules/user";
import type { Directive, DirectiveBinding } from "vue";

export const auth: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<string | Array<string>>) {
    const { value } = binding;
    if (value) {
      !hasAuth(value) && el.parentNode?.removeChild(el);
    } else {
      throw new Error(
        "[Directive: auth]: need auths! Like v-auth=\"['btn.add','btn.edit']\""
      );
    }
  }
};

/** 创建权限指令（单个权限） */
export const permission: Directive = {
  mounted(el: HTMLElement, binding) {
    const { value } = binding;
    const userStore = useUserStoreHook();
    const permissions = userStore.permissions || [];

    if (value && value.length > 0) {
      const hasPermission = permissions.some(permission =>
        permission.includes(value)
      );
      if (!hasPermission) {
        el.parentNode?.removeChild(el);
      }
    } else {
      throw new Error(`need permission! Like v-permission="'system/user/add'"`);
    }
  }
};

/** 创建权限指令（判断是否每一项都不满足权限） */
export const allPermission: Directive = {
  mounted(el: HTMLElement, binding) {
    const { value } = binding;
    const userStore = useUserStoreHook();
    const permissions = userStore.permissions || [];

    // 确保传入的 permission 是数组并且非空
    if (Array.isArray(value) && value.length > 0) {
      // 检查每一项是否都匹配不上
      const notHasAllPermissions = value.every(permissionItem =>
        permissions.every(
          userPermission => !(userPermission === permissionItem)
        )
      );
      if (notHasAllPermissions) {
        el.parentNode?.removeChild(el);
      }
    } else {
      throw new Error(
        `need permissions array! Like v-all-permission="['system/user/add', 'system/user/edit']"`
      );
    }
  }
};

/** 判断是否每一项都不满足权限 */
export const notHasAllPermission = (permission: string[]) => {
  const userStore = useUserStoreHook();
  const permissions = userStore.permissions || [];
  // 确保传入的 permission 是数组并且非空
  if (Array.isArray(permission) && permission.length > 0) {
    // 检查每一项是否都匹配不上
    const notHasAllPermissions = permission.every(permissionItem =>
      permissions.every(userPermission => !(userPermission === permissionItem))
    );
    return notHasAllPermissions;
  }

  return false;
};
