import { type PropType, ref, defineComponent } from "vue";
import { cloneDeep } from "@pureadmin/utils";

const props = {
  /** 头部最左边的标题 */
  title: {
    type: String,
    default: "列表"
  },
  /** 对于树形表格，如果想启用展开和折叠功能，传入当前表格的ref即可 */
  tableRef: {
    type: Object as PropType<any>
  },
  /** 需要展示的列 */
  columns: {
    type: Array as PropType<TableColumnList>,
    default: () => []
  },
  isExpandAll: {
    type: Boolean,
    default: true
  },
  tableKey: {
    type: [String, Number] as PropType<string | number>,
    default: "0"
  },
  titleSolt: {
    type: Boolean,
    default: true
  }
};

export default defineComponent({
  name: "PureTableBar",
  props,
  emits: ["refresh"],
  setup(props, { slots, attrs }) {
    const size = ref("default");
    const isFullscreen = ref(false);
    const dynamicColumns = ref(cloneDeep(props?.columns));

    return () => (
      <>
        <div
          {...attrs}
          class={[
            "w-[99/100]",
            "px-2",
            "pt-2",
            "pb-2",
            "bg-bg_color",
            isFullscreen.value
              ? ["!w-full", "!h-full", "z-[2002]", "fixed", "inset-0"]
              : "mt-2",
            props.titleSolt ? "" : "pt-2"
          ]}
        >
          {slots.default({
            size: size.value,
            dynamicColumns: dynamicColumns.value
          })}
        </div>
      </>
    );
  }
});
