<template>
  <div class="quick-time-range-selector">
    <!-- 快捷时间选择按钮 -->
    <div class="quick-buttons">
      <div
        v-for="item in quickOptions"
        :key="item.value"
        class="quick-btn"
        :class="{ active: selectedQuick === item.value }"
        @click="handleQuickSelect(item)"
      >
        {{ item.label }}
      </div>
    </div>

    <!-- 自定义时间范围选择 -->
    <div class="custom-range">
      <!-- <div class="range-label"></div> -->
      <el-date-picker
        v-model="customRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        size="default"
        @change="handleCustomRangeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import dayjs from "dayjs";

interface QuickOption {
  label: string;
  value: string;
  getRange: () => [string, string];
}

interface Props {
  // 当前选中的时间范围 [开始日期, 结束日期]
  modelValue?: [string, string];
  // 是否禁用未来日期
  disableFuture?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [
    dayjs().startOf("month").format("YYYY-MM-DD"),
    dayjs().endOf("month").format("YYYY-MM-DD")
  ],
  disableFuture: true
});

const emit = defineEmits<{
  (e: "update:modelValue", value: [string, string]): void;
  (e: "change", value: [string, string]): void;
}>();

// 内部状态
const selectedQuick = ref<string>("");
const customRange = ref<[string, string]>(props.modelValue);

// 快捷选项配置
const quickOptions: QuickOption[] = [
  {
    label: "上周",
    value: "lastWeek",
    getRange: () => [
      dayjs().subtract(1, "week").startOf("week").format("YYYY-MM-DD"),
      dayjs().subtract(1, "week").endOf("week").format("YYYY-MM-DD")
    ]
  },
  {
    label: "上月",
    value: "lastMonth",
    getRange: () => [
      dayjs().subtract(1, "month").startOf("month").format("YYYY-MM-DD"),
      dayjs().subtract(1, "month").endOf("month").format("YYYY-MM-DD")
    ]
  },
  {
    label: "本周",
    value: "thisWeek",
    getRange: () => [
      dayjs().startOf("week").format("YYYY-MM-DD"),
      dayjs().endOf("week").format("YYYY-MM-DD")
    ]
  },
  {
    label: "本月",
    value: "thisMonth",
    getRange: () => [
      dayjs().startOf("month").format("YYYY-MM-DD"),
      dayjs().endOf("month").format("YYYY-MM-DD")
    ]
  }
];

// 检查当前时间范围是否匹配某个快捷选项
const checkQuickMatch = (range: [string, string]) => {
  const matchedOption = quickOptions.find(option => {
    const optionRange = option.getRange();
    return optionRange[0] === range[0] && optionRange[1] === range[1];
  });
  selectedQuick.value = matchedOption ? matchedOption.value : "";
};

// 监听外部传入的值
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      customRange.value = newVal;
      // 检查是否匹配某个快捷选项
      checkQuickMatch(newVal);
    }
  },
  { immediate: true }
);

// 处理快捷选择
const handleQuickSelect = (option: QuickOption) => {
  selectedQuick.value = option.value;
  const range = option.getRange();
  customRange.value = range;
  emit("update:modelValue", range);
  emit("change", range);
};

// 处理自定义范围选择
const handleCustomRangeChange = (value: [string, string] | null) => {
  if (value) {
    selectedQuick.value = ""; // 清除快捷选择状态
    customRange.value = value;
    emit("update:modelValue", value);
    emit("change", value);
  }
};
</script>

<style lang="scss" scoped>
.quick-time-range-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .quick-btn {
    padding: 6px 12px;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background: #ecf5ff;
    }

    &.active {
      color: #fff;
      background: #409eff;
      border-color: #409eff;
    }
  }
}

.custom-range {
  display: flex;
  align-items: center;
  gap: 8px;

  .range-label {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
  }
}
</style>
