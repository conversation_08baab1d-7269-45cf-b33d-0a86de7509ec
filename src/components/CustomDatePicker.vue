<template>
  <div ref="wrapperRef" class="custom-date-picker-wrapper">
    <!-- 显示区域：当前选中日期和图标 -->
    <div class="cdp-display" @click="showPicker = true">
      <span class="cdp-date-text">{{ modelValue || todayStr }}</span>
    </div>
  </div>

  <!-- 使用 Teleport 将弹出层渲染到 body 下，避免被父容器裁剪 -->
  <Teleport to="body">
    <div
      v-if="showPicker"
      class="cdp-popup"
      :class="placementClass"
      :style="popupStyle"
      @mousedown.stop
    >
      <div class="custom-date-picker">
        <div class="cdp-header">
          <button class="cdp-btn" @click="prevMonth">«</button>
          <span class="cdp-title">
            {{ displayYear }}年{{ displayMonth + 1 }}月
          </span>
          <button class="cdp-btn" @click="nextMonth">»</button>
        </div>
        <div class="cdp-week">
          <span v-for="w in weekDays" :key="w" class="cdp-weekday">
            {{ w }}
          </span>
        </div>
        <div class="cdp-days">
          <span
            v-for="(d, idx) in days"
            :key="idx"
            :class="[
              'cdp-day',
              d.isToday ? 'cdp-today' : '',
              d.isSelected ? 'cdp-selected' : '',
              d.isDisabled ? 'cdp-disabled' : ''
            ]"
            @click="selectDate(d)"
          >
            {{ d.day || "" }}
          </span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick
} from "vue";

// 组件 props
const props = defineProps<{
  modelValue: string; // 选中日期，格式 YYYY-MM-DD
  disableFuture?: boolean; // 是否禁用未来日期
  placement?: "top" | "bottom"; // 弹窗方向，默认 top
}>();
const emit = defineEmits(["update:modelValue", "change"]);

// 弹窗位置计算
const popupStyle = ref({});

// 计算弹窗位置
const calculatePopupPosition = () => {
  if (!wrapperRef.value) return;

  const rect = wrapperRef.value.getBoundingClientRect();
  const popupWidth = 280; // 更新日历宽度
  const popupHeight = 320; // 更新日历高度

  let left = rect.left - 95; // 向左偏移95px
  let top =
    props.placement === "bottom"
      ? rect.bottom + 10
      : rect.top - popupHeight - 10;

  // 确保弹窗不超出视口边界
  if (left < 0) left = 10;
  if (left + popupWidth > window.innerWidth) {
    left = window.innerWidth - popupWidth - 10;
  }
  if (top < 0) top = 10;
  if (top + popupHeight > window.innerHeight) {
    top = window.innerHeight - popupHeight - 10;
  }

  popupStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  };
};

// placement 相关
const placementClass = computed(() => {
  return props.placement === "bottom" ? "cdp-popup-bottom" : "cdp-popup-top";
});

// 控制弹窗显示
const showPicker = ref(false);
const wrapperRef = ref<HTMLElement | null>(null);

// 监听弹窗显示状态，计算位置
watch(showPicker, visible => {
  if (visible) {
    nextTick(() => {
      calculatePopupPosition();
    });
  }
});

// 关闭弹窗（点击外部）
function handleClickOutside(e: MouseEvent) {
  if (!wrapperRef.value) return;
  if (!wrapperRef.value.contains(e.target as Node)) {
    showPicker.value = false;
  }
}
onMounted(() => {
  document.addEventListener("mousedown", handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener("mousedown", handleClickOutside);
});

// 星期标题
const weekDays = ["日", "一", "二", "三", "四", "五", "六"];

// 当前显示的年月
const today = new Date();
const todayStr = computed(() => {
  return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;
});
const selected = ref(props.modelValue ? new Date(props.modelValue) : today);
const displayYear = ref(selected.value.getFullYear());
const displayMonth = ref(selected.value.getMonth());

// 监听外部 v-model 变化
watch(
  () => props.modelValue,
  val => {
    if (val) {
      const d = new Date(val);
      selected.value = d;
      displayYear.value = d.getFullYear();
      displayMonth.value = d.getMonth();
    }
  }
);

// 生成当前月的日期数组
const days = computed(() => {
  const year = displayYear.value;
  const month = displayMonth.value;
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startWeek = firstDay.getDay();
  const arr: any[] = [];

  // 补前空
  for (let i = 0; i < startWeek; i++) {
    arr.push({});
  }
  // 填日期
  for (let d = 1; d <= daysInMonth; d++) {
    const dateObj = new Date(year, month, d);
    const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(d).padStart(2, "0")}`;
    const isToday =
      dateObj.getFullYear() === today.getFullYear() &&
      dateObj.getMonth() === today.getMonth() &&
      dateObj.getDate() === today.getDate();
    const isSelected =
      props.modelValue &&
      dateObj.getFullYear() === selected.value.getFullYear() &&
      dateObj.getMonth() === selected.value.getMonth() &&
      dateObj.getDate() === selected.value.getDate();
    const isDisabled =
      props.disableFuture && dateObj.getTime() > today.getTime();
    arr.push({
      day: d,
      dateStr,
      isToday,
      isSelected,
      isDisabled
    });
  }
  // 补后空
  while (arr.length % 7 !== 0) {
    arr.push({});
  }
  return arr;
});

// 切换月份
function prevMonth() {
  if (displayMonth.value === 0) {
    displayYear.value--;
    displayMonth.value = 11;
  } else {
    displayMonth.value--;
  }
}
function nextMonth() {
  if (displayMonth.value === 11) {
    displayYear.value++;
    displayMonth.value = 0;
  } else {
    displayMonth.value++;
  }
}

// 选择日期
function selectDate(d: any) {
  if (!d.day || d.isDisabled) return;
  emit("update:modelValue", d.dateStr);
  emit("change", d.dateStr);
  showPicker.value = false; // 选择后自动关闭弹窗
}
</script>

<style lang="scss" scoped>
@import "../views/large-screen/styles/font-sizes.scss";

.custom-date-picker-wrapper {
  position: relative;
  display: inline-block;
}

.cdp-display {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center; /* 文本居中 */
  width: 120px; /* 改为固定宽度 */
  padding: 2px 10px;
  font-size: 24px;
  @extend .fs-date-picker-text; // 使用新的日期文本字体大小
  font-weight: 500;
  color: #fff;
  cursor: pointer;
  background: rgb(0 24 75 / 70%);
  border: 1px solid #409eff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(64 158 255 / 10%);
  transition: box-shadow 0.2s;
}

.cdp-display:hover {
  border-color: #66b1ff;
  box-shadow: 0 4px 16px #409eff44;
}

.cdp-date-text {
  width: 100%; /* 占满容器宽度 */
  text-align: center; /* 文本居中 */
  letter-spacing: 1px;
}

.cdp-icon {
  font-size: 18px;
  color: #409eff;
}

.cdp-popup {
  position: fixed; /* 改为 fixed 定位，相对于视口定位 */
  z-index: 99999; /* 提高z-index层级，确保在大屏页面中显示在最顶层 */
  background: transparent;

  /* left 和 top 通过 JavaScript 动态计算 */
}

/* 以下为原日历样式 */
.custom-date-picker {
  width: 280px; /* 减小日历整体宽度 */
  padding: 16px; /* 减小内边距 */
  color: #fff;
  user-select: none;
  background: #001529;
  border: 1px solid #409eff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
}

.cdp-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.cdp-title {
  font-size: 18px; /* 减小标题字体 */
  font-weight: bold;
}

.cdp-btn {
  padding: 2px 8px; /* 减小按钮内边距 */
  font-size: 18px; /* 减小按钮字体 */
  color: #409eff;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 4px;
  transition: background 0.2s;
}

.cdp-btn:hover {
  background: #409eff33;
}

.cdp-week {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.cdp-weekday {
  display: inline-block;
  width: calc(100% / 7);
  height: 30px; /* 减小行高 */
  font-size: 14px; /* 减小星期字体 */
  font-weight: 600;
  line-height: 30px;
  color: #42a5ff;
  text-align: center;
}

.cdp-days {
  display: flex;
  flex-wrap: wrap;
}

.cdp-day {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% / 7);
  height: 30px; /* 减小日期格高度 */
  font-size: 14px; /* 减小日期字体 */
  cursor: pointer;
  border-radius: 50%;
  transition:
    background 0.2s,
    color 0.2s;
}

.cdp-day.cdp-today {
  font-weight: bold;
  color: #409eff;
  border: 2px solid #409eff;
}

.cdp-day.cdp-selected {
  font-weight: bold;
  color: #fff;
  background: #409eff;
  box-shadow: 0 0 8px #409eff99;
}

.cdp-day.cdp-disabled {
  color: #888;
  cursor: not-allowed;
  background: #222;
  opacity: 0.5;
}
</style>
