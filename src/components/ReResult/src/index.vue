<script lang="ts" setup>
import { h, onMounted, ref, useSlots } from "vue";
import { type TippyOptions, useTippy } from "vue-tippy";
import { ElDialog } from "element-plus";

defineOptions({
  name: "ReResult"
});

const open = ref(true);

const props = defineProps({});

onMounted(() => {});

const openResult = () => {
  open.value = true;
};
</script>

<template>
  <ElDialog v-model="open" @close="open = false">
    Click to open Message Box
  </ElDialog>
</template>
