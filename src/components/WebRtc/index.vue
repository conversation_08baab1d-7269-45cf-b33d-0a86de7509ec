<script lang="ts" setup>
/// <reference types="vite/client" />

import { useUserStoreHook } from "@/store/modules/user";
import {
  CircleClose,
  Microphone,
  Mute,
  VideoPlay
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { computed, onBeforeUnmount, onMounted, reactive, ref } from "vue";

// 添加Vue宏的类型声明
import type { PropType } from "vue";

defineOptions({
  name: "WebRtc"
});

// 定义组件接口
const props = defineProps({
  // 是否自动接听
  autoAnswer: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  // 信令服务器地址
  signalServer: {
    type: String as PropType<string>,
    default: import.meta.env.VITE_SIGNAL_SERVER || "wss://ybda.top/webrtc-ws"
  },
  // 是否显示控制UI
  showControls: {
    type: Boolean as PropType<boolean>,
    default: true
  },
  // 房间ID
  roomId: {
    type: String as PropType<string>,
    default: () => `room-${Math.random().toString(36).substring(2, 9)}`
  },
  // 是否自动连接
  autoConnect: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  // 默认是否禁音
  defaultMuted: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const emit = defineEmits([
  "connected", // 连接建立时
  "disconnected", // 连接断开时
  "error", // 发生错误时
  "call-started", // 通话开始时
  "call-ended", // 通话结束时
  "incoming-call", // 收到来电时
  "mute-changed" // 禁音状态变化时
]);

const userStore = useUserStoreHook();

// 客户端id
const userId = ref(`PC-${userStore.userInfo.userId}`);

// 状态管理
const state = reactive({
  status: "idle", // idle, connecting, connected, disconnected, error
  isMuted: props.defaultMuted, // 默认根据props设置禁音状态
  isCallInProgress: false,
  hasIncomingCall: false,
  incomingCallFrom: null,
  errorMessage: "",
  connectionStep: "", // 添加连接步骤提示
  hasMicrophone: false // 默认设为false，直到检测到麦克风
});

// WebRTC 连接相关
let peerConnection: RTCPeerConnection | null = null;
let localStream: MediaStream | null = null;
let remoteStream: MediaStream | null = null;
let wsConnection: WebSocket | null = null;
let pendingCandidates: RTCIceCandidate[] = [];

// DOM 引用
const localAudio = ref<HTMLAudioElement | null>(null);
const remoteAudio = ref<HTMLAudioElement | null>(null);

// 修改心跳相关变量
const HEARTBEAT_INTERVAL = 20000; // 20秒发送一次心跳
const HEARTBEAT_TIMEOUT = 10000; // 10秒没有响应则认为断开
let heartbeatInterval: number | null = null; // 修改类型为 number
let heartbeatTimeoutId: number | null = null; // 修改类型为 number

// 获取通话按钮文本
const callButtonText = computed(() => {
  if (!state.hasMicrophone) return "无可用麦克风";
  return state.isCallInProgress ? "结束通话" : "开始通话";
});

// 获取通话按钮图标
const callButtonIcon = computed(() => {
  if (!state.hasMicrophone) return CircleClose;
  return state.isCallInProgress ? CircleClose : VideoPlay;
});

// 获取状态文本
const statusText = computed(() => {
  if (state.errorMessage) return state.errorMessage;
  if (state.connectionStep) return state.connectionStep;

  switch (state.status) {
    case "idle":
      return state.hasMicrophone ? "准备就绪" : "无可用麦克风";
    case "connecting":
      return "正在连接...";
    case "connected":
      return "已连接";
    case "disconnected":
      return "连接已断开";
    case "error":
      return "连接错误";
    default:
      return "";
  }
});

// 初始化
onMounted(async () => {
  console.log("webrtc挂载：roomId", props.roomId, "clientId:", userId.value);
  // 检查麦克风可用性
  await checkMicrophoneAvailability();

  // 如果设置了自动连接且有麦克风，则自动开始通话
  if (props.autoConnect && state.hasMicrophone) {
    await startCall();

    // 如果默认禁音，则设置禁音
    if (props.defaultMuted) {
      toggleMute(true);
    }
  }
});

// 清理
onBeforeUnmount(() => {
  endCall();
  closeConnection();
});

// 初始化 WebSocket 连接（用于信令）
const initWebSocket = () => {
  try {
    state.connectionStep = "正在连接信令服务器...";
    wsConnection = new WebSocket(props.signalServer);

    wsConnection.onopen = () => {
      console.log("WebSocket连接已建立");
      state.connectionStep = "信令服务器已连接，等待应答中...";
      // 连接成功后加入房间
      sendToSignalServer({
        type: "join",
        roomId: props.roomId,
        clientId: userId.value
      });

      // 启动心跳
      startHeartbeat();
    };

    wsConnection.onmessage = event => {
      const message = JSON.parse(event.data);
      console.log("收到消息:", message);

      // 处理心跳响应
      if (message.type === "heartbeat") {
        console.log("收到心跳响应:", {
          timestamp: message.timestamp,
          count: message.count,
          roomId: message.roomId,
          clientId: message.clientId
        });
        // 清除超时检查
        if (heartbeatTimeoutId) {
          clearTimeout(heartbeatTimeoutId);
          heartbeatTimeoutId = null;
        }
        return;
      }
      handleSignalingData(message);
    };

    wsConnection.onerror = error => {
      console.error("WebSocket错误:", error);
      state.status = "error";
      state.errorMessage = "信令服务器连接失败";
      emit("error", { message: "信令服务器连接失败" });
      // 连接错误时重置通话状态
      resetCallState();
    };

    wsConnection.onclose = () => {
      console.log("WebSocket连接已关闭");
      state.status = "disconnected";
      emit("disconnected");
      // 连接关闭时重置通话状态
      resetCallState();
    };
  } catch (error) {
    console.error("初始化WebSocket失败:", error);
    state.status = "error";
    state.errorMessage = "初始化WebSocket失败";
    emit("error", { message: "初始化WebSocket失败" });
    // 初始化失败时重置通话状态
    resetCallState();
  }
};

// 发送数据到信令服务器
const sendToSignalServer = (data: any) => {
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    // 添加房间ID和客户端ID
    data.roomId = props.roomId;
    data.clientId = userId.value;

    wsConnection.send(JSON.stringify(data));
    console.log(`发送消息: ${data.type}`, data);
  } else {
    console.error("WebSocket未连接，无法发送数据");
    ElMessage.warning("信令服务器未连接，请稍后再试");
  }
};

// 处理信令数据
const handleSignalingData = async (data: any) => {
  console.log(`收到消息: ${data.type}`, data);

  switch (data.type) {
    case "join":
      // 当收到其他客户端的加入消息时，如果我们已经准备好了，就发送 offer
      if (data.clientId !== userId.value && peerConnection && localStream) {
        console.log(`新用户加入房间: ${data.clientId}`);
        await createAndSendOffer();
      }
      break;

    case "peer-joined":
      // 兼容旧消息类型
      if (data.clientId !== userId.value && peerConnection && localStream) {
        console.log(`新用户加入房间: ${data.clientId}`);
        await createAndSendOffer();
      }
      break;

    case "offer":
      await handleOffer(data);
      break;

    case "answer":
      await handleAnswer(data);
      break;

    case "ice-candidate":
      await handleIceCandidate(data);
      break;

    case "peer-left":
      console.log(`用户离开房间: ${data.clientId}`);
      handleRemoteHangup();
      break;

    case "leave":
      console.log(`用户离开房间: ${data.clientId}`);
      handleRemoteHangup();
      break;

    default:
      console.log(`未知消息类型: ${data.type}`);
  }
};

// 创建并发送offer
const createAndSendOffer = async () => {
  try {
    console.log("创建并发送 offer...");
    state.connectionStep = "正在创建通话请求...";
    const offer = await peerConnection!.createOffer({
      offerToReceiveAudio: true
    });

    console.log("设置本地描述...");
    state.connectionStep = "正在设置本地会话描述...";
    await peerConnection!.setLocalDescription(offer);

    console.log("发送 offer...");
    state.connectionStep = "正在发送通话请求...";
    sendToSignalServer({
      type: "offer",
      sdp: offer.sdp
    });
  } catch (error) {
    console.error("创建 offer 失败:", error);
    ElMessage.error("创建通话请求失败");
  }
};

// 检查是否有可用的麦克风设备
const checkMicrophoneAvailability = async (): Promise<boolean> => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const hasMic = devices.some(device => device.kind === "audioinput");
    state.hasMicrophone = hasMic;

    return hasMic;
  } catch (error) {
    console.error("检查麦克风失败:", error);
    state.hasMicrophone = false;
    ElMessage.error("没有可用的麦克风设备");
    return false;
  }
};

// 获取本地音频流
const getLocalStream = async () => {
  try {
    // 先检查麦克风可用性
    const hasMicrophone = await checkMicrophoneAvailability();
    if (!hasMicrophone) {
      throw new Error("没有可用的麦克风设备");
    }

    // 获取本地音频流
    localStream = await navigator.mediaDevices.getUserMedia({
      audio: true,
      video: false
    });

    if (localAudio.value) {
      localAudio.value.srcObject = localStream;
    }

    // 添加本地音频轨道到对等连接
    localStream.getTracks().forEach(track => {
      peerConnection?.addTrack(track, localStream!);
    });

    console.log("获取本地音频流成功");
    return true;
  } catch (error) {
    console.error("获取本地音频流失败:", error);

    // 根据错误类型提供更具体的错误信息
    if (error instanceof DOMException) {
      if (
        error.name === "NotFoundError" ||
        error.name === "DevicesNotFoundError"
      ) {
        ElMessage.error("未找到麦克风设备，请检查设备连接");
      } else if (
        error.name === "NotAllowedError" ||
        error.name === "PermissionDeniedError"
      ) {
        ElMessage.error("麦克风访问被拒绝，请授予麦克风使用权限");
      } else if (
        error.name === "NotReadableError" ||
        error.name === "TrackStartError"
      ) {
        ElMessage.error(
          "麦克风设备正被其他应用程序使用，请关闭其他使用麦克风的应用"
        );
      } else {
        ElMessage.error("无法访问麦克风，请检查权限设置");
      }
    } else {
      ElMessage.error("无法访问麦克风，请检查权限设置");
    }

    throw error;
  }
};

// 修改startCall函数，添加麦克风检查
const startCall = async () => {
  if (!state.hasMicrophone) {
    ElMessage.error("无可用麦克风，无法开始通话");
    return;
  }

  try {
    state.status = "connecting";
    state.connectionStep = "正在初始化通话...";

    // 如果WebSocket未连接，先连接WebSocket
    if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {
      initWebSocket();
    } else {
      // WebSocket已连接，直接加入房间
      sendToSignalServer({
        type: "join",
        roomId: props.roomId,
        clientId: userId.value
      });
    }

    // 创建PeerConnection
    state.connectionStep = "正在创建对等连接...";
    await createPeerConnection();

    // 获取本地音频流
    state.connectionStep = "正在获取麦克风权限...";
    await getLocalStream();

    state.isCallInProgress = true;
    emit("call-started");
    ElMessage.success("已加入通话房间");
  } catch (error) {
    console.error("发起呼叫失败:", error);
    state.status = "error";
    state.errorMessage = "发起呼叫失败";
    emit("error", { message: "发起呼叫失败" });
    ElMessage.error("发起通话失败");
  }
};

// 创建PeerConnection
const createPeerConnection = async () => {
  try {
    // 配置ICE服务器
    const configuration: RTCConfiguration = {
      iceServers: [
        {
          urls: ["turn:117.175.127.248:3478?transport=udp"],
          username: "zzb",
          credential: "123456",
          // @ts-ignore
          credentialType: "password"
        },
        {
          urls: ["turn:117.175.127.248:3478?transport=tcp"],
          username: "zzb",
          credential: "123456",
          // @ts-ignore
          credentialType: "password"
        },
        {
          urls: ["stun:117.175.127.248:3478"]
        }
      ],
      sdpSemantics: "unified-plan",
      bundlePolicy: "max-bundle" as RTCBundlePolicy,
      rtcpMuxPolicy: "require" as RTCRtcpMuxPolicy
    };

    peerConnection = new RTCPeerConnection(configuration);

    // 监听ICE候选
    peerConnection.onicecandidate = event => {
      if (event.candidate) {
        state.connectionStep = "正在交换网络信息...";
        sendToSignalServer({
          type: "ice-candidate",
          candidate: event.candidate
        });
      }
    };

    // 监听连接状态变化
    peerConnection.oniceconnectionstatechange = () => {
      const iceState = peerConnection!.iceConnectionState;
      console.log("ICE连接状态变化:", iceState);

      switch (iceState) {
        case "checking":
          state.connectionStep = "正在检查网络连接...";
          updateStatus("正在建立连接...");
          break;
        case "connected":
          state.connectionStep = "连接已建立";
          updateStatus("已连接");
          state.status = "connected";
          emit("connected");
          break;
        case "completed":
          state.connectionStep = "连接已完成";
          updateStatus("连接已完成");
          break;
        case "failed":
          state.connectionStep = "连接失败";
          updateStatus("连接失败");
          state.status = "error";
          state.errorMessage = "WebRTC连接失败";
          emit("error", { message: "WebRTC连接失败" });
          // 连接失败时重置通话状态
          resetCallState();
          ElMessage.error("通话连接失败，请重试");
          break;
        case "disconnected":
          state.connectionStep = "连接已断开";
          updateStatus("连接断开");
          state.status = "disconnected";
          emit("disconnected");
          // 连接断开时重置通话状态
          resetCallState();
          ElMessage.warning("通话连接已断开");
          break;
        case "closed":
          state.connectionStep = "连接已关闭";
          updateStatus("连接已关闭");
          // 连接关闭时重置通话状态
          resetCallState();
          break;
      }
    };

    // 监听远程流
    peerConnection.ontrack = event => {
      console.log("收到远程音频轨道");
      state.connectionStep = "已接收对方音频";
      remoteStream = event.streams[0];

      if (remoteAudio.value) {
        remoteAudio.value.srcObject = remoteStream;

        // 确保音频能播放
        remoteAudio.value.play().catch(error => {
          console.warn("自动播放失败，可能需要用户交互:", error);
        });
      }
    };

    console.log("PeerConnection 创建成功");
  } catch (error) {
    console.error("创建PeerConnection失败:", error);
    throw error;
  }
};

// 处理收到的offer
const handleOffer = async (data: any) => {
  try {
    console.log("收到 offer");
    state.connectionStep = "收到通话请求，正在处理...";

    // 如果还没有创建 PeerConnection，先创建它
    if (!peerConnection) {
      state.connectionStep = "正在创建对等连接...";
      await createPeerConnection();
    }

    // 如果还没有获取本地流，先获取
    if (!localStream) {
      state.connectionStep = "正在获取麦克风权限...";
      await getLocalStream();
    }

    state.connectionStep = "正在设置远程会话描述...";
    await peerConnection.setRemoteDescription(
      new RTCSessionDescription({
        type: "offer",
        sdp: data.sdp
      })
    );

    // 处理之前暂存的 ICE 候选
    if (pendingCandidates.length > 0) {
      state.connectionStep = "正在处理网络信息...";
    }
    while (pendingCandidates.length > 0) {
      const candidate = pendingCandidates.shift();
      await peerConnection.addIceCandidate(candidate!);
      console.log("处理暂存的 ICE 候选");
    }

    state.connectionStep = "正在创建应答...";
    const answer = await peerConnection.createAnswer();
    state.connectionStep = "正在设置本地会话描述...";
    await peerConnection.setLocalDescription(answer);

    state.connectionStep = "正在发送应答...";
    sendToSignalServer({
      type: "answer",
      sdp: answer.sdp
    });

    state.status = "connected";
    state.isCallInProgress = true;
    emit("call-started");

    console.log("已发送 answer");
  } catch (error) {
    console.error("处理 offer 失败:", error);
    ElMessage.error("处理通话请求失败");
  }
};

// 处理收到的answer
const handleAnswer = async (data: any) => {
  try {
    console.log("处理 answer");
    state.connectionStep = "收到应答，正在处理...";
    await peerConnection!.setRemoteDescription(
      new RTCSessionDescription({
        type: "answer",
        sdp: data.sdp
      })
    );
    state.connectionStep = "通话连接已建立";
  } catch (error) {
    console.error("处理 answer 失败:", error);
    ElMessage.error("建立通话连接失败");
  }
};

// 处理ICE候选
const handleIceCandidate = async (data: any) => {
  try {
    if (!data.candidate) return;

    const candidate = new RTCIceCandidate(data.candidate);

    // 如果还没有创建 PeerConnection，先创建它
    if (!peerConnection) {
      pendingCandidates.push(candidate);
      console.log("暂存 ICE 候选");
      return;
    }

    // 如果还没有设置远程描述，暂存候选
    if (!peerConnection.remoteDescription) {
      pendingCandidates.push(candidate);
      console.log("暂存 ICE 候选");
      return;
    }

    await peerConnection.addIceCandidate(candidate);
    console.log("添加 ICE 候选成功");
  } catch (error) {
    console.error("添加 ICE 候选失败:", error);
  }
};

// 更新状态
const updateStatus = (status: string) => {
  console.log("状态更新:", status);
};

// 切换静音状态
const toggleMute = (isMuted: boolean) => {
  if (localStream) {
    localStream.getAudioTracks().forEach(track => {
      track.enabled = !track.enabled;
    });
    state.isMuted = isMuted;

    ElMessage.info(isMuted ? "已静音" : "已取消静音");
  }
};

// 处理远程挂断
const handleRemoteHangup = () => {
  endCall();
  ElMessage.info("对方已结束通话");
};

// 结束通话
const endCall = () => {
  if (state.isCallInProgress) {
    // 发送离开信号
    sendToSignalServer({
      type: "leave",
      roomId: props.roomId,
      clientId: userId.value
    });
  }

  // 清理本地流
  if (localStream) {
    localStream.getTracks().forEach(track => track.stop());
    localStream = null;
  }

  // 关闭PeerConnection
  if (peerConnection) {
    peerConnection.close();
    peerConnection = null;
  }

  state.isCallInProgress = false;
  state.status = "idle";
  state.isMuted = props.defaultMuted;
  state.connectionStep = "";

  emit("call-ended");
};

// 重置通话状态
const resetCallState = () => {
  // 清理本地流
  if (localStream) {
    localStream.getTracks().forEach(track => track.stop());
    localStream = null;
  }

  // 关闭PeerConnection但不发送离开信号
  if (peerConnection) {
    peerConnection.close();
    peerConnection = null;
  }

  state.isCallInProgress = false;
  state.isMuted = props.defaultMuted;
  state.connectionStep = "";

  // 不改变状态，因为状态已经在调用此函数前设置为disconnected或error
};

// 修改心跳函数
const startHeartbeat = () => {
  console.log("开始发送心跳...");

  // 清除可能存在的旧心跳
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
  if (heartbeatTimeoutId) {
    clearTimeout(heartbeatTimeoutId);
    heartbeatTimeoutId = null;
  }

  // 设置新的心跳间隔
  heartbeatInterval = window.setInterval(() => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      console.log("发送心跳消息...");

      // 发送心跳消息，格式与后端保持一致
      const heartbeatMessage = {
        type: "heartbeat",
        timestamp: Date.now(),
        roomId: props.roomId,
        clientId: userId.value
      };

      wsConnection.send(JSON.stringify(heartbeatMessage));
      console.log("已发送心跳:", heartbeatMessage);

      // 设置超时检查
      heartbeatTimeoutId = window.setTimeout(() => {
        console.warn("心跳超时，准备重连...");
        if (wsConnection?.readyState === WebSocket.OPEN) {
          reconnectWebSocket();
        }
      }, HEARTBEAT_TIMEOUT);
    } else {
      console.warn("WebSocket未连接，无法发送心跳");
      reconnectWebSocket();
    }
  }, HEARTBEAT_INTERVAL);
};

// 修改重连函数
const reconnectWebSocket = () => {
  console.log("开始重新连接WebSocket...");
  closeConnection();

  // 添加短暂延迟后重连
  setTimeout(() => {
    initWebSocket();
  }, 1000);
};

// 修改closeConnection函数
const closeConnection = () => {
  console.log("关闭连接，清理心跳...");

  // 停止心跳
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }

  // 清除心跳超时检查
  if (heartbeatTimeoutId) {
    clearTimeout(heartbeatTimeoutId);
    heartbeatTimeoutId = null;
  }

  if (peerConnection) {
    peerConnection.close();
    peerConnection = null;
  }

  if (wsConnection) {
    wsConnection.close();
    wsConnection = null;
  }

  pendingCandidates = [];
};

// 切换通话状态
const toggleCall = () => {
  if (state.isCallInProgress) {
    endCall();
  } else {
    startCall();
  }
};

// 拒绝来电
const rejectCall = () => {
  state.hasIncomingCall = false;
  sendToSignalServer({
    type: "reject",
    to: state.incomingCallFrom,
    from: userId.value
  });
};

// 暴露给父组件的方法
defineExpose({
  startCall,
  endCall,
  toggleMute,
  joinRoom: startCall
});
</script>

<template>
  <div class="webrtc-container">
    <!-- 音频元素 -->
    <audio ref="localAudio" autoplay muted style="display: none" />
    <audio ref="remoteAudio" autoplay style="display: none" />

    <!-- 状态显示 -->
    <div v-if="statusText" class="status-text">{{ statusText }}</div>

    <!-- 控制按钮 -->
    <div v-if="showControls" class="control-buttons">
      <!-- 禁音按钮 - 只在有麦克风且通话进行中时显示 -->
      <el-button
        v-if="state.hasMicrophone && state.isCallInProgress"
        class="control-btn"
        :type="state.isMuted ? 'danger' : 'success'"
        circle
        :title="state.isMuted ? '取消禁音' : '禁音'"
        @click="() => toggleMute(!state.isMuted)"
      >
        <el-icon>
          <component :is="state.isMuted ? Mute : Microphone" />
        </el-icon>
      </el-button>

      <!-- 通话按钮 - 使用更显著的图标 -->
      <el-button
        class="control-btn"
        :type="state.isCallInProgress ? 'danger' : 'primary'"
        :disabled="!state.hasMicrophone"
        circle
        :title="callButtonText"
        @click="state.isCallInProgress ? endCall() : startCall()"
      >
        <el-icon>
          <component :is="callButtonIcon" />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.webrtc-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.status-text {
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.control-buttons {
  display: flex;
  gap: 15px;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.incoming-call {
  padding: 10px;
  margin-top: 15px;
  text-align: center;
  background-color: #f8f9fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.incoming-call-text {
  margin-bottom: 10px;
  font-weight: bold;
  color: #409eff;
}

.incoming-call-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
