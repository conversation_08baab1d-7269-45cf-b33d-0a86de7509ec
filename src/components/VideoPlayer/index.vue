<template>
  <div class="video-player-container">
    <div v-if="loading" class="loading-overlay">
      <el-icon class="loading-icon" size="48">
        <Loading />
      </el-icon>
      <p>视频加载中...</p>
    </div>

    <div v-if="error" class="error-overlay">
      <el-icon class="error-icon" size="48">
        <Warning />
      </el-icon>
      <p>{{ error }}</p>
      <el-button size="small" type="primary" @click="retry">重试</el-button>
    </div>

    <video
      v-show="!loading && !error"
      ref="videoRef"
      class="video-element"
      :controls="controls"
      :autoplay="autoplay"
      :muted="muted"
      :loop="loop"
      preload="metadata"
      webkit-playsinline
      playsinline
      crossorigin="anonymous"
      @loadstart="handleLoadStart"
      @loadedmetadata="handleLoadedMetadata"
      @loadeddata="handleLoadedData"
      @canplay="handleCanPlay"
      @canplaythrough="handleCanPlayThrough"
      @play="handlePlay"
      @pause="handlePause"
      @ended="handleEnded"
      @error="handleError"
      @timeupdate="handleTimeUpdate"
      @progress="handleProgress"
      @waiting="handleWaiting"
      @playing="handlePlaying"
    >
      您的浏览器不支持视频播放
    </video>

    <!-- 自定义控制栏（可选） -->
    <div
      v-if="showCustomControls && !loading && !error"
      class="custom-controls"
    >
      <div class="progress-container">
        <div class="progress-bar" @click="handleProgressClick">
          <div
            class="progress-loaded"
            :style="{ width: loadedPercent + '%' }"
          />
          <div
            class="progress-played"
            :style="{ width: playedPercent + '%' }"
          />
        </div>
      </div>

      <div class="controls-row">
        <div class="left-controls">
          <el-button
            size="small"
            circle
            :icon="isPlaying ? VideoPause : VideoPlay"
            @click="togglePlay"
          />
          <span class="time-display">
            {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
          </span>
        </div>

        <div class="right-controls">
          <!-- 倍速控制 -->
          <el-dropdown trigger="click" @command="changePlaybackRate">
            <el-button size="small">
              {{ playbackRate }}x
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="0.5">0.5x</el-dropdown-item>
                <!-- <el-dropdown-item command="0.75">0.75x</el-dropdown-item> -->
                <el-dropdown-item command="1">1x (正常)</el-dropdown-item>
                <!-- <el-dropdown-item command="1.25">1.25x</el-dropdown-item> -->
                <el-dropdown-item command="1.5">1.5x</el-dropdown-item>
                <el-dropdown-item command="2">2x</el-dropdown-item>
                <el-dropdown-item command="3">3x</el-dropdown-item>
                <el-dropdown-item command="4">4x</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button
            size="small"
            circle
            :icon="FullScreen"
            @click="toggleFullscreen"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import {
  ArrowDown,
  FullScreen,
  Loading,
  VideoPause,
  VideoPlay,
  Warning
} from "@element-plus/icons-vue";

interface Props {
  src?: string;
  controls?: boolean;
  autoplay?: boolean;
  muted?: boolean;
  loop?: boolean;
  showCustomControls?: boolean;
  width?: string | number;
  height?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  controls: true,
  autoplay: false,
  muted: false,
  loop: false,
  showCustomControls: false,
  width: "100%",
  height: "auto"
});

const emit = defineEmits<{
  loadstart: [];
  loadedmetadata: [duration: number];
  loadeddata: [];
  canplay: [];
  canplaythrough: [];
  play: [];
  pause: [];
  ended: [];
  error: [error: string];
  timeupdate: [currentTime: number, duration: number];
  progress: [loaded: number, total: number];
}>();

const videoRef = ref<HTMLVideoElement | null>(null);
const loading = ref(false);
const error = ref("");
const isPlaying = ref(false);

const currentTime = ref(0);
const duration = ref(0);
const loadedPercent = ref(0);
const playedPercent = ref(0);
const playbackRate = ref(1); // 播放倍速

// 添加防抖控制，避免快进快退时频繁显示loading
let seekingTimeout: NodeJS.Timeout | null = null;
const isSeeking = ref(false);

// 监听src变化，重新加载视频
watch(
  () => props.src,
  (newSrc, oldSrc) => {
    console.log("视频源变化:", { oldSrc, newSrc });
    if (newSrc) {
      // 重置状态
      loading.value = false;
      error.value = "";
      isPlaying.value = false;

      // 确保videoRef存在，如果不存在则等待
      if (videoRef.value) {
        loadVideo(newSrc);
      } else {
        // 如果videoRef还没准备好，等待下一个tick
        setTimeout(() => {
          if (videoRef.value) {
            loadVideo(newSrc);
          } else {
            console.warn("视频元素仍未准备好，跳过加载");
          }
        }, 50);
      }
    }
  },
  { immediate: true }
);

// 加载视频
const loadVideo = (src: string) => {
  if (!videoRef.value) return;

  loading.value = true;
  error.value = "";

  // 优化视频加载
  const video = videoRef.value;
  video.src = src;
  video.preload = "metadata";

  // 设置视频属性以优化播放
  video.setAttribute("playsinline", "true");
  video.setAttribute("webkit-playsinline", "true");

  // 添加缓冲策略
  if ("buffered" in video) {
    video.addEventListener("progress", () => {
      if (video.buffered.length > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        if (duration > 0) {
          const bufferedPercent = (bufferedEnd / duration) * 100;
        }
      }
    });
  }

  // 先暂停并重置视频
  video.pause();
  video.currentTime = 0;

  // 清除之前的src
  if (video.src) {
    video.removeAttribute("src");
    video.load(); // 清空当前加载的内容
  }

  // 重新设置src并加载
  video.src = src;
  video.load();

  console.log("开始加载视频:", src, {
    readyState: video.readyState,
    networkState: video.networkState
  });
};

// 事件处理函数
const handleLoadStart = () => {
  loading.value = true;
  error.value = "";
  emit("loadstart");
};

const handleLoadedMetadata = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration;
    emit("loadedmetadata", duration.value);
  }
};

const handleLoadedData = () => {
  emit("loadeddata");
};

const handleCanPlay = () => {
  loading.value = false;
  console.log("视频可以开始播放");
  emit("canplay");
};

const handleCanPlayThrough = () => {
  loading.value = false;
  emit("canplaythrough");
};

const handlePlay = () => {
  isPlaying.value = true;
  emit("play");
};

const handlePause = () => {
  isPlaying.value = false;
  emit("pause");
};

const handleEnded = () => {
  isPlaying.value = false;
  emit("ended");
};

const handleError = (event: Event) => {
  loading.value = false;
  const target = event.target as HTMLVideoElement;
  const errorMsg = target.error
    ? `视频加载失败 (错误代码: ${target.error.code})`
    : "视频加载失败";
  error.value = errorMsg;
  emit("error", errorMsg);
  ElMessage.error(errorMsg);
};

const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    playedPercent.value =
      duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0;
    emit("timeupdate", currentTime.value, duration.value);
  }
};

const handleWaiting = () => {
  // 如果正在快进快退，延迟显示loading状态
  if (isSeeking.value) {
    if (seekingTimeout) clearTimeout(seekingTimeout);
    seekingTimeout = setTimeout(() => {
      if (!isPlaying.value) return; // 如果已暂停则不显示loading
      loading.value = true;
      console.log("视频缓冲中...");
    }, 500); // 延迟500ms显示loading，避免短暂缓冲造成闪烁
  } else {
    loading.value = true;
    console.log("视频缓冲中...");
  }
};

const handlePlaying = () => {
  // 清除延迟的loading显示
  if (seekingTimeout) {
    clearTimeout(seekingTimeout);
    seekingTimeout = null;
  }
  loading.value = false;
  isSeeking.value = false;
  console.log("视频开始播放");
};

// 倍速控制
const changePlaybackRate = (rate: string) => {
  const newRate = parseFloat(rate);
  if (!videoRef.value || newRate <= 0) {
    console.warn("无效的播放倍速:", rate);
    return;
  }

  try {
    // 检查浏览器是否支持该倍速
    const video = videoRef.value;
    const originalRate = video.playbackRate;

    video.playbackRate = newRate;

    // 验证倍速是否真的被设置
    if (Math.abs(video.playbackRate - newRate) < 0.01) {
      playbackRate.value = newRate;
      console.log(`播放倍速设置为: ${newRate}x`);
      ElMessage.success(`播放速度已设置为 ${newRate}x`);
    } else {
      // 如果设置失败，恢复原来的倍速
      video.playbackRate = originalRate;
      console.warn(
        `浏览器不支持 ${newRate}x 倍速，当前倍速: ${video.playbackRate}x`
      );
      ElMessage.warning(`您的浏览器不支持 ${newRate}x 倍速播放`);
    }
  } catch (error) {
    console.error("设置播放倍速失败:", error);
    ElMessage.error("设置播放倍速失败");
  }
};

const handleProgress = () => {
  if (videoRef.value && videoRef.value.buffered.length > 0) {
    const loaded = videoRef.value.buffered.end(
      videoRef.value.buffered.length - 1
    );
    loadedPercent.value =
      duration.value > 0 ? (loaded / duration.value) * 100 : 0;
    emit("progress", loaded, duration.value);
  }
};

// 控制方法
const togglePlay = () => {
  if (!videoRef.value) return;

  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play().catch(err => {
      console.error("播放失败:", err);
      ElMessage.error("播放失败，请检查视频文件");
    });
  }
};

const toggleFullscreen = () => {
  if (!videoRef.value) return;

  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    videoRef.value.requestFullscreen().catch(err => {
      console.error("全屏失败:", err);
      ElMessage.error("全屏功能不可用");
    });
  }
};

const handleProgressClick = (event: MouseEvent) => {
  if (!videoRef.value || duration.value === 0) return;

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const percent = (event.clientX - rect.left) / rect.width;
  videoRef.value.currentTime = percent * duration.value;
};

const retry = () => {
  if (props.src) {
    loadVideo(props.src);
  }
};

// 格式化时间显示
const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
};

// 暴露方法给父组件
defineExpose({
  play: () => videoRef.value?.play(),
  pause: () => videoRef.value?.pause(),
  seek: (time: number) => {
    if (videoRef.value) {
      videoRef.value.currentTime = time;
    }
  },
  setVolume: (volume: number) => {
    if (videoRef.value) {
      videoRef.value.volume = Math.max(0, Math.min(1, volume));
    }
  },
  setPlaybackRate: (rate: number) => {
    changePlaybackRate(rate.toString());
  },
  getCurrentTime: () => currentTime.value,
  getDuration: () => duration.value,
  getPlaybackRate: () => playbackRate.value,
  isPlaying: () => isPlaying.value
});

// 键盘快捷键控制
const handleKeydown = (event: KeyboardEvent) => {
  if (!videoRef.value) return;

  // 防止在输入框中触发快捷键
  const target = event.target as HTMLElement;
  if (
    target.tagName === "INPUT" ||
    target.tagName === "TEXTAREA" ||
    target.contentEditable === "true"
  ) {
    return;
  }

  switch (event.code) {
    case "Space":
      event.preventDefault();
      togglePlay();
      break;
    case "ArrowLeft":
      event.preventDefault();
      isSeeking.value = true; // 标记正在快退
      if (videoRef.value.currentTime > 10) {
        videoRef.value.currentTime -= 10;
      } else {
        videoRef.value.currentTime = 0;
      }
      console.log("快退10秒");
      break;
    case "ArrowRight":
      event.preventDefault();
      isSeeking.value = true; // 标记正在快进
      const maxTime = videoRef.value.duration || 0;
      if (videoRef.value.currentTime + 10 < maxTime) {
        videoRef.value.currentTime += 10;
      } else {
        videoRef.value.currentTime = maxTime;
      }
      console.log("快进10秒");
      break;
  }
};

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
  console.log("键盘快捷键已启用: 空格键播放/暂停，左右方向键快退/快进");
});

// 组件卸载时移除键盘事件监听
onBeforeUnmount(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
.video-player-container {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  display: block;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  z-index: 10;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-icon {
  color: #f56c6c;
  margin-bottom: 16px;
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px;
  color: white;
}

.progress-container {
  margin-bottom: 12px;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.progress-loaded,
.progress-played {
  height: 100%;
  border-radius: 2px;
  position: absolute;
  top: 0;
  left: 0;
}

.progress-loaded {
  background: rgba(255, 255, 255, 0.5);
}

.progress-played {
  background: #409eff;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-controls,
.right-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-display {
  font-size: 14px;
  font-family: monospace;
}
</style>
