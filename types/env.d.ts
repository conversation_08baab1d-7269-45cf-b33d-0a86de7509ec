/// <reference types="vite/client" />

// 解决 __VLS_ 相关类型冲突问题
declare namespace __VLS_IntrinsicElements {
  // 避免与 Volar 的类型定义冲突
}

declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare module "*.svg" {
  const content: any;
  export default content;
}

declare module "*.svg?component" {
  import { type FunctionalComponent, type SVGAttributes } from "vue";
  const component: FunctionalComponent<SVGAttributes>;
  export default component;
}
